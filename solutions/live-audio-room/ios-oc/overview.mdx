---
articleID: 15942
---
# 概述

- - -

## 简介
语聊房是指在线语音连麦虚拟房间，每个房间设有多个麦位，房主在麦上聊天，同时向房间内其他用户推流，其他听众可以进入房间收听。房主也可邀请听众上麦互动，不同房型的麦位数量和房间内最大听众数量不同。

随着音视频直播行业的发展，由于语音可承载的信息密度比文字图片更丰富，使用门槛比视频又更简便，是天然的社交工具，很多产品在社交领域以语聊房的方式做了尝试，比如职场社交脉脉、语音社交鱼耳、娱乐社交唱吧、视频相亲等等，专注于某个特定场景，成为吸引特定群体的深度社交工具。

## 应用场景

在语聊房中，房主和几名发言人以语音的方式在线互动，可能还会有听众（不能发言，只能收听），通过赠送礼物和聊天消息互动。语聊房常见的应用场景有：相亲交友、FM 电台、K 歌语聊、游戏互动、赛事直播、私密影院等。


| 应用场景 | 描述 |
| -- | -- |
| 语音/视频相亲房 | 红娘作为主持人，N 名发言人作为嘉宾，红娘负责控场并带动气氛，抛出话题和游戏推动活动进行。<br />嘉宾在此过程中对彼此加深了解，展示个人魅力，可以对其他嘉宾“爆灯”表示喜欢。 |
| 情感陪护房、语音电台房等在线 FM | 房主单人直播或房主和几名固定陪聊嘉宾，同时播放背景音乐和音效，听众可以赠送礼物上麦，以参与语音互动。 |
| KTV 语聊房 | 一般有一名管理员，大家可以点歌、评论、猜歌、接唱等，主要分为多人连麦和多人轮麦两个模式。<br />多人连麦：一名主唱，连麦用户可以边听边说话，主唱听不到连麦用户的说话声，房内听众则能听到全部声音。<br />多人轮麦：点歌后，一人唱一段，唱完自动轮到下一个人唱，其他用户在等待的时间只能听，只能评论交流，不能语聊。|
| 互动游戏房 | 用户在语聊房内进行狼人杀、剧本杀、pia 戏、真心话大冒险、你画我猜等游戏，该场景下会按照游戏流程创建房间，根据游戏进度业务上控制说话的玩家权限按顺序发言。 |
| 赛事直播房 | 语聊房内存在主持人和赛事转播的音视频，房间内听众根据制定的业务逻辑和主持人连麦共同讨论比赛，如咪咕直播，通过即构的能力打造云导播台，将 CCTV5 的直播视频流和平台的主播解说融合，打造和网友一起看比赛的体验。|
| 私密影院房 | 房主和几名发言人一起看电影看剧，在房内边看边吐槽。 |

## 方案优势

### 语聊的高音质低延时提供稳定的基础体验
- 弱网抗性保证体验的稳定性；
- 优秀自研引擎保证音质；
- 全球节点保证低延时；
- 夜间高峰时段的高并发稳定可用；
- 消息优先级设置。
### 语聊房最佳实践提供更全面的安全保障
- 鉴权功能和防炸麦方案；
- 快速切换房间；
- 内容审核一站式方案；
- 连麦的同步性和解决方案高可用性保障。
### 丰富的语聊房附加玩法带来更多的增值功能
- 更多变声变调混响等效果，丰富玩法和趣味性；
- 双声道效果和音频前处理/外部采集；
- 播放音效/BGM；
- 音频频谱声浪。

## 功能列表

<table>
  
<tbody><tr>
<th>主要功能</th>
<th>功能描述</th>
</tr>
<tr>
<td>登录房间</td>
<td>房主和听众登录到房间后可进行推拉流等功能。</td>
</tr>
<tr>
<td>推流</td>
<td>推送自己的音频流，主要是麦上用户推送自己的声音媒体数据。</td>
</tr>
<tr>
<td>拉流</td>
<td>播放音频流，主要是听众拉取麦上用户的声音媒体数据。</td>
</tr>
<tr>
<td>房间内 IM 功能</td>
<td>发送和接收消息，主要是听众通过发送文字消息参与互动。</td>
</tr>
<tr>
<td>混流</td>
<td>房主可以发起混流，即把多路音频流混合成单流。推流后混流，听众在拉流时只需要拉一路流即可收听麦上用户的互动音频，降低开发实现上的复杂性以及对设备的性能要求。</td>
</tr>
<tr>
<td>安全审核</td>
<td>支持对文本和图片消息的安全审核。</td>
</tr>
<tr>
<td>麦位管理</td>
<td>房主可以对房间内的麦位进行管理，包括听众上下麦，禁麦等操作。</td>
</tr>
<tr>
<td>消息优先级</td>
<td>支持设置消息优先级，保证高优先级消息传输。</td>
</tr>
<tr>
<td>呼叫邀请</td>
<td>支持房主向听众发送呼叫邀请，听众接受或拒绝邀请。</td>
</tr>
<tr>
<td>登录状态监控</td>
<td>支持在登录状态下，使用定时心跳机制监控在线情况，并实时更新登录状态。</td>
</tr>
</tbody></table>

<Warning title="注意">
开发者如果想使用 ZEGO Express SDK 实现进阶音频功能，可参考 [自定义音频采集](!ExpressVideoSDK-AudioAdvanced/custom_audio_io)、[音频频谱与声浪](!ExpressVideoSDK-AudioAdvanced/SoundLevelSpectrum)、[变声/混响/立体声](!ExpressVideoSDK-AudioAdvanced/AudioEffects) 等文档。
</Warning>
