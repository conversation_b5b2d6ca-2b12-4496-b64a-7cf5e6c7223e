---
articleID: 11170
---
# 概述
---

## 场景描述

在线音乐教学，可以让专业音乐教师在线对学生进行音乐教学辅导或陪练，以一对一或一对多的乐器教学与陪练为主。ZEGO 在线教育解决方案中，针对音乐教学场景，支持设置高达 48 kHz 采样率、192 kbps 码率的音频，高度还原乐器弹奏音质，同时提供上传乐谱文件、画笔标注、双摄像头画面等功能，提升线上音乐教学的互动体验。
<Frame width="512" height="auto" caption="">
  <img src="https://storage.zego.im/sdk-doc/Pics/solution_edu/music.png" />
</Frame>


## 功能列表

| 功能 | 描述 |
| --- | ---- |
|设备和网络检测|课程开始前，教师端与学生端可各自进行设备与网络检测，以确保授课过程中的音视频互动效果与稳定性。|
|实时音视频|教师单向授课，学生实时接收教师的音视频进行观看；学生互动连麦，其他学生实时接收教师和连麦学生的音视频。|
|互动白板|教师可通过白板工具进行教学涂鸦等，学生也可通过涂鸦工具参与课堂互动。|
|文件共享|支持多种类型的课件，帮助教师提升课堂趣味性。|
|屏幕共享|教师可以通过屏幕共享将自己桌面的内容实时分享给学生观看，提高教学效率。|
|实时消息|教师和学生在课堂中可以实时发送文字消息。|
|课堂管理|教师可以控制课堂的开始或结束。|
|学生管理|教师可以管理学生的音视频和实时消息权限。|
|课堂质量检测|教师和学生可以在课堂中，查看自己和对方的网络质量状况。|
|录制回放|教师可将课堂内容录制下来，方便学生课后进行回顾，以及学校机构评估教学质量。|


## 技术方案

通过以下 SDK 与服务可以搭建出在线音乐教学场景：
<Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Common/GoClass/Large_Classes_Arch_Diagram_ch.png" /></Frame>


## 方案优势

### **高清音质**

- ZEGO 自研的音视频引擎，支持 48 kHz 全频带采样，通过成熟的 3A 技术和智能音频降噪算法，对声音进行回声消除、噪音抑制、音量增益，既能将空调、风扇等发出的噪音消除掉，又能不误伤音乐的原声，更好地还原音乐的细节。
- 为缩小因手机性能不同对音质产生的影响，ZEGO 针对不同型号的手机在算法上进行不断优化，以实现良好音质。

### **高清画质**

视频引擎最高支持全链路 4K 分辨率，搭载多种编码格式，多种上行流控策略有机结合，下行亦能根据网络状况自适应灵活选择大小画面，在 70% 丢包的恶劣连通环境下，仍能保证流畅视觉体验。

### **超低延迟**

ZEGO 基于全球云通讯服务特性，自研“海量有序数据网络”（MSDN）。 MSDN 具备覆盖面广、可用性强、支持超高并发、超低延迟的特性，全球平均端到端延时 300 ms。

### **丰富的功能组件**

- 支持教学白板，提供涂鸦、文本、图形、橡皮、激光笔等多种教具，绘制过程与本人音视频流实时同步。
- 支持文件共享，可共享多类型格式文件，支持动态 PPT、H5 课件演示与互动。
- 支持屏幕共享、媒体文件播放等能力，满足多样化教学方式。

### **多种录制方式**

支持课程录制，提供 CDN 录制、云录制、数据流录制等多种录制方式。


### **课堂质量可回溯**

基于 ZEGO 为开发者提供的星图平台，可对课程进行全链路的质量监控及故障定位，上课过程可追溯。


### **全平台适配**

- 支持 Windows、macOS、Web、iOS、Android、Electron 等多种平台和开发架构。
- 深度兼容 15000+ 种设备型号，保障全球师生体验的一致性。
