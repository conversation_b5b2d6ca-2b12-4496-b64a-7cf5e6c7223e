---
articleID: 6365
---



# 离开课堂
---
## 描述

调用离开课堂接口，可以退出当前课堂，房间内教师和所有学生端都可以离开课堂。

调用频率限制：10 次/秒

<Note title="说明">


当所有成员都离开课堂 15 分钟以后，教室会自动销毁，用户可以在教室销毁前再次登录课堂。
</Note>



## 接口原型

* 请求方法：`POST`
* 请求地址：`/edu_room/leave_room`
* 传输协议：`application/json`



## 请求参数

| 参数    | 类型   | 是否必选 | 示例      | 描述       |
| ------- | ------ | -------- | --------- | ---------- |
| room_id | String | 是       | "123456"  | 教室房间 ID，只能包含数字，最长9个字符。 |
| uid     | Int64  | 是       | 171171717 | 用户 ID。     |
| room_type | Int32  | 否	| 2	    | 房间类型，取值如下： <ul><li>1：小班课</li><li>2：大班课</li></ul> 不传默认为小班课。 |


## 请求示例

```json
{
  "uid":171171717,
  "room_id":"123456",
  "room_type": 2
}
```



## 响应参数

无。



## 响应示例

```json
{
  "ret": {
    "code": 0,
    "message": "succeed"
  }
}
```



## 返回码

| 返回码 | 描述           |
| ------ | -------------- |
| 10005  | 需要先登录教室。 |
| 10006  | 教室不存在。     |
