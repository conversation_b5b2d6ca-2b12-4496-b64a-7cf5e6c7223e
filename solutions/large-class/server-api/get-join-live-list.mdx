---
articleID: 6361
---



# 获取连麦成员列表
---
## 描述

拉取教室内连麦中的成员信息，包括用户角色、用户 ID、用户昵称、用户摄像头和麦克风状态、用户权限等。

调用频率限制：10 次/秒


## 接口原型

* 请求方法：`POST`
* 请求地址：`/edu_room/get_join_live_list`
* 传输协议：`application/json`



## 请求参数

| 参数    | 类型   | 是否必选 | 示例      | 描述       |
| ------- | ------ | -------- | --------- | ---------- |
| room_id | String | 是       | "123456"  | 教室房间 ID，只能包含数字，最长 9 个字符。 |
| uid     | Int64  | 是       | 171171717 | 用户ID。    |
| room_type | Int32  | 否	| 2	    | 房间类型，取值如下： <ul><li>1：小班课</li><li>2：大班课</li></ul>  不传默认为小班课。 |



## 请求示例

```json
{
  "uid":171171717,
  "room_id":"123456",
  "room_type": 2
}
```



## 响应参数

| 参数                          | 类型   | 示例          | 描述                                                         |
| ----------------------------- | ------ | ------------- | ------------------------------------------------------------ |
| room_id                       | String | "123456"      | 教室房间 ID。                                                   |
| seq                           | Int64  | 1             | 连麦成员列表序列号，客户端需要缓存在本地，用来检测是否有数据同步丢失。 |
| join_live_list                |   Object    |          -     | 连麦成员列表。                                                 |


attendee_list 数据结构如下：

| 参数                          | 类型   | 示例          | 描述                                                         |
| ----------------------------- | ------ | ------------- | ------------------------------------------------------------ |
| join_live_list.uid            | Int64  | 171171717     | 用户 ID。                                                     |
| join_live_list.nick_name      | String | "Shawn"       | 用户昵称。                                                     |
| join_live_list.role           | Int32  | 1             | 用户角色，取值如下： <ul><li>1：老师</li><li>2：学生</li></ul>                     |
| join_live_list.camera         | Int32  | 1             | 用户摄像头状态，取值如下： <ul><li>1：关闭</li><li>2：打开</li></ul>                    |
| join_live_list.mic            | Int32  | 1             | 用户麦克风状态，取值如下： <ul><li>1：关闭</li><li>2：打开</li></ul>                              |
| join_live_list.can_share      | Int32  | 2             | 用户共享权限状态，取值如下： <ul><li>1：关闭</li><li>2：打开</li></ul>                 |
| join_live_list.login_time     | Int64  | 1600928701534 | 用户登录教室时间，为 Unix 时间戳，单位：毫秒。                                         |
| join_live_list.join_live_time | Int64  | 1600928726313 | 用户连麦时间，为 Unix 时间戳，单位：毫秒。 

## 响应示例

```json
{
  "ret": {
    "code": 0,
    "message": "succeed"
  },
  "data": {
    "join_live_list": [
      {
        "uid": 171171717,
        "nick_name": "Shawn",
        "role": 1,
        "login_time": 1600928701534,
        "join_live_time": 1600928726313,
        "camera": 2,
        "mic": 2,
        "can_share": 2
      }
    ],
    "seq": 1,
    "room_id": "123456"
  }
}
```



## 返回码

| 返回码 | 描述           |
| ------ | -------------- |
| 10005  | 需要先登录房间。 |
| 10006  | 房间不存在。     |
