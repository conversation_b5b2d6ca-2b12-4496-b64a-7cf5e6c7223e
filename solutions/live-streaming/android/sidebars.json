{"mySidebar": [{"type": "doc", "label": "概述", "id": "overview", "articleID": 16274}, {"type": "doc", "label": "体验 App", "id": "download-demo", "articleID": 16278}, {"type": "category", "label": "实现直播", "collapsed": false, "items": [{"type": "category", "label": "CDN 直播", "collapsed": false, "items": [{"type": "doc", "label": "快速开始", "id": "implement/cdn-live-streaming/quick-start", "articleID": 20720}, {"type": "doc", "label": "推流至 CDN", "id": "implement/cdn-live-streaming/stream-to-cdn", "articleID": 20735}, {"type": "doc", "label": "从 CDN 拉流", "id": "implement/cdn-live-streaming/pull-stream-from-cdn", "articleID": 20737}, {"type": "doc", "label": "CDN 推流鉴权", "id": "implement/cdn-live-streaming/cdn-stream-publishing-authentication", "articleID": 20765}]}, {"type": "category", "label": "超低延迟直播", "collapsed": false, "items": [{"type": "doc", "label": "快速开始", "id": "implement/l3-live-streaming/quick-start", "articleID": 20741}]}]}, {"type": "doc", "label": "实现连麦", "id": "implement-cohosting", "articleID": 20743}, {"type": "doc", "label": "实现 PK", "id": "implement-pk", "articleID": 20746}, {"type": "category", "label": "直播进阶配置", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "advanced/use-token", "articleID": 20756}, {"type": "doc", "label": "通用直播配置", "id": "advanced/general-config", "articleID": 20753}, {"type": "doc", "label": "场景化音视频配置", "id": "advanced/scenario-config", "articleID": 20758}]}, {"type": "category", "label": "API 文档", "collapsed": false, "items": [{"type": "link", "label": "Express", "href": "https://doc-zh.zego.im/article/3548", "articleID": 20102}, {"type": "link", "label": "ZIM", "href": "https://doc-zh.zego.im/article/11850", "articleID": 20104}]}, {"type": "category", "label": "常见错误码", "collapsed": false, "items": [{"type": "link", "label": "Express 错误码", "href": "/real-time-video-android-java/client-sdk/error-code", "articleID": 15958}, {"type": "link", "label": "ZIM 错误码", "href": "/zim-android/sdk-error-codes/zim", "articleID": 15961}]}]}