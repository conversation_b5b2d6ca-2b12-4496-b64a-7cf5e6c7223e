# 下载

- - -

ZEGO 实时音视频（Express Video）SDK 由深圳市即构科技有限公司提供，您可以在本页面获取适用于 React Native 开发框架、且**含有版权音乐功能**的 Express-Video SDK，当前可下载版本为 3.19.0，发布日志请参考 [发布日志](./introduction/release-notes.mdx)，合规事宜请参考 [ZEGO 安全合规白皮书](https://doc-zh.zego.im/policies-and-agreements/zego-security-and-compliance-white-paper)。

| 资源 | 下载地址 | 相关文档|
| ---- | ----| ------ |
| SDK | [Express SDK v3.19.0（含版权音乐功能）](https://www.npmjs.com/package/zego-express-engine-reactnative/v/3.19.0-copyrightedmusic.15?activeTab=versions) | [集成 SDK - 实时音视频](./quick-starts/integrate-the-sdk/express-video.mdx) |

<Note title="说明">

- 版权音乐功能需要联系 ZEGO 技术支持单独开通，如未开通该功能，调用相关接口可能会报错。
- 本页面提供的 SDK 是基于 Express SDK。
    - 通过版本号，您可以了解本页面提供的 SDK 所对应的 Express SDK 版本。
    - 如需了解 Express SDK 版本变更详情，请参考 [实时音视频 - 发布日志](/real-time-video-android-java/client-sdk/release-notes)。
    - 如需了解版权音乐功能的更新详情，请参考 [版权音乐功能重要更新（SDK）](./introduction/release-notes.mdx)。
- 如已集成过 Express SDK，需要删除旧包并重新集成本页面提供的包，避免 SDK 版本不匹配造成初始化失败。
</Note>
