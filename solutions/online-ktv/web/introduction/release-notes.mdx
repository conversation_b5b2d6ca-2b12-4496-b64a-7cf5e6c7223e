# 发布日志

- - -

## 版权音乐功能重要更新（SDK）

<Warning title="注意">

请注意，本页面**仅更新与版权音乐 KTV 相关的发布日志**，如果您想要了解 Express SDK 的其它变更，请参考 [实时音视频 - 发布日志](/real-time-video-web/client-sdk/release-notes)。
</Warning>

### 3.7.1 版本

**发布日期：2024-10-30** 

<h5>修复问题</h5>

**1. 修复版权音乐下载歌曲报错的问题**

---

### 3.6.0 版本

**发布日期：2024-08-23** 

<h5>新增功能</h5>

| 功能项 | 功能描述 | 相关接口 |
|--------|----------|----------|
| 新增计费模式 | 版权音乐新增包月计费模式，详情请联系 ZEGO 商务人员咨询。 | - |

### 3.0.0 版本

**发布日期：2023-10-19** 

<h5>新增功能</h5>

| 功能项 | 功能描述 | 相关接口 |
|--------|----------|----------|
| 部分版权方支持获取逐行歌词 | 部分版权方支持获取逐行歌词。**版权方的详细信息，请联系 ZEGO 商务人员咨询。** | [getLrcLyric](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoCopyrightedMusic#get-lrc-lyric) |
| 支持实时合唱、以及混流精准对齐 | 支持实时合唱，且在合唱场景中，可以基于 ZEGO 服务器的 NTP 时间，在混流时自动对齐各路流的播放时间。 | <ul><li>[startMixerTask](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#start-mixer-task)</li><li>[setStreamAlignmentProperty](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#set-stream-alignment-property)</li><li>[startPublishingStream](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#start-publishing-stream)</li><li>[getNetworkTimeInfo](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#get-network-time-info)</li></ul> |
| 新增逐行歌词打分完成回调接口 | 调用 [startScore](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoCopyrightedMusic#start-score) 接口开始评分后，开发者可以通过 [songLineScoreComplete](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~interface~ZegoCopyrightedMusicEvent#song-line-score-complete) 回调接口，获取每行歌词的演唱评分。 | <ul><li>[songLineScoreComplete](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~interface~ZegoCopyrightedMusicEvent#song-line-score-complete)</li><li>[startScore](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoCopyrightedMusic#start-score)</li></ul> |
| 新增下载歌曲资源进度回调接口 | 调用 [download](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoCopyrightedMusic#download) 接口开始下载歌曲资源后，开发者可以通过 [downloadProgressUpdate](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~interface~ZegoCopyrightedMusicEvent#download-progress-update) 回调接口，获取资源的下载进度。 | <ul><li>[downloadProgressUpdate](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~interface~ZegoCopyrightedMusicEvent#download-progress-update)</li><li>[download](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoCopyrightedMusic#download)</li></ul> |
| 新增歌曲音质增强功能 | 新增音频播放器音质增强功能，提升伴奏的音质以及现场的氛围感。该功能需要额外集成 VoiceChanger 模块，请在您项目目录下的 "index.js" 文件，添加如下代码：`import {VoiceChanger} from "zego-express-engine-webrtc/voice-changer";` | [enableLiveAudioEffect](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#enable-live-audio-effect) |

### 2.26.0 版本

**发布日期：2023-08-18** 

<h5>新增功能</h5>

| 功能项 | 功能描述 | 相关接口 |
|--------|----------|----------|
| 版权内容中心新增音乐版权方歌曲资源 | 支持在相关接口的参数 vendorID 中传入不同的版权方取值，进行点歌、获取歌词等操作。**版权方的详细信息，请联系 ZEGO 商务人员咨询。** | <ul><li>[requestResource](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoCopyrightedMusic#request-resource)</li><li>[getSharedResource](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoCopyrightedMusic#get-shared-resource)</li></ul> |
| 支持区分场景，获取不同歌曲资源 | 支持在 [requestResource](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoCopyrightedMusic#request-resource) 接口传入不同的场景 ID，获取不同的音乐资源，应用于直播、语聊等场景中。 | [requestResource](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoCopyrightedMusic#request-resource) |
| 扩展歌曲打分能力支持范围 | 支持多个版权方歌曲的打分、展示音高线的能力，详细信息，请联系 ZEGO 商务人员咨询。 | [发送扩展请求接口说明](https://doc-zh.zego.im/article/api?doc=copyright_music_agreement/copyright_music_agreement) |
| 支持歌曲聚合搜索 | 支持通过输入歌手、歌名等关键字，同时在多家版权方曲库中搜索查询，支持对搜索结果筛选、排序、自定义展示。 | <ul><li>[sendExtendedRequest](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoCopyrightedMusic#send-extended-request)</li><li>[发送扩展请求接口说明](https://doc-zh.zego.im/article/api?doc=copyright_music_agreement/copyright_music_agreement)</li><li>[搜索多版权方的歌曲](https://doc-zh.zego.im/article/api?doc=KTV_Live_Chorus_Program_down-muti_VendorID)</li></ul> |

<h5>改进优化</h5>

| 优化项 | 优化描述 | 相关接口 |
|--------|----------|----------|
| 优化独唱效果 | 优化 Web 端独唱效果，提高人声与伴奏的一致性，降低延迟。 | - |

---


### 2.24.5 版本

**发布日期：2023-04-28** 

首次发布，支持 KTV 点歌、版权内容中心曲库等能力，可应用于独唱场景中。