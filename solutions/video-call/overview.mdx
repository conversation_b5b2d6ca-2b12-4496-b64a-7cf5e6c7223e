---
articleID: 3288
---
# 概述
---

## 场景介绍
实时视频场景方案是指同一房间内的成员，进行一对一或多人实时音视频通话。支持实时音视频通话、音视频设备管理、分辨率设置、实时网络数据、实时消息互动、美颜美型等功能，常见应用场景有一对一视频通话、多人视频会议、闪聊、在线问诊、在线教育等。

<Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/GoCall/call/GoCall_IM.jpg" /></Frame>

## 功能列表

| 主要功能 | 功能描述 |
|-------|--------|
| 登录房间 | 支持用户创建房间或加入已存在的房间进行音视频通话。|
| 音视频通话 | 支持一对一或多人间的语音/视频通话功能。|
| 设备管理 | 支持开启或关闭麦克风、摄像头，切换前后置摄像头等管理方法。|
| 视频设置 | 支持设置视频分辨率、帧率、码率等视频配置。|
| 音频设置 | 支持设置通话音质、3A 音频处理等音频属性和参数。|
| 视图切换 | 支持设置宫格视图或演讲者视图。支持自动切换视频视图和音频视图。|
| 实时质量数据 |<ul><li> 通话前，支持麦克风、摄像头、扬声器等音视频设备检测。</li><li>通话中，用户可以查看当前的网络质量数据，音视频质量数据透明可溯。</li><li>通话后，提供音视频质量分析平台“星图”和质量体验报告，保障高质量的直播效果。</li></ul> |
| 实时消息互动 | 通过 ZEGO 即时通讯服务，实时展示房间内的消息，例如发消息、进退房提示、互动通知等。|
| 美颜美型 | 基于 AI 美颜服务，提供美白、磨皮、锐化、红润等基础的美颜功能，支持大眼、瘦脸、小嘴、亮眼、白牙、瘦鼻等美型效果，打造独特自然的直播效果。|

<Note title="提示">



开发者如果想使用 Express-Video SDK 实现更高级的功能，例如混音、音频录制、外部采集等，**请参考 Express-Video SDK 的视频进阶、音频进阶功能**。
</Note>


## 方案优势

### 功能齐全，玩法丰富

ZEGO SDK 提供丰富的直播功能，开发者可以根据自己的业务需求，进行美颜、美型、滤镜、混响、变声、背景音乐等个性化功能集成。

### 音画质高清呈现

支持全频带采样，业界领先的 3A 处理过程，对音质进行高质量的回声消除、噪声抑制与音量增益控制，搭配多种编码格式，保障清晰生动的画质呈现，提供良好的直播体验。

### 质量透明可溯

ZEGO SDK 提供质量回调 API，开发者可通过注册相关的回调，实时监听直播过程中推拉流质量情况，快速定位问题，保证良好的直播体验。

### 全球覆盖，稳定流畅

网络节点覆盖全球 200+ 国家和地区，总计 500+ 核心节点，确保了用户在全球各地稳定、流畅的观看体验。
