---
articleID: 3320
---
# 概述
- - - 

## 场景介绍
实时语音场景的典型使用案例是同一房间内的成员，进行**实时语音对话**。

Zego 提供了对实时语音场景的支持，建议开发者按照[快速开始-集成](https://doc-zh.zego.im/article/3574)，逐渐熟悉并掌握**实时语音技术的使用与开发**。

## 功能列表
| 主要功能 | 功能描述 |
|-------|--------|
| 登录房间 | 用户需要登录房间，才可进行后续的推拉流。|
| 推流 | 用户可以将自己的音频推送出去。|
| 拉流 | 用户可以拉取其他用户的音频。|
| 媒体播放器 | 用户可以本地播放背景音乐，或同时将背景音乐推送出去。|
| 变声与混响 | 提供多种预设的变声和混响效果，同时支持灵活调整音调、混响参数等效果。|
| 双声道 | 支持设置双声道。|

> 请注意，开发者如果想使用 Express Audio SDK 实现更高级的功能，例如混音、音频录制等，**请直接参考 Express Audio SDK 的进阶功能**。
