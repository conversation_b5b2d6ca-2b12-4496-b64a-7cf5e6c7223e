{"mySidebar": [{"type": "doc", "label": "概述", "id": "overview", "articleID": 3320}, {"type": "doc", "label": "体验 App", "id": "download-demo", "articleID": 3533}, {"type": "category", "label": "跑通示例源码", "collapsed": false, "items": [{"type": "doc", "label": "Android", "id": "run-example-code/android", "articleID": 4094}, {"type": "doc", "label": "iOS", "id": "run-example-code/ios", "articleID": 4092}, {"type": "doc", "label": "Windows", "id": "run-example-code/windows", "articleID": 4667}, {"type": "doc", "label": "macOS", "id": "run-example-code/mac-os", "articleID": 4668}, {"type": "doc", "label": "Web", "id": "run-example-code/web", "articleID": 4669}, {"type": "doc", "label": "小程序", "id": "run-example-code/we-chat-mini-program", "articleID": 4670}]}, {"type": "category", "label": "SDK 集成指引", "collapsed": false, "items": [{"type": "doc", "label": "Android", "id": "sdk/android", "articleID": 3993}, {"type": "doc", "label": "iOS", "id": "sdk/ios", "articleID": 3992}, {"type": "doc", "label": "Windows", "id": "sdk/windows", "articleID": 5205}]}, {"type": "category", "label": "实现流程", "collapsed": false, "items": [{"type": "doc", "label": "Android", "id": "impletent/android", "articleID": 4001}, {"type": "doc", "label": "iOS", "id": "impletent/ios", "articleID": 3999}, {"type": "doc", "label": "Windows", "id": "impletent/windows", "articleID": 5047}]}, {"type": "category", "label": "API 文档", "collapsed": false, "items": [{"type": "link", "label": "Android", "href": "https://doc-zh.zego.im/article/3548", "articleID": 4007}, {"type": "link", "label": "iOS", "href": "https://doc-zh.zego.im/article/4950", "articleID": 4009}, {"type": "link", "label": "Windows", "href": "https://doc-zh.zego.im/article/4951", "articleID": 5207}]}, {"type": "link", "label": "常见错误码", "href": "/real-time-video-ios-oc/client-sdk/error-code", "articleID": 4004}]}