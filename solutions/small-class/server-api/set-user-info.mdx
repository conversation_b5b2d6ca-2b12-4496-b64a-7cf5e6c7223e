---
articleID: 5908
---


# 修改某个用户属性
---
## 描述

教师修改教室内自己或者其他人的摄像头和麦克风状态以及共享权限状态。

调用频率限制：10 次/秒

<Warning title="注意">


只有教师有资格修改其他成员的属性。
</Warning>



## 接口原型

* 请求方法：`POST`
* 请求地址：`/edu_room/set_user_info`
* 传输协议：`application/json`



## 请求参数

| 参数       | 类型   | 是否必选 | 示例      | 描述                                          |
| ---------- | ------ | -------- | --------- | --------------------------------------------- |
| room_id    | String | 是       | "123456"  | 教室房间 ID。                                    |
| uid        | Int64  | 是       | 171171717 | 用户 ID。                                        |
| target_uid | Int64  | 是       | 171171717 | 目标用户 ID，当与用户 ID 一致时，表示设置本人的数据。 |
| camera     | Int32  | 否       | 1         | 摄像头状态，取值如下：<ul><li>1：关闭</li><li>2：打开</li></ul>                  |
| mic        | Int32  | 否       | 1         | 麦克风状态，取值如下：<ul><li>1：关闭</li><li>2：打开</li></ul>                  |
| can_share  | Int32  | 否       | 1         | 共享权限状态，取值如下：<ul><li>1：关闭</li><li>2：打开</li></ul>                |



## 请求示例

```json
{
  "uid":171171717,
  "room_id":"123456",
  "target_uid":171171717,
  "mic":2,
  "camera":2,
  "can_share":2
}
```



## 响应参数

无



## 响应示例

```json
{
  "ret": {
    "code": 0,
    "message": "succeed"
  }
}
```



## 返回码

| 返回码 | 描述                 |
| ------ | -------------------- |
| 10003  | 没有权限修改他人信息。 |
| 10004  | 目标用户不在教室。     |
| 10005  | 需要先登录教室。       |
| 10007  | 连麦人数已满。         |
