---
articleID: 5907
---



# 获取某个用户属性
---
## 描述

拉取教室内某个用户的属性信息，包括用户角色、用户 uid、用户昵称、用户摄像头和麦克风状态、用户权限等。

调用频率限制：10 次/秒

## 接口原型

* 请求方法：`POST`
* 请求地址：`/edu_room/get_user_info`
* 传输协议：`application/json`



## 请求参数

| 参数       | 类型   | 是否必选 | 示例      | 描述                                          |
| ---------- | ------ | -------- | --------- | --------------------------------------------- |
| room_id    | String | 是       | "123456"  | 教室房间 ID，只能包含数字，最长 9 个字符。                                    |
| uid        | Int64  | 是       | 171171717 | 用户 ID。                                        |
| target_uid | Int64  | 是       | 171171717 | 目标用户 ID，当与用户 ID 一致时，表示拉取本人的数据。 |



## 请求示例

```json
{
  "uid":171171717,
  "room_id":"123456",
  "target_uid":171171717
}
```



## 响应参数

| 参数           | 类型   | 示例          | 描述                              |
| -------------- | ------ | ------------- | --------------------------------- |
| uid            | Int64  | 171171717     | 用户 ID。                           |
| nick_name      | String | "Shawn"       | 用户昵称。                          |
| role           | Int32  | 1             | 用户角色，取值如下：<ul><li>1：老师</li><li>2：学生</li></ul>         |
| camera         | Int32  | 1             | 用户摄像头状态，取值如下：<ul><li>1：关闭</li><li>2：打开</li></ul>   |
| mic            | Int32  | 1             | 用户麦克风状态，取值如下：<ul><li>1：关闭</li><li>2：打开</li></ul>   |
| can_share      | Int32  | 2             | 用户共享权限状态，取值如下：<ul><li>1：关闭</li><li>2：打开</li></ul> |
| login_time     | Int64  | 1600929248051 | 用户登录教室时间，为 Unix 时间戳，单位：毫秒。              |
| join_live_time | Int64  | 1600929258537 | 用户连麦时间，为 Unix 时间戳，单位：毫秒。              |



## 响应示例

```json
{
  "ret": {
    "code": 0,
    "message": "succeed"
  },
  "data": {
    "uid": 171171717,
    "nick_name": "Shawn",
    "role": 1,
    "login_time": 1600929248051,
    "join_live_time": 1600929258537,
    "camera": 2,
    "mic": 2,
    "can_share": 2
  }
}
```



## 返回码

| 返回码 | 描述             |
| ------ | ---------------- |
| 10004  | 目标用户不在教室。 |
