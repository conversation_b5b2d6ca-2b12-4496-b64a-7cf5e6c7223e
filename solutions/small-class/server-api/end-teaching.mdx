---
articleID: 5911
---


# 结束教学
---

## 描述

调用结束教学接口，可以销毁教室，只有教师可以结束教学。

调用频率限制：10 次/秒


## 接口原型

* 请求方法：`POST`
* 请求地址：`/edu_room/end_teaching`
* 传输协议：`application/json`



## 请求参数

| 参数    | 类型   | 是否必选 | 示例      | 描述       |
| ------- | ------ | -------- | --------- | ---------- |
| room_id | String | 是       | "123456"  | 教室房间 ID，只能包含数字，最长 9 个字符。 |
| uid     | Int64  | 是       | 171171717 | 用户 ID。     |



## 请求示例

```json
{
  "uid":171171717,
  "room_id":"123456"
}
```



## 响应参数

无



## 响应示例

```json
{
  "ret": {
    "code": 0,
    "message": "succeed"
  }
}
```



## 返回码

| 返回码 | 描述                 |
| ------ | -------------------- |
| 10003  | 用户没有权限结束教学。 |
| 10005  | 需要先登录教室。       |
| 10006  | 教室不存在。           |
