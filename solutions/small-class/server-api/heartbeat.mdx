---
articleID: 5909
---



# 心跳
---
## 描述

登录房间后，需定时调用心跳接口，间隔时长为响应参数 `interval` 取值，否则一段时间后用户会被自动下线。

调用频率限制：10 次/秒



## 接口原型

* 请求方法：`POST`
* 请求地址：`/edu_room/heartbeat`
* 传输协议：`application/json`



## 请求参数

| 参数    | 类型   | 是否必选 | 示例      | 描述       |
| ------- | ------ | -------- | --------- | ---------- |
| room_id | String | 是       | "123456"  | 教室房间 ID，只能包含数字，最长 9 个字符。 |
| uid     | Int64  | 是       | 171171717 | 用户 ID。     |



## 请求示例

```json
{
  "uid":171171717,
  "room_id":"123456"
}
```



## 响应参数

| 参数               | 类型  | 示例 | 描述                                                 |
| ------------------ | ----- | ---- | ---------------------------------------------------- |
| interval           | Int32 | 30   | 心跳间隔时间，单位秒。                                 |
| attendee_list_seq  | Int64 | 1    | 在线成员列表序列号，用于检测是否有成员列表的数据丢失。若与客户端本地缓存值之间的差值大于1，则需要调用 [get_attendee_list](/small-class/server-api/get-attendee-list) 更新在线成员列表。 |
| join_live_list_seq | Int64 | 1    | 连麦成员列表序列号，用户检测是否有连麦列表的数据丢失。若与客户端本地缓存值之间的差值大于1，则需要调用 [get_join_live_list](/small-class/server-api/get-join-live-list) 更新连麦成员列表。 |



## 响应示例

```json
{
  "ret": {
    "code": 0,
    "message": "succeed"
  },
  "data": {
    "interval": 120,
    "attendee_list_seq": 1,
    "join_live_list_seq": 0
  }
}
```



## 返回码

| 返回码 | 描述           |
| ------ | -------------- |
| 10005  | 需要先登录教室。 |
