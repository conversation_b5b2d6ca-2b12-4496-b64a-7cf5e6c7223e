{"mySidebar": [{"type": "doc", "label": "方案简介", "id": "overview", "articleID": 5308}, {"type": "doc", "label": "体验 App", "id": "download-demo", "articleID": 5309}, {"type": "category", "label": "SDK 集成指引", "collapsed": false, "items": [{"type": "doc", "label": "Android", "id": "sdk/android", "articleID": 5310}, {"type": "doc", "label": "iOS", "id": "sdk/ios", "articleID": 5311}, {"type": "doc", "label": "Web", "id": "sdk/web", "articleID": 5319}, {"type": "doc", "label": "Electron", "id": "sdk/electron", "articleID": 8215}]}, {"type": "category", "label": "教师端实现流程", "collapsed": false, "items": [{"type": "doc", "label": "Android", "id": "implement-teacher-side/android", "articleID": 5312}, {"type": "doc", "label": "iOS", "id": "implement-teacher-side/ios", "articleID": 5313}, {"type": "doc", "label": "Web", "id": "implement-teacher-side/web", "articleID": 5320}, {"type": "doc", "label": "Electron", "id": "implement-teacher-side/electron", "articleID": 8213}]}, {"type": "category", "label": "学生端实现流程", "collapsed": false, "items": [{"type": "doc", "label": "Android", "id": "implement-student-side/android", "articleID": 5314}, {"type": "doc", "label": "iOS", "id": "implement-student-side/ios", "articleID": 5315}, {"type": "doc", "label": "Web", "id": "implement-student-side/web", "articleID": 5321}, {"type": "doc", "label": "Electron", "id": "implement-student-side/electron", "articleID": 8214}]}, {"type": "category", "label": "在线课堂示例 demo 后台服务", "collapsed": false, "items": [{"type": "doc", "label": "在线课堂示例 demo 后台服务说明", "id": "server-api/accessing-server-apis", "articleID": 5370}, {"type": "doc", "label": "登录在线课堂示例 demo 教室", "id": "server-api/login-room", "articleID": 5904}, {"type": "doc", "label": "获取在线成员列表", "id": "server-api/get-attendee-list", "articleID": 5905}, {"type": "doc", "label": "获取连麦成员列表", "id": "server-api/get-join-live-list", "articleID": 5906}, {"type": "doc", "label": "获取某个用户属性", "id": "server-api/get-user-info", "articleID": 5907}, {"type": "doc", "label": "修改某个用户属性", "id": "server-api/set-user-info", "articleID": 5908}, {"type": "doc", "label": "心跳", "id": "server-api/heartbeat", "articleID": 5909}, {"type": "doc", "label": "离开课堂", "id": "server-api/leave-room", "articleID": 5910}, {"type": "doc", "label": "结束教学", "id": "server-api/end-teaching", "articleID": 5911}, {"type": "doc", "label": "全局返回码", "id": "server-api/return-codes", "articleID": 5912}]}, {"type": "doc", "label": "常见错误码", "id": "error-codes", "articleID": 5368}]}