{"title": "ZEGO", "favicon": "image/favicon.ico", "themeConfig": {"removeWatermark": true, "colors": {"primaryLight": "#0055FF", "primaryDark": "#266EFF"}, "colorMode": {"defaultMode": "light", "disableSwitch": false, "respectPrefersColorScheme": false}, "navbar": {"title": "", "logo": {"dark": "image/logo_zegocloud_dark.png", "light": "image/logo_zegocloud_light.png"}, "iconRedirectUrl": "https://www.zegocloud.com/docs", "items": [{"type": "default", "label": "<PERSON><PERSON><PERSON>", "href": "https://console.zegocloud.com/"}]}, "navbar.zh": {"title": "", "logo": {"dark": "image/logo_zego_dark.png", "light": "image/logo_zego_light.png"}, "iconRedirectUrl": "https://doc-zh.zego.im", "items": [{"type": "default", "label": "控制台", "href": "https://console.zego.im/"}]}, "footer": {"logo": {"dark": "image/logo_zegocloud_dark.png", "light": "image/logo_zegocloud_light.png"}, "caption": "", "copyright": {"label": "Copyright @2021-2025 ZEGOCLOUD. All Rights Reserved.    Guangdong PSB Filing No. 44030502006028 粤ICP备15113647号", "href": "https://beian.miit.gov.cn/#/Integrated/index"}, "links": [{"title": "Developers", "items": [{"label": "UIKits", "href": "https://www.zegocloud.com/developers/uikits"}, {"label": "SDKs", "href": "https://www.zegocloud.com/developers/sdks"}, {"label": "API Reference", "href": "https://www.zegocloud.com/docs/api-reference"}]}, {"title": "Support", "items": [{"label": "Tutorials", "href": "https://www.zegocloud.com/docs/tutorials"}, {"label": "Tech Support", "href": "https://www.zegocloud.com/tech-support"}, {"label": "FAQ", "href": "https://docs.zegocloud.com/faq?product=all&platform=alll"}]}], "socials": [{"logo": "Discord", "href": "https://discord.gg/EtNRATttyp"}, {"logo": "GitHub", "href": "https://github.com/ZEGOCLOUD"}], "policies": [{"label": "Privacy policy", "href": "https://www.zegocloud.com/privacy-policy"}, {"label": "<PERSON>ie notice", "href": "https://www.zegocloud.com/cookie-policy"}, {"label": "Terms of service", "href": "https://www.zegocloud.com/terms-of-service"}]}, "footer.zh": {"logo": {"dark": "image/logo_zego_dark.png", "light": "image/logo_zego_light.png"}, "caption": "地址:深圳市南山区前海卓越壹号T3写字楼38-39层", "copyright": {"label": "即构科技 版权所有 粤ICP备15113647号 ©2018-2025", "href": "https://beian.miit.gov.cn/#/Integrated/index"}, "links": [{"title": "开发者中心", "items": [{"label": "SDK 中心", "href": "https://doc-zh.zego.im/sdk-download/2968"}, {"label": "API 中心", "href": "https://doc-zh.zego.im/api-center"}]}, {"title": "更多资源", "items": [{"label": "体验 APP", "href": "https://www.zego.im/experienceApp"}, {"label": "常见问题", "href": "https://doc-zh.zego.im/faq"}]}], "socials": [{"logo": "GitHub", "href": "https://github.com/zegoim"}], "policies": [{"label": "云服务协议", "href": "https://doc-zh.zego.im/policies-and-agreements/zego-cloud-service-agreement"}, {"label": "隐私政策", "href": "https://doc-zh.zego.im/policies-and-agreements/zego-privacy-policy"}, {"label": "SDK 政策", "href": "https://doc-zh.zego.im/policies-and-agreements/zego-sdk-privacy-policy"}, {"label": "<PERSON><PERSON>政策", "href": "https://doc-zh.zego.im/policies-and-agreements/zego-cookie-privacy-policy"}, {"label": "廉洁举报", "href": "https://doc-zh.zego.im/policies-and-agreements/zego-integrity-and-compliance-reporting-guide"}, {"label": "安全合规", "href": "https://doc-zh.zego.im/policies-and-agreements/zego-security-and-compliance-white-paper"}]}}, "search": {"algolia": {"appId": "N61JOMLMAK", "apiKey": "********************************", "indexName": "zegocloud"}}, "openapi": {"create-agent-instance": {"specPath": "core_products/aiagent/en/server/api-reference/agent-instance-management/create-agent-instance.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-instance-management"}, "delete-agent-instance": {"specPath": "core_products/aiagent/en/server/api-reference/agent-instance-management/delete-agent-instance.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-instance-management"}, "update-agent-instance": {"specPath": "core_products/aiagent/en/server/api-reference/agent-instance-management/update-agent-instance.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-instance-management"}, "create-digital-human-agent-instance": {"specPath": "core_products/aiagent/en/server/api-reference/agent-instance-management/create-digital-human-agent-instance.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-instance-management"}, "get-agent-instance-msg-list": {"specPath": "core_products/aiagent/en/server/api-reference/agent-instance-control/get-agent-instance-msg-list.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-instance-control"}, "interrupt-agent-instance": {"specPath": "core_products/aiagent/en/server/api-reference/agent-instance-control/interrupt-agent-instance.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-instance-control"}, "query-agent-instance-status": {"specPath": "core_products/aiagent/en/server/api-reference/agent-instance-control/query-agent-instance-status.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-instance-control"}, "reset-agent-instance-msg-list": {"specPath": "core_products/aiagent/en/server/api-reference/agent-instance-control/reset-agent-instance-msg-list.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-instance-control"}, "send-agent-instance-llm": {"specPath": "core_products/aiagent/en/server/api-reference/agent-instance-control/send-agent-instance-llm.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-instance-control"}, "send-agent-instance-tts": {"specPath": "core_products/aiagent/en/server/api-reference/agent-instance-control/send-agent-instance-tts.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-instance-control"}, "list-agents": {"specPath": "core_products/aiagent/en/server/api-reference/agent-configuration-management/list-agents.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-configuration-management"}, "query-agents": {"specPath": "core_products/aiagent/en/server/api-reference/agent-configuration-management/query-agents.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-configuration-management"}, "register-agent": {"specPath": "core_products/aiagent/en/server/api-reference/agent-configuration-management/register-agent.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-configuration-management"}, "unregister-agent": {"specPath": "core_products/aiagent/en/server/api-reference/agent-configuration-management/unregister-agent.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-configuration-management"}, "update-agent": {"specPath": "core_products/aiagent/en/server/api-reference/agent-configuration-management/update-agent.yaml", "outputDir": "core_products/aiagent/en/server/api-reference/agent-configuration-management"}}, "instances": [{"id": "real_time_video_ios_oc_zh", "path": "core_products/real-time-voice-video/zh/ios-oc", "routeBasePath": "real-time-video-ios-oc", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "iOS: Objective-C"}}, {"id": "real_time_video_ios_swift_zh", "path": "core_products/real-time-voice-video/zh/ios-swift", "routeBasePath": "real-time-video-ios-swift", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "iOS: Swift"}}, {"id": "real_time_video_android_java_zh", "path": "core_products/real-time-voice-video/zh/android-java", "routeBasePath": "real-time-video-android-java", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Android: Java"}}, {"id": "real_time_video_android_kotlin_zh", "path": "core_products/real-time-voice-video/zh/android-kotlin", "routeBasePath": "real-time-video-android-kotlin", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Android: <PERSON><PERSON><PERSON>"}}, {"id": "real_time_video_macos_oc_zh", "path": "core_products/real-time-voice-video/zh/macos-oc", "routeBasePath": "real-time-video-macos-oc", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "macOS: Objective-C"}}, {"id": "real_time_video_macos_swift_zh", "path": "core_products/real-time-voice-video/zh/macos-swift", "routeBasePath": "real-time-video-macos-swift", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "macOS: Swift"}}, {"id": "real_time_video_macos_cpp_zh", "path": "core_products/real-time-voice-video/zh/macos-cpp", "routeBasePath": "real-time-video-macos-cpp", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "macOS: C++"}}, {"id": "real_time_video_windows_cpp_zh", "path": "core_products/real-time-voice-video/zh/windows-cpp", "routeBasePath": "real-time-video-windows-cpp", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Windows: C++"}}, {"id": "real_time_video_windows_cs_zh", "path": "core_products/real-time-voice-video/zh/windows-cs", "routeBasePath": "real-time-video-windows-cs", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Windows: C#"}}, {"id": "real_time_video_harmonyos_arkts_zh", "path": "core_products/real-time-voice-video/zh/harmony-arkts", "routeBasePath": "real-time-video-harmonyos-ark<PERSON>", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "HarmonyOS: ArkTS"}}, {"id": "real_time_video_linux_cpp_zh", "path": "core_products/real-time-voice-video/zh/linux-cpp", "routeBasePath": "real-time-video-linux-cpp", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Linux: C++"}}, {"id": "real_time_video_linux_java_zh", "path": "core_products/real-time-voice-video/zh/linux-java", "routeBasePath": "real-time-video-linux-java", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Linux: Java"}}, {"id": "real_time_video_web_zh", "path": "core_products/real-time-voice-video/zh/web", "routeBasePath": "real-time-video-web", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Web: JS"}}, {"id": "real_time_video_miniprogram_zh", "path": "core_products/real-time-voice-video/zh/mini-program", "routeBasePath": "real-time-video-miniprogram", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "小程序: JS"}}, {"id": "real_time_video_flutter_dart_zh", "path": "core_products/real-time-voice-video/zh/flutter-dart", "routeBasePath": "real-time-video-flutter", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "real_time_video_electron_js_zh", "path": "core_products/real-time-voice-video/zh/electron-js", "routeBasePath": "real-time-video-electron-js", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Electron: JS"}}, {"id": "real_time_video_ue_cpp_zh", "path": "core_products/real-time-voice-video/zh/ue-cpp", "routeBasePath": "real-time-video-ue-cpp", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Unreal Engine: C++"}}, {"id": "real_time_video_u3d_cs_zh", "path": "core_products/real-time-voice-video/zh/u3d-cs", "routeBasePath": "real-time-video-u3d-cs", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Unity3D: C#"}}, {"id": "real_time_video_uniapp_zh", "path": "core_products/real-time-voice-video/zh/uni-app", "routeBasePath": "real-time-video-uniapp", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "uni-app: JS"}}, {"id": "real_time_video_rn_zh", "path": "core_products/real-time-voice-video/zh/rn-js", "routeBasePath": "real-time-video-rn", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "React Native: JS"}}, {"id": "real_time_video_cocos_creator_zh", "path": "core_products/real-time-voice-video/zh/cocos-creator-ts", "routeBasePath": "real-time-video-cocos-creator", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Cocos Creator: TS"}}, {"id": "real_time_voice_video_api_zh", "label": "实时音视频 (客户端 API)", "path": "core_products/real-time-voice-video/zh/client-api", "routeBasePath": "zh/real-time-voice-video-api", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频"}, "category": ["产品", "互动核心产品"], "tab": "客户端 API", "platform": "All"}}, {"id": "real_time_voice_video_server_zh", "label": "实时音视频 (服务端 API)", "path": "core_products/real-time-voice-video/zh/server", "routeBasePath": "zh/real-time-voice-video-server", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频"}, "category": ["产品", "互动核心产品"], "tab": "服务端 API", "platform": "Server"}}, {"id": "real_time_video_server_zh", "path": "core_products/real-time-voice-video/zh/server", "routeBasePath": "real-time-video-server", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_video_zh", "name": "实时音视频", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "platform": "服务端 API"}}, {"id": "real_time_voice_ios_zh", "path": "core_products/real-time-voice/zh/ios-oc", "routeBasePath": "real-time-voice-ios", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "iOS: Objective-C"}}, {"id": "real_time_voice_ios_swift_zh", "path": "core_products/real-time-voice/zh/ios-swift", "routeBasePath": "real-time-voice-ios-swift", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "iOS: Swift"}}, {"id": "real_time_voice_android_zh", "path": "core_products/real-time-voice/zh/android-java", "routeBasePath": "real-time-voice-android", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Android: Java"}}, {"id": "real_time_voice_android_kotlin_zh", "path": "core_products/real-time-voice/zh/android-kotlin", "routeBasePath": "real-time-voice-android-kotlin", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Android: <PERSON><PERSON><PERSON>"}}, {"id": "real_time_voice_macos_zh", "path": "core_products/real-time-voice/zh/macos-oc", "routeBasePath": "real-time-voice-macos", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "macOS: Objective-C"}}, {"id": "real_time_voice_macos_swift_zh", "path": "core_products/real-time-voice/zh/macos-swift", "routeBasePath": "real-time-voice-macos-swift", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "macOS: Swift"}}, {"id": "real_time_voice_macos_cpp_zh", "path": "core_products/real-time-voice/zh/macos-cpp", "routeBasePath": "real-time-voice-macos-cpp", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "macOS: C++"}}, {"id": "real_time_voice_windows_zh", "path": "core_products/real-time-voice/zh/windows-cpp", "routeBasePath": "real-time-voice-windows", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Windows: C++"}}, {"id": "real_time_voice_harmonyos_zh", "path": "core_products/real-time-voice/zh/harmony-arkts", "routeBasePath": "real-time-voice-harmony<PERSON>", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "HarmonyOS: ArkTS"}}, {"id": "real_time_voice_linux_zh", "path": "core_products/real-time-voice/zh/linux-cpp", "routeBasePath": "real-time-voice-linux", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Linux: C++"}}, {"id": "real_time_voice_web_zh", "path": "core_products/real-time-voice/zh/web", "routeBasePath": "real-time-voice-web", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Web: JS"}}, {"id": "real_time_voice_miniprogram_zh", "path": "core_products/real-time-voice/zh/mini-program", "routeBasePath": "real-time-voice-miniprogram", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "小程序: JS"}}, {"id": "real_time_voice_flutter_zh", "path": "core_products/real-time-voice/zh/flutter-dart", "routeBasePath": "real-time-voice-flutter", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "real_time_voice_electron_zh", "path": "core_products/real-time-voice/zh/electron-js", "routeBasePath": "real-time-voice-electron", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Electron: JS"}}, {"id": "real_time_voice_ue_zh", "path": "core_products/real-time-voice/zh/ue-cpp", "routeBasePath": "real-time-voice-ue", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Unreal Engine: C++"}}, {"id": "real_time_voice_u3d_zh", "path": "core_products/real-time-voice/zh/u3d-cs", "routeBasePath": "real-time-voice-u3d", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Unity3D: C#"}}, {"id": "real_time_voice_uniapp_zh", "path": "core_products/real-time-voice/zh/uni-app", "routeBasePath": "real-time-voice-uniapp", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "uni-app: JS"}}, {"id": "real_time_voice_rn_zh", "path": "core_products/real-time-voice/zh/rn-js", "routeBasePath": "real-time-voice-rn", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "React Native: JS"}}, {"id": "real_time_voice_cocos_creator_zh", "path": "core_products/real-time-voice/zh/cocos-creator-ts", "routeBasePath": "real-time-voice-cocos-creator", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Cocos Creator: TS"}}, {"id": "real_time_voice_api_zh", "label": "实时语音 (客户端 API)", "path": "core_products/real-time-voice/zh/client-api", "routeBasePath": "zh/real_time_voice-api", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音"}, "category": ["产品", "互动核心产品"], "tab": "客户端 API", "platform": "All"}}, {"id": "real_time_voice_server_zh", "label": "实时语音 (服务端 API)", "path": "core_products/real-time-voice-video/zh/server", "routeBasePath": "zh/real-time-voice-server", "locale": "zh", "navigationInfo": {"group": {"id": "real_time_voice_zh", "name": "实时语音"}, "category": ["产品", "互动核心产品"], "tab": "服务端 API", "platform": "Server"}}, {"id": "live_streaming_ios_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/ios-oc", "routeBasePath": "live-streaming-ios", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "iOS: Objective-C"}}, {"id": "live_streaming_ios_swift_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/ios-swift", "routeBasePath": "live-streaming-ios-swift", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "iOS: Swift"}}, {"id": "live_streaming_android_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/android-java", "routeBasePath": "live-streaming-android", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Android: Java"}}, {"id": "live_streaming_android_kotlin_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/android-kotlin", "routeBasePath": "live-streaming-android-kotlin", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Android: <PERSON><PERSON><PERSON>"}}, {"id": "live_streaming_macos_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/macos-oc", "routeBasePath": "live-streaming-macos", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "macOS: Objective-C"}}, {"id": "live_streaming_macos_swift_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/macos-swift", "routeBasePath": "live-streaming-macos-swift", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "macOS: Swift"}}, {"id": "live_streaming_macos_cpp_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/macos-cpp", "routeBasePath": "live-streaming-macos-cpp", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "macOS: C++"}}, {"id": "live_streaming_windows_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/windows-cpp", "routeBasePath": "live-streaming-windows", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Windows: C++"}}, {"id": "live_streaming_linux_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/linux-cpp", "routeBasePath": "live-streaming-linux", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Linux: C++"}}, {"id": "live_streaming_web_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/web", "routeBasePath": "live-streaming-web", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Web: JS"}}, {"id": "live_streaming_flutter_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/flutter-dart", "routeBasePath": "live-streaming-flutter", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "live_streaming_electron_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/electron-js", "routeBasePath": "live-streaming-electron", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Electron: JS"}}, {"id": "live_streaming_u3d_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/u3d-cs", "routeBasePath": "live-streaming-u3d", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Unity3D: C#"}}, {"id": "live_streaming_uniapp_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/uniapp", "routeBasePath": "live-streaming-uniapp", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "uni-app: JS"}}, {"id": "live_streaming_rn_zh", "label": "Video Call", "path": "core_products/low-latency-live-streaming/zh/rn-js", "routeBasePath": "live-streaming-rn", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播", "tag": "Hot"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "React Native: JS"}}, {"id": "live_streaming_api_zh", "label": "超低延迟直播 (客户端 API)", "path": "core_products/low-latency-live-streaming/zh/client-api", "routeBasePath": "zh/live_streaming-api", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播"}, "category": ["产品", "互动核心产品"], "tab": "客户端 API", "platform": "All"}}, {"id": "live_streaming_server_zh", "label": "超低延迟直播 (服务端 API)", "path": "core_products/real-time-voice-video/zh/server", "routeBasePath": "zh/live_streaming-server", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_zh", "name": "超低延迟直播"}, "category": ["产品", "互动核心产品"], "tab": "服务端 API", "platform": "Server"}}, {"id": "zim_android_zh", "label": "即时通讯 (Android)", "path": "core_products/zim/zh/docs_zim_android_zh", "routeBasePath": "zh/zim-android", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Android: Java"}}, {"id": "zim_harmonyos_zh", "label": "即时通讯 (HarmonyOS)", "path": "core_products/zim/zh/docs_zim_harmonyos_zh", "routeBasePath": "zh/zim-harmonyos", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "HarmonyOS: ArkTS"}}, {"id": "zim_flutter_zh", "label": "即时通讯 (Flutter)", "path": "core_products/zim/zh/docs_zim_flutter_zh", "routeBasePath": "zh/zim-flutter", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯", "tag": ""}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "zim_ios_zh", "label": "即时通讯 (iOS)", "path": "core_products/zim/zh/docs_zim_ios_zh", "routeBasePath": "zh/zim-ios", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "iOS: Objective-C"}}, {"id": "zim_macos_zh", "label": "即时通讯 (macOS)", "path": "core_products/zim/zh/docs_zim_macos_zh", "routeBasePath": "zh/zim-macos", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "macOS: Objective-C"}}, {"id": "zim_rn_zh", "label": "即时通讯 (RN)", "path": "core_products/zim/zh/docs_zim_rn_zh", "routeBasePath": "zh/zim-rn", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "React Native: TS"}}, {"id": "zim_u3d_zh", "label": "即时通讯 (Unity 3D)", "path": "core_products/zim/zh/docs_zim_u3d_zh", "routeBasePath": "zh/zim-u3d", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Unity3D: C#"}}, {"id": "zim_web_zh", "label": "即时通讯 (Web)", "path": "core_products/zim/zh/docs_zim_web_zh", "routeBasePath": "zh/zim-web", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Web: TS"}}, {"id": "zim_windows_zh", "label": "即时通讯 (Windows)", "path": "core_products/zim/zh/docs_zim_windows_zh", "routeBasePath": "zh/zim-win", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "Windows: C++"}}, {"id": "zim_miniprogram_zh", "label": "即时通讯 (小程序)", "path": "core_products/zim/zh/docs_zim_miniprogram_zh", "routeBasePath": "zh/zim-miniprogram", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "小程序: TS"}}, {"id": "zim_uniapp_x_zh", "label": "即时通讯 (uni-app x)", "path": "core_products/zim/zh/docs_zim_uniapp_x_zh", "routeBasePath": "zh/zim-uniapp-x", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "uni-app x: UTS"}}, {"id": "zim_uniapp_zh", "label": "即时通讯 (uni-app)", "path": "core_products/zim/zh/docs_zim_uniapp_zh", "routeBasePath": "zh/zim-uniapp", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "文档", "platform": "uni-app: TS"}}, {"id": "zim_client_api_zh", "label": "即时通讯 (客户端 API)", "path": "core_products/zim/zh/client-api", "routeBasePath": "zh/zim-client-api", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "客户端 API", "platform": "All"}}, {"id": "zim_server_zh", "label": "即时通讯 (服务端 API)", "path": "core_products/zim/zh/docs_zim_server_zh", "routeBasePath": "zh/zim-server", "locale": "zh", "navigationInfo": {"group": {"id": "zim_zh", "name": "即时通讯"}, "category": ["产品", "互动核心产品"], "tab": "服务端 API", "platform": "Server"}}, {"id": "callkit_android_zh", "label": "音视频通话 UIKit (Android)", "path": "uikit/callkit/docs_callkit_android_zh", "routeBasePath": "callkit-android", "locale": "zh", "navigationInfo": {"group": {"id": "callkit_zh", "name": "音视频通话 UIKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "Android: Java"}}, {"id": "callkit_ios_zh", "label": "音视频通话 UIKit (iOS)", "path": "uikit/callkit/docs_callkit_ios_zh", "routeBasePath": "callkit-ios", "locale": "zh", "navigationInfo": {"group": {"id": "callkit_zh", "name": "音视频通话 UIKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "iOS: Swift"}}, {"id": "callkit_web_zh", "label": "音视频通话 UIKit (Web)", "path": "uikit/callkit/docs_callkit_web_zh", "routeBasePath": "callkit-web", "locale": "zh", "navigationInfo": {"group": {"id": "callkit_zh", "name": "音视频通话 UIKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "Web: JS"}}, {"id": "callkit_miniprogram_zh", "label": "音视频通话 UIKit (小程序)", "path": "uikit/callkit/docs_callkit_miniprogram_zh", "routeBasePath": "callkit-miniprogram", "locale": "zh", "navigationInfo": {"group": {"id": "callkit_zh", "name": "音视频通话 UIKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "小程序: JS"}}, {"id": "callkit_uniapp_zh", "label": "音视频通话 UIKit (uni-app)", "path": "uikit/callkit/docs_callkit_uniapp_zh", "routeBasePath": "callkit-uniapp", "locale": "zh", "navigationInfo": {"group": {"id": "callkit_zh", "name": "音视频通话 UIKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "uni-app: JS"}}, {"id": "live_streaming_kit_android_zh", "label": "互动直播 UIKit (Android)", "path": "uikit/live_streaming_kit/docs_live_streaming_kit_android_zh", "routeBasePath": "live-streaming-kit-android", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_kit_zh", "name": "互动直播 UIKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "Android: Java"}}, {"id": "live_streaming_kit_ios_zh", "label": "互动直播 UIKit (iOS)", "path": "uikit/live_streaming_kit/docs_live_streaming_kit_ios_zh", "routeBasePath": "live-streaming-kit-ios", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_kit_zh", "name": "互动直播 UIKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "iOS: Swift"}}, {"id": "live_streaming_kit_web_zh", "label": "互动直播 UIKit (Web)", "path": "uikit/live_streaming_kit/docs_live_streaming_kit_web_zh", "routeBasePath": "live-streaming-kit-web", "locale": "zh", "navigationInfo": {"group": {"id": "live_streaming_kit_zh", "name": "互动直播 UIKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "Web: JS"}}, {"id": "live_audio_room_kit_android_zh", "label": "语聊房 UIKit (Android)", "path": "uikit/live_audio_room_kit/docs_live_audio_room_kit_android_zh", "routeBasePath": "live-audio-room-kit-android", "locale": "zh", "navigationInfo": {"group": {"id": "live_audio_room_kit_zh", "name": "语聊房 UIKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "Android: Java"}}, {"id": "live_audio_room_kit_ios_zh", "label": "语聊房 UIKit (iOS)", "path": "uikit/live_audio_room_kit/docs_live_audio_room_kit_ios_zh", "routeBasePath": "live-audio-room-kit-ios", "locale": "zh", "navigationInfo": {"group": {"id": "live_audio_room_kit_zh", "name": "语聊房 UIKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "iOS: Swift"}}, {"id": "in_app_chat_kit_android_zh", "label": "IMKit (Android)", "path": "uikit/imkit/docs_in_app_chat_kit_android_zh", "routeBasePath": "in-app-chat-kit-android", "locale": "zh", "navigationInfo": {"group": {"id": "imkit_zh", "name": "IMKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "Android: Java"}}, {"id": "in_app_chat_kit_ios_zh", "label": "IMKit (iOS)", "path": "uikit/imkit/docs_in_app_chat_kit_ios_zh", "routeBasePath": "in-app-chat-kit-ios", "locale": "zh", "navigationInfo": {"group": {"id": "imkit_zh", "name": "IMKit", "tag": "New"}, "category": ["产品", "UIKits"], "platform": "iOS: Swift"}}, {"id": "supper_board_zh", "path": "https://doc-zh.zego.im/article/11288", "routeBasePath": "super-board", "locale": "zh", "navigationInfo": {"group": {"id": "supper_board_zh", "name": "超级白板", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "Android: Java"}}, {"id": "cloud_player_zh", "label": "云端播放器", "path": "extended_services/cloud_player/zh/docs_cloud_player_zh", "routeBasePath": "cloud-player", "locale": "zh", "navigationInfo": {"group": {"id": "cloud_player_zh", "name": "云端播放器", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "文档"}}, {"id": "cloud_player_server_zh", "label": "云端播放器 (Server)", "path": "extended_services/cloud_player/zh/docs_cloud_player_server_zh", "routeBasePath": "cloud-player-server", "locale": "zh", "navigationInfo": {"group": {"id": "cloud_player_zh", "name": "云端播放器", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "服务端 API"}}, {"id": "cloud_recording_zh", "path": "extended_services/cloud_recording/zh/docs_cloud_recording_zh", "routeBasePath": "cloud-recording", "locale": "zh", "navigationInfo": {"group": {"id": "cloud_recording_zh", "name": "云端录制", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "文档"}}, {"id": "ai_effects_ios_objc_zh", "label": "AI 美颜 (iOS)", "path": "extended_services/ai-effects/zh/ios-objc", "routeBasePath": "ai-effects-ios-objc", "locale": "zh", "navigationInfo": {"group": {"id": "ai_effects_zh", "name": "AI 美颜", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "iOS: Objective-C"}}, {"id": "ai_effects_android_java_zh", "label": "AI 美颜 (Android)", "path": "extended_services/ai-effects/zh/android-java", "routeBasePath": "ai-effects-android-java", "locale": "zh", "navigationInfo": {"group": {"id": "ai_effects_zh", "name": "AI 美颜", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "Android: Java"}}, {"id": "ai_effects_macos_objc_zh", "label": "AI 美颜 (macOS Objective-C)", "path": "extended_services/ai-effects/zh/macos-objc", "routeBasePath": "ai-effects-macos-objc", "locale": "zh", "navigationInfo": {"group": {"id": "ai_effects_zh", "name": "AI 美颜", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "macOS: Objective-C"}}, {"id": "ai_effects_macos_c_zh", "label": "AI 美颜 (macOS C)", "path": "extended_services/ai-effects/zh/macos-c", "routeBasePath": "ai-effects-macos-c", "locale": "zh", "navigationInfo": {"group": {"id": "ai_effects_zh", "name": "AI 美颜", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "macOS: C"}}, {"id": "ai_effects_windows_c_zh", "label": "AI 美颜 (Windows C)", "path": "extended_services/ai-effects/zh/windows-c", "routeBasePath": "ai-effects-windows-c", "locale": "zh", "navigationInfo": {"group": {"id": "ai_effects_zh", "name": "AI 美颜", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "Windows: C"}}, {"id": "ai_effects_flutter_dart_zh", "label": "AI 美颜 (<PERSON><PERSON><PERSON> Dart)", "path": "extended_services/ai-effects/zh/flutter-dart", "routeBasePath": "ai-effects-flutter-dart", "locale": "zh", "navigationInfo": {"group": {"id": "ai_effects_zh", "name": "AI 美颜", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "ai_effects_react_native_javascript_zh", "label": "AI 美颜 (React Native)", "path": "extended_services/ai-effects/zh/react-native-javascript", "routeBasePath": "ai-effects-react-native-javascript", "locale": "zh", "navigationInfo": {"group": {"id": "ai_effects_zh", "name": "AI 美颜", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "React Native: JS"}}, {"id": "cloud_recording_server_zh", "path": "extended_services/cloud_recording/zh/docs_cloud_recording_server_zh", "routeBasePath": "cloud-recording-server", "locale": "zh", "navigationInfo": {"group": {"id": "cloud_recording_zh", "name": "云端录制", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "服务端 API"}}, {"id": "local_recording_linux_cpp_zh", "path": "extended_services/local_recording/zh/docs_local_recording_linux_cpp_zh", "routeBasePath": "local-recording-linux-cpp", "locale": "zh", "navigationInfo": {"group": {"id": "local-recording", "name": "本地服务端录制", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "Linux: C++"}}, {"id": "local_recording_linux_java_zh", "path": "extended_services/local_recording/zh/docs_local_recording_linux_java_zh", "routeBasePath": "local-recording-linux-java", "locale": "zh", "navigationInfo": {"group": {"id": "local-recording", "name": "本地服务端录制", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "Linux: Java"}}, {"id": "analytics_dashboard_zh", "path": "extended_services/analytics_dashboard/zh/docs_analytics_dashboard_zh", "routeBasePath": "analytics-dashboard", "locale": "zh", "navigationInfo": {"group": {"id": "analytics_dashboard_zh", "name": "星图", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "文档"}}, {"id": "analytics_dashboard_server_zh", "path": "extended_services/analytics_dashboard/zh/docs_analytics_dashboard_server_zh", "routeBasePath": "analytics-dashboard-server", "locale": "zh", "navigationInfo": {"group": {"id": "analytics_dashboard_zh", "name": "星图", "tag": ""}, "category": ["产品", "互动扩展服务"], "platform": "Server"}}, {"id": "aiagent_server_zh", "path": "core_products/aiagent/zh/server", "routeBasePath": "aiagent-server", "locale": "zh", "navigationInfo": {"group": {"id": "aiagent_zh", "name": "实时互动 AI Agent", "tag": "New"}, "category": ["产品", "AIGC"], "platform": "Server"}}, {"id": "aiagent_android_zh", "path": "core_products/aiagent/zh/android", "routeBasePath": "aiagent-android", "locale": "zh", "navigationInfo": {"group": {"id": "aiagent_zh", "name": "实时互动 AI Agent", "tag": "New"}, "category": ["产品", "AIGC"], "platform": "Android: Java"}}, {"id": "aiagent_ios_zh", "path": "core_products/aiagent/zh/ios", "routeBasePath": "aiagent-ios", "locale": "zh", "navigationInfo": {"group": {"id": "aiagent_zh", "name": "实时互动 AI Agent", "tag": "New"}, "category": ["产品", "AIGC"], "platform": "iOS: Objective-C"}}, {"id": "aiagent_web_zh", "path": "core_products/aiagent/zh/web", "routeBasePath": "aiagent-web", "locale": "zh", "navigationInfo": {"group": {"id": "aiagent_zh", "name": "实时互动 AI Agent", "tag": "New"}, "category": ["产品", "AIGC"], "platform": "Web: JS"}}, {"id": "aiagent_flutter_zh", "path": "core_products/aiagent/zh/flutter", "routeBasePath": "aiagent-flutter", "locale": "zh", "navigationInfo": {"group": {"id": "aiagent_zh", "name": "实时互动 AI Agent", "tag": "New"}, "category": ["产品", "AIGC"], "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "aigc_digital_human_zh", "label": "数字人 API（服务端）", "path": "core_products/digital-human/zh/server", "routeBasePath": "aigc-digital-human-server", "locale": "zh", "navigationInfo": {"group": {"id": "aigc_digital_human_zh", "name": "数字人 API", "tag": ""}, "category": ["产品", "AIGC"], "platform": "Server"}}, {"id": "shumei_moderation_zh", "path": "cloud-market/shumei-moderation/zh/server", "routeBasePath": "shumei-moderation", "locale": "zh", "askAi": {"questions": ["能对哪些内容进行审核？", "怎么审核一条视频流？"]}, "navigationInfo": {"group": {"id": "shumei-moderation-zh", "name": "数美内容审核", "tag": ""}, "category": ["产品", "云市场"], "platform": "Server"}}, {"id": "ai-voice_changer_android_zh", "path": "cloud-market/ai-voice-changer/zh/android", "routeBasePath": "ai-voice-changer-android", "locale": "zh", "askAi": {"questions": ["支持什么语言？", "有哪些应用场景？"]}, "navigationInfo": {"group": {"id": "ai-voice-changer-zh", "name": "大饼 AI 变声", "tag": ""}, "category": ["产品", "云市场"], "platform": "Android: Java"}}, {"id": "ai_voice_changer_flutter_zh", "path": "cloud-market/ai-voice-changer/zh/flutter", "routeBasePath": "ai-voice-changer-flutter", "locale": "zh", "navigationInfo": {"group": {"id": "ai-voice-changer-zh", "name": "大饼 AI 变声", "tag": ""}, "category": ["产品", "云市场"], "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "ai_voice_changer_ios_zh", "path": "cloud-market/ai-voice-changer/zh/ios", "routeBasePath": "ai-voice-changer-ios", "locale": "zh", "navigationInfo": {"group": {"id": "ai-voice-changer-zh", "name": "大饼 AI 变声", "tag": ""}, "category": ["产品", "云市场"], "platform": "iOS: Objective-C"}}, {"id": "ai_voice_changer_windows_zh", "path": "cloud-market/ai-voice-changer/zh/windows", "routeBasePath": "ai-voice-changer-windows", "locale": "zh", "navigationInfo": {"group": {"id": "ai-voice-changer-zh", "name": "大饼 AI 变声", "tag": ""}, "category": ["产品", "云市场"], "platform": "Windows: C++"}}, {"id": "real_time_translation_android_zh", "path": "cloud-market/realtime-translation/zh/android", "routeBasePath": "real-time-translation-android", "locale": "zh", "askAi": {"questions": ["支持什么语言？", "有哪些应用场景？"]}, "navigationInfo": {"group": {"id": "real-time-translation-zh", "name": "实时传译", "tag": ""}, "category": ["产品", "云市场"], "platform": "Android: Java"}}, {"id": "real_time_translation_ios_zh", "path": "cloud-market/realtime-translation/zh/ios", "routeBasePath": "real-time-translation-ios", "locale": "zh", "navigationInfo": {"group": {"id": "real-time-translation-zh", "name": "实时传译", "tag": ""}, "category": ["产品", "云市场"], "platform": "iOS: Objective-C"}}, {"id": "solution_audio_room_android_java_zh", "path": "solutions/live-audio-room/android", "routeBasePath": "live-audio-room-android-java", "locale": "zh", "navigationInfo": {"group": {"id": "solution_audio_room_zh", "name": "语聊房", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Android: Java"}}, {"id": "solution_audio_room_ios_objective_c_zh", "path": "solutions/live-audio-room/ios-oc", "routeBasePath": "live-audio-room-ios-objective-c", "locale": "zh", "navigationInfo": {"group": {"id": "solution_audio_room_zh", "name": "语聊房", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "iOS: Objective-C"}}, {"id": "solution_audio_room_ios_swift_zh", "path": "solutions/live-audio-room/ios-swift", "routeBasePath": "live-audio-room-ios-swift", "locale": "zh", "navigationInfo": {"group": {"id": "solution_audio_room_zh", "name": "语聊房", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "iOS: Swift"}}, {"id": "solution_live_streaming_android_zh", "path": "solutions/live-streaming/android", "routeBasePath": "solution-live-streaming-android", "locale": "zh", "navigationInfo": {"group": {"id": "solution_live_streaming_zh", "name": "秀场直播", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Android: Java"}}, {"id": "solution_live_streaming_ios_zh", "path": "solutions/live-streaming/ios", "routeBasePath": "solution-live-streaming-ios", "locale": "zh", "navigationInfo": {"group": {"id": "solution_live_streaming_zh", "name": "秀场直播", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "iOS: Objective-C"}}, {"id": "solution_live_streaming_web_zh", "path": "solutions/live-streaming/web", "routeBasePath": "solution-live-streaming-web", "locale": "zh", "navigationInfo": {"group": {"id": "solution_live_streaming_zh", "name": "秀场直播", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Web: JS"}}, {"id": "online_KTV_android_java_zh", "path": "solutions/online-ktv/android", "routeBasePath": "online-ktv-android", "locale": "zh", "navigationInfo": {"group": {"id": "solution_ktv_zh", "name": "在线 KTV", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Android: Java"}}, {"id": "online_KTV_ios_objective-c_zh", "path": "solutions/online-ktv/ios", "routeBasePath": "online-ktv-ios", "locale": "zh", "navigationInfo": {"group": {"id": "solution_ktv_zh", "name": "在线 KTV", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "iOS: Objective-C"}}, {"id": "online_KTV_windows_cpp_zh", "path": "solutions/online-ktv/windows", "routeBasePath": "online-ktv-windows", "locale": "zh", "navigationInfo": {"group": {"id": "solution_ktv_zh", "name": "在线 KTV", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Windows: C++"}}, {"id": "online_KTV_linux_cpp_zh", "path": "solutions/online-ktv/linux", "routeBasePath": "online-ktv-linux", "locale": "zh", "navigationInfo": {"group": {"id": "solution_ktv_zh", "name": "在线 KTV", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Linux: C++"}}, {"id": "online_KTV_web_javascript_zh", "path": "solutions/online-ktv/web", "routeBasePath": "online-ktv-web", "locale": "zh", "navigationInfo": {"group": {"id": "solution_ktv_zh", "name": "在线 KTV", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Web: JS"}}, {"id": "online_KTV_flutter_dart_zh", "path": "solutions/online-ktv/flutter", "routeBasePath": "online-ktv-flutter", "locale": "zh", "navigationInfo": {"group": {"id": "solution_ktv_zh", "name": "在线 KTV", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "online_KTV_electron_javascript_zh", "path": "solutions/online-ktv/electron", "routeBasePath": "online-ktv-electron", "locale": "zh", "navigationInfo": {"group": {"id": "solution_ktv_zh", "name": "在线 KTV", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Electron: JS"}}, {"id": "online_KTV_unity3d_cs_zh", "path": "solutions/online-ktv/u3d", "routeBasePath": "online-ktv-u3d", "locale": "zh", "navigationInfo": {"group": {"id": "solution_ktv_zh", "name": "在线 KTV", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Unity3D: C#"}}, {"id": "online_KTV_react-native_javascript_zh", "path": "solutions/online-ktv/rn", "routeBasePath": "online-ktv-rn", "locale": "zh", "navigationInfo": {"group": {"id": "solution_ktv_zh", "name": "在线 KTV", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "React Native: JS"}}, {"id": "online_KTV_server_zh", "path": "solutions/online-ktv/server", "routeBasePath": "online-ktv-server", "locale": "zh", "navigationInfo": {"group": {"id": "solution_ktv_zh", "name": "在线 KTV", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "服务端 API"}}, {"id": "solution_online_fitness_zh", "path": "solutions/online-fitness", "routeBasePath": "online-fitness", "locale": "zh", "navigationInfo": {"group": {"id": "solution_online_fitness_zh", "name": "在线健身", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Android: Java"}}, {"id": "solution_interactive_podcast_zh", "path": "solutions/interactive-podcast", "routeBasePath": "interactive-podcast", "locale": "zh", "navigationInfo": {"group": {"id": "solution_interactive_podcast_zh", "name": "互动播客", "tag": ""}, "category": ["解决方案", "社交娱乐"], "platform": "Android: Java"}}, {"id": "solution_small_class_zh", "path": "solutions/small-class", "routeBasePath": "small-class", "locale": "zh", "navigationInfo": {"group": {"id": "solution_small_class_zh", "name": "小班课", "tag": ""}, "category": ["解决方案", "在线教育"], "platform": "Android: Java"}}, {"id": "solution_big_class_zh", "path": "solutions/large-class", "routeBasePath": "large-class", "locale": "zh", "navigationInfo": {"group": {"id": "solution_big_class_zh", "name": "大班课", "tag": ""}, "category": ["解决方案", "在线教育"], "platform": "Android: Java"}}, {"id": "solution_online_music_lessons_zh", "path": "solutions/online-music-teaching", "routeBasePath": "online-music-teaching", "locale": "zh", "navigationInfo": {"group": {"id": "solution_online_music_lessons_zh", "name": "在线音乐教学", "tag": ""}, "category": ["解决方案", "在线教育"], "platform": "Android: Java"}}, {"id": "solution_online_painting_lessons_zh", "path": "solutions/online-art-teaching", "routeBasePath": "online-art-teaching", "locale": "zh", "navigationInfo": {"group": {"id": "solution_online_painting_lessons_zh", "name": "在线美术教学", "tag": ""}, "category": ["解决方案", "在线教育"], "platform": "Android: Java"}}, {"id": "solution_online_programming_lessons_zh", "path": "solutions/online-code-teaching", "routeBasePath": "online-code-teaching", "locale": "zh", "navigationInfo": {"group": {"id": "solution_online_programming_lessons_zh", "name": "在线编程教学", "tag": ""}, "category": ["解决方案", "在线教育"], "platform": "Android: Java"}}, {"id": "solution_telemedicine_zh", "path": "solutions/remote_medical", "routeBasePath": "remote-medical", "locale": "zh", "navigationInfo": {"group": {"id": "solution_telemedicine_zh", "name": "远程医疗", "tag": ""}, "category": ["解决方案", "医疗健康"], "platform": "Android: Java"}}, {"id": "solution_voice_call_zh", "path": "solutions/voice-call", "routeBasePath": "voice-call", "locale": "zh", "navigationInfo": {"group": {"id": "solution_voice_call_zh", "name": "语音通话", "tag": ""}, "category": ["解决方案", "协同办公"], "platform": "Android: Java"}}, {"id": "solution_video_call_zh", "path": "solutions/video-call", "routeBasePath": "video-call", "locale": "zh", "navigationInfo": {"group": {"id": "solution_video_call_zh", "name": "视频通话", "tag": ""}, "category": ["解决方案", "协同办公"], "platform": "Android: Java"}}, {"id": "solution_meeting_zh", "path": "solutions/video-conference", "routeBasePath": "video-conference", "locale": "zh", "navigationInfo": {"group": {"id": "solution_meeting_zh", "name": "视频会议", "tag": ""}, "category": ["解决方案", "协同办公"], "platform": "Android: Java"}}, {"id": "console_new_zh", "label": "控制台", "path": "general/zh/console/new", "routeBasePath": "console", "locale": "zh", "navigationInfo": {"group": {"id": "console-zh", "name": "控制台", "tag": ""}, "category": ["通用文档", "ZEGO 平台"], "platform": "新版本"}}, {"id": "console_old_zh", "label": "控制台", "path": "general/zh/console/old", "routeBasePath": "console-old", "locale": "zh", "navigationInfo": {"group": {"id": "console-zh", "name": "控制台", "tag": ""}, "category": ["通用文档", "ZEGO 平台"], "platform": "旧版本"}}, {"id": "policies_and_agreements_zh", "path": "general/zh/docs_policies_and_agreements_zh", "routeBasePath": "policies-and-agreements", "locale": "zh", "navigationInfo": {"group": {"id": "policies_and_agreements_zh", "name": "政策与协议", "tag": ""}, "category": ["通用文档", "ZEGO 平台"], "platform": "All"}}, {"id": "glossary_zh", "path": "general/zh/docs_glossary_zh", "routeBasePath": "glossary", "locale": "zh", "navigationInfo": {"group": {"id": "glossary_zh", "name": "术语表", "tag": ""}, "category": ["通用文档", "ZEGO 平台"], "platform": "All"}}, {"id": "faq_zh", "path": "general/zh/faq", "routeBasePath": "faq", "locale": "zh", "navigationInfo": {"group": {"id": "faq-zh", "name": "常见问题", "tag": ""}, "category": ["通用文档", "ZEGO 平台"], "platform": "All"}}, {"id": "real_time_video", "label": "Video Call", "path": "https://www.zegocloud.com/docs/video-call/overview?platform=android&language=java", "routeBasePath": "real-time-video", "locale": "en", "navigationInfo": {"group": {"id": "real_time_video", "name": "Video Call", "tag": ""}, "category": ["Products", "Cloud Communications Products"], "platform": "Android: Java"}}, {"id": "real_time_voice", "label": "Video Call", "path": "https://www.zegocloud.com/docs/voice-call/overview?platform=android&language=java", "routeBasePath": "real-time-voice", "locale": "en", "navigationInfo": {"group": {"id": "real_time_voice", "name": "Voice Call", "tag": ""}, "category": ["Products", "Cloud Communications Products"], "platform": "Android: Java"}}, {"id": "live_streaming", "label": "Video Call", "path": "https://www.zegocloud.com/docs/live-streaming/overview?platform=android&language=java", "routeBasePath": "live-streaming", "locale": "en", "navigationInfo": {"group": {"id": "live_streaming", "name": "Live Streaming", "tag": ""}, "category": ["Products", "Cloud Communications Products"], "platform": "Android: Java"}}, {"id": "zim_android", "label": "In-app Chat(Android)", "path": "core_products/zim/en/docs_zim_android", "routeBasePath": "zim-android", "navigationInfo": {"group": {"id": "zim", "name": "In-app Chat", "tag": "New"}, "category": ["Products", "Cloud Communications Products"], "tab": "Document", "platform": "Android: Java"}, "askAi": {"questions": ["How to Get Offline Push PushID", "How to upgrade from AppSign authentication to Token authentication in In-app Chat?", "How to give a thumbs up to a message?"]}}, {"id": "zim_flutter", "label": "In-app Chat(Flutter)", "path": "core_products/zim/en/docs_zim_flutter", "routeBasePath": "zim-flutter", "navigationInfo": {"group": {"id": "zim", "name": "In-app Chat", "tag": "New"}, "category": ["Products", "Cloud Communications Products"], "tab": "Document", "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "zim_ios", "label": "In-app Chat(iOS)", "path": "core_products/zim/en/docs_zim_ios", "routeBasePath": "zim-ios", "navigationInfo": {"group": {"id": "zim", "name": "In-app Chat", "tag": "New"}, "category": ["Products", "Cloud Communications Products"], "tab": "Document", "platform": "iOS: Objective-C"}}, {"id": "zim_macos", "label": "In-app Chat(macOS)", "path": "core_products/zim/en/docs_zim_macos", "routeBasePath": "zim-macos", "navigationInfo": {"group": {"id": "zim", "name": "In-app Chat", "tag": "New"}, "category": ["Products", "Cloud Communications Products"], "tab": "Document", "platform": "macOS: Objective-C"}}, {"id": "zim_rn", "label": "In-app Chat(RN)", "path": "core_products/zim/en/docs_zim_rn", "routeBasePath": "zim-rn", "navigationInfo": {"group": {"id": "zim", "name": "In-app Chat", "tag": "New"}, "category": ["Products", "Cloud Communications Products"], "tab": "Document", "platform": "React Native: TS"}}, {"id": "zim_u3d", "label": "In-app Chat(Unity 3D)", "path": "core_products/zim/en/docs_zim_u3d", "routeBasePath": "zim-u3d", "navigationInfo": {"group": {"id": "zim", "name": "In-app Chat", "tag": "New"}, "category": ["Products", "Cloud Communications Products"], "tab": "Document", "platform": "Unity3D: C#"}}, {"id": "zim_web", "label": "In-app Chat(Web)", "path": "core_products/zim/en/docs_zim_web", "routeBasePath": "zim-web", "navigationInfo": {"group": {"id": "zim", "name": "In-app Chat", "tag": "New"}, "category": ["Products", "Cloud Communications Products"], "tab": "Document", "platform": "Web: TS"}}, {"id": "zim_windows", "label": "In-app Chat(Windows)", "path": "core_products/zim/en/docs_zim_windows", "routeBasePath": "zim-win", "navigationInfo": {"group": {"id": "zim", "name": "In-app Chat", "tag": "New"}, "category": ["Products", "Cloud Communications Products"], "tab": "Document", "platform": "Windows: C++"}}, {"id": "zim_client_api", "label": "In-app Chat(Client API)", "path": "core_products/zim/en/client-api", "routeBasePath": "zim-client-api", "navigationInfo": {"group": {"id": "zim", "name": "In-app Chat", "tag": "New"}, "category": ["Products", "Cloud Communications Products"], "tab": "Client API", "platform": "All"}}, {"id": "zim_server", "label": "In-app Chat(Server)", "path": "core_products/zim/en/docs_zim_server", "routeBasePath": "zim-server", "navigationInfo": {"group": {"id": "zim", "name": "In-app Chat", "tag": "New"}, "category": ["Products", "Cloud Communications Products"], "tab": "Server API", "platform": "Server"}}, {"id": "callkit_android", "label": "<PERSON> Kit(Android)", "path": "uikit/callkit/docs_callkit_android", "routeBasePath": "uikit/callkit-android", "locale": "en", "navigationInfo": {"group": {"id": "callkit", "name": "Call Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Android: Java"}}, {"id": "callkit_ios", "label": "Call Kit(iOS)", "path": "uikit/callkit/docs_callkit_ios", "routeBasePath": "uikit/callkit-ios", "locale": "en", "navigationInfo": {"group": {"id": "callkit", "name": "Call Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "iOS: Swift"}}, {"id": "callkit_flutter", "label": "Call Kit(Flutter)", "path": "uikit/callkit/docs_callkit_flutter", "routeBasePath": "uikit/callkit-flutter", "locale": "en", "navigationInfo": {"group": {"id": "callkit", "name": "Call Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "callkit_rn", "label": "Call Kit(RN)", "path": "uikit/callkit/docs_callkit_rn", "routeBasePath": "uikit/callkit-rn", "locale": "en", "navigationInfo": {"group": {"id": "callkit", "name": "Call Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "React Native: JS"}}, {"id": "callkit_web", "label": "Call Kit(Web)", "path": "uikit/callkit/docs_callkit_web", "routeBasePath": "uikit/callkit-web", "locale": "en", "navigationInfo": {"group": {"id": "callkit", "name": "Call Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Web: JS"}}, {"id": "live_streaming_kit_android", "label": "Live Streaming Kit(Android)", "path": "uikit/live_streaming_kit/docs_live_streaming_kit_android", "routeBasePath": "uikit/live-streaming-kit-android", "locale": "en", "navigationInfo": {"group": {"id": "live_streaming_kit", "name": "Live Streaming Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Android: Java"}}, {"id": "live_streaming_kit_ios", "label": "Live Streaming Kit(iOS)", "path": "uikit/live_streaming_kit/docs_live_streaming_kit_ios", "routeBasePath": "uikit/live-streaming-kit-ios", "locale": "en", "navigationInfo": {"group": {"id": "live_streaming_kit", "name": "Live Streaming Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "iOS: Swift"}}, {"id": "live_streaming_kit_flutter", "label": "Live Streaming Kit(Flutter)", "path": "uikit/live_streaming_kit/docs_live_streaming_kit_flutter", "routeBasePath": "uikit/live-streaming-kit-flutter", "locale": "en", "navigationInfo": {"group": {"id": "live_streaming_kit", "name": "Live Streaming Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "live_streaming_kit_rn", "label": "Live Streaming Kit(RN)", "path": "uikit/live_streaming_kit/docs_live_streaming_kit_rn", "routeBasePath": "uikit/live-streaming-kit-rn", "locale": "en", "navigationInfo": {"group": {"id": "live_streaming_kit", "name": "Live Streaming Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "React Native: JS"}}, {"id": "live_streaming_kit_web", "label": "Live Streaming Kit(Web)", "path": "uikit/live_streaming_kit/docs_live_streaming_kit_web", "routeBasePath": "uikit/live-streaming-kit-web", "locale": "en", "navigationInfo": {"group": {"id": "live_streaming_kit", "name": "Live Streaming Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Web: JS"}}, {"id": "live_audio_room_kit_android", "label": "Live Audio Room Kit(Android)", "path": "uikit/live_audio_room_kit/docs_live_audio_room_kit_android", "routeBasePath": "uikit/live-audio-room-kit-android", "navigationInfo": {"group": {"id": "live_audio_room_kit", "name": "Live Audio Room Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Android: Java"}}, {"id": "live_audio_room_kit_ios", "label": "Live Audio Room Kit(iOS)", "path": "uikit/live_audio_room_kit/docs_live_audio_room_kit_ios", "routeBasePath": "uikit/live-audio-room-kit-ios", "navigationInfo": {"group": {"id": "live_audio_room_kit", "name": "Live Audio Room Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "iOS: Swift"}}, {"id": "live_audio_room_kit_flutter", "label": "Live Audio Room Kit(Flutter)", "path": "uikit/live_audio_room_kit/docs_live_audio_room_kit_flutter", "routeBasePath": "uikit/live-audio-room-kit-flutter", "navigationInfo": {"group": {"id": "live_audio_room_kit", "name": "Live Audio Room Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "live_audio_room_kit_rn", "label": "Live Audio Room Kit(RN)", "path": "uikit/live_audio_room_kit/docs_live_audio_room_kit_rn", "routeBasePath": "uikit/live-audio-room-kit-rn", "navigationInfo": {"group": {"id": "live_audio_room_kit", "name": "Live Audio Room Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "React Native: JS"}}, {"id": "in_app_chat_kit_android", "label": "In-app Chat Kit(Android)", "path": "uikit/imkit/docs_in_app_chat_kit_android", "routeBasePath": "uikit/in-app-chat-kit-android", "navigationInfo": {"group": {"id": "imkit", "name": "In-app Chat Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Android: Java"}}, {"id": "in_app_chat_kit_ios", "label": "In-app Chat Kit(iOS)", "path": "uikit/imkit/docs_in_app_chat_kit_ios", "routeBasePath": "uikit/in-app-chat-kit-ios", "navigationInfo": {"group": {"id": "imkit", "name": "In-app Chat Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "iOS: Swift"}}, {"id": "in_app_chat_kit_flutter", "label": "In-app Chat Kit(Flutter)", "path": "uikit/imkit/docs_in_app_chat_kit_flutter", "routeBasePath": "uikit/in-app-chat-kit-flutter", "navigationInfo": {"group": {"id": "imkit", "name": "In-app Chat Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "in_app_chat_kit_rn", "label": "In-app Chat Kit(RN)", "path": "uikit/imkit/docs_in_app_chat_kit_rn", "routeBasePath": "uikit/in-app-chat-kit-rn", "navigationInfo": {"group": {"id": "imkit", "name": "In-app Chat Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "React Native: JS"}}, {"id": "video_conference_kit_android", "label": "Video Conference Kit(Android)", "path": "uikit/video_conference_kit/docs_video_conference_kit_android", "routeBasePath": "uikit/video-conference-kit-android", "navigationInfo": {"group": {"id": "video_conference_kit", "name": "Video Conference Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Android: Java"}}, {"id": "video_conference_kit_ios", "label": "Video Conference Kit(iOS)", "path": "uikit/video_conference_kit/docs_video_conference_kit_ios", "routeBasePath": "uikit/video-conference-kit-ios", "navigationInfo": {"group": {"id": "video_conference_kit", "name": "Video Conference Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "iOS: Swift"}}, {"id": "video_conference_kit_flutter", "label": "Video Conference Kit(Flutter)", "path": "uikit/video_conference_kit/docs_video_conference_kit_flutter", "routeBasePath": "uikit/video-conference-kit-flutter", "navigationInfo": {"group": {"id": "video_conference_kit", "name": "Video Conference Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "video_conference_kit_rn", "label": "Video Conference Kit(RN)", "path": "uikit/video_conference_kit/docs_video_conference_kit_rn", "routeBasePath": "uikit/video-conference-kit-rn", "navigationInfo": {"group": {"id": "video_conference_kit", "name": "Video Conference Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "React Native: JS"}}, {"id": "video_conference_kit_web", "label": "Video Conference Kit(Web)", "path": "uikit/video_conference_kit/docs_video_conference_kit_web", "routeBasePath": "uikit/video-conference-kit-web", "navigationInfo": {"group": {"id": "video_conference_kit", "name": "Video Conference Kit", "tag": ""}, "category": ["Products", "ZEGO UIKits"], "platform": "Web: JS"}}, {"id": "supper_board", "label": "Super Board", "path": "https://docs.zegocloud.com/article/13884", "routeBasePath": "super-board", "locale": "en", "navigationInfo": {"group": {"id": "supper_board", "name": "Super Board", "tag": ""}, "category": ["Products", "Supporting Services"], "platform": "Android: Java"}}, {"id": "cloud_player", "label": "Cloud Player", "path": "extended_services/cloud_player/en/docs_cloud_player", "routeBasePath": "en/cloud-player", "locale": "en", "navigationInfo": {"group": {"id": "cloud_player", "name": "Cloud Player", "tag": ""}, "category": ["Products", "Supporting Services"], "platform": "All"}}, {"id": "cloud_player_server", "label": "Cloud Player (Server)", "path": "extended_services/cloud_player/en/docs_cloud_player_server", "routeBasePath": "en/cloud-player-server", "locale": "en", "navigationInfo": {"group": {"id": "cloud_player", "name": "Cloud Player", "tag": ""}, "category": ["Products", "Supporting Services"], "platform": "Server API"}}, {"id": "ai_effects_ios_objc", "label": "AI Effects (iOS)", "path": "extended_services/ai-effects/en/ios-objc", "routeBasePath": "en/ai-effects-ios-objc", "locale": "en", "navigationInfo": {"group": {"id": "ai_effects", "name": "AI Effects", "tag": ""}, "category": ["Products", "Supporting Services"], "platform": "iOS: Objective-C"}}, {"id": "ai_effects_android_java", "label": "AI Effects (Java)", "path": "extended_services/ai-effects/en/android-java", "routeBasePath": "en/ai-effects-android-java", "locale": "en", "navigationInfo": {"group": {"id": "ai_effects", "name": "AI Effects", "tag": ""}, "category": ["Products", "Supporting Services"], "platform": "Android: Java"}}, {"id": "ai_effects_react_native_javascript", "label": "AI Effects (React Native)", "path": "extended_services/ai-effects/en/react-native-javascript", "routeBasePath": "en/ai-effects-react-native-javascript", "locale": "en", "navigationInfo": {"group": {"id": "ai_effects", "name": "AI Effects", "tag": ""}, "category": ["Products", "Supporting Services"], "platform": "React Native: JS"}}, {"id": "cloud_recording", "label": "Cloud Recording", "path": "extended_services/cloud_recording/en/docs_cloud_recording", "routeBasePath": "en/cloud-recording", "locale": "en", "navigationInfo": {"group": {"id": "cloud_recording", "name": "Cloud Recording", "tag": ""}, "category": ["Products", "Supporting Services"], "platform": "Docs"}}, {"id": "cloud_recording_server", "label": "Cloud Recording", "path": "extended_services/cloud_recording/en/docs_cloud_recording_server", "routeBasePath": "en/cloud-recording-server", "locale": "en", "navigationInfo": {"group": {"id": "cloud_recording", "name": "Cloud Recording", "tag": ""}, "category": ["Products", "Supporting Services"], "platform": "Server"}}, {"id": "admin-console", "path": "https://www.zegocloud.com/docs/admin-console/overview?platform=all&language=all", "routeBasePath": "admin-console", "locale": "en", "navigationInfo": {"group": {"id": "admin-console", "name": "<PERSON><PERSON>", "tag": ""}, "category": ["Products", "Supporting Services"], "platform": "Android: Java"}}, {"id": "aiagent_server", "label": "AI Agent(Server)", "path": "core_products/aiagent/en/server", "routeBasePath": "en/aiagent-server", "locale": "en", "navigationInfo": {"group": {"id": "aiagent_en", "name": "AI Agent", "tag": "New"}, "category": ["Products", "AIGC"], "platform": "Server"}}, {"id": "aiagent_android", "label": "AI Agent(Android)", "path": "core_products/aiagent/en/android", "routeBasePath": "en/aiagent-android", "locale": "en", "navigationInfo": {"group": {"id": "aiagent_en", "name": "AI Agent", "tag": "New"}, "category": ["Products", "AIGC"], "platform": "Android: Java"}}, {"id": "aiagent_ios", "label": "AI Agent(iOS)", "path": "core_products/aiagent/en/ios", "routeBasePath": "en/aiagent-ios", "locale": "en", "navigationInfo": {"group": {"id": "aiagent_en", "name": "AI Agent", "tag": "New"}, "category": ["Products", "AIGC"], "platform": "iOS: Objective-C"}}, {"id": "aiagent_web", "label": "AI Agent(Web)", "path": "core_products/aiagent/en/web", "routeBasePath": "en/aiagent-web", "locale": "en", "navigationInfo": {"group": {"id": "aiagent_en", "name": "AI Agent", "tag": "New"}, "category": ["Products", "AIGC"], "platform": "Web: JS"}}, {"id": "aiagent_flutter", "label": "AI Agent(Flutter)", "path": "core_products/aiagent/en/flutter", "routeBasePath": "en/aiagent-flutter", "locale": "en", "navigationInfo": {"group": {"id": "aiagent_en", "name": "AI Agent", "tag": "New"}, "category": ["Products", "AIGC"], "platform": "Flutter: <PERSON><PERSON>"}}, {"id": "aigc_digital_human", "label": "Digital Human API", "path": "core_products/digital-human/en/server", "routeBasePath": "en/aigc-digital-human-server", "locale": "en", "navigationInfo": {"group": {"id": "aigc_digital_human", "name": "Digital Human API", "tag": ""}, "category": ["Products", "AIGC"], "platform": "Server"}}, {"id": "faq", "path": "general/en/faq", "routeBasePath": "en/faq", "locale": "en", "navigationInfo": {"group": {"id": "faq-en", "name": "FAQ", "tag": ""}, "category": ["General", "ZEGOCLOUD Platform"], "platform": "All"}}], "analytics": {"ga4": {"measurementId": "G-PKWFYY4JH2"}}, "i18n": {"defaultLocale": "en", "localeConfigs": {"en": "English", "zh": "中文"}}, "sitemap": {"siteUrl": "https://doc-zh.zego.im"}}