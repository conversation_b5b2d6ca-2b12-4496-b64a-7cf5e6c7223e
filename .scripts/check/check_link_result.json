{"mode": "interactive", "language": "zh", "check_remote": false, "generated_at": "2025-08-11T11:05:38.117367", "instances": {"实时音视频 (Android Java) (Android: Java)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/video/custom-video-preprocessing.mdx": [{"url": "#1-开启自定义视频前处理", "line_content": "调用 [enableCustomVideoProcessing](@enableCustomVideoProcessing) 接口，[开启自定义视频前处理](#1-开启自定义视频前处理) 功能。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/video/custom-video-preprocessing.mdx:47"}, {"url": "#2-获取原始视频数据进行视频前处理", "line_content": "[获取原始视频数据，进行视频前处理](#2-获取原始视频数据进行视频前处理)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/video/custom-video-preprocessing.mdx:50"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/video/custom-video-capture.mdx": [{"url": "/real-time-video-android-java/video/custom-video-capture#4_1", "line_content": "调用开始预览接口 [startPreview](@startPreview) 或者开始推流接口 [startPublishingStream](@startPublishingStream) 后，将会触发 [onStart](@onStart-IZegoCustomVideoCaptureHandler) 回调；开发者启动采集相关的业务逻辑后，调用发送自定义采集的视频帧数据接口向 SDK 发送视频帧数据，自定义采集的视频帧数据接口与 [1 开启自定义视频采集](/real-time-video-android-java/video/custom-video-capture#4_1) 中向 SDK 提供视频帧数据类型 [bufferType](@bufferType-ZegoCustomVideoCaptureConfig) 一一对应：", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/video/custom-video-capture.mdx:106"}, {"url": "/real-time-video-android-java/video/custom-video-capture#开启自定义视频采集", "line_content": "- 该功能仅当 [开启自定义视频采集](/real-time-video-android-java/video/custom-video-capture#开启自定义视频采集) 中设置的自定义采集类型 [bufferType](@bufferType-ZegoCustomVideoCaptureConfig) 为 `GL_TEXTURE_2D` 时才有效。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/video/custom-video-capture.mdx:140"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/other/media-player.mdx": [{"url": "/real-time-video-android-java/quick-start/implementing-video-call#设置权限", "line_content": "如果要使用混音能力，必须要 [设置麦克风权限](/real-time-video-android-java/quick-start/implementing-video-call#设置权限)，如果您不希望录制麦克风的声音，可以通过 [muteMicrophone](@muteMicrophone) 静音麦克风。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/other/media-player.mdx:231"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/other/local-media-recording.mdx": [{"url": "#方案四cdn-录制", "line_content": "<td><a href=\"#方案四cdn-录制\" >CDN 录制</a></td>", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/other/local-media-recording.mdx:49"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/live-streaming/playing-stream-by-url.mdx": [{"url": "/real-time-video-android-java/client-sdk/error-code#1004xxx-拉流相关的错误码", "line_content": "拉流时，如果出现错误，请参考 [常见错误码 - 1004xxx 拉流相关的错误码](/real-time-video-android-java/client-sdk/error-code#1004xxx-拉流相关的错误码)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/live-streaming/playing-stream-by-url.mdx:62"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/quick-start/integrating-sdk.mdx": [{"url": "/real-time-video-android-java/quick-start/implementing-video-call#Integration_3", "line_content": "- 其他文件：供您在采用 [方式 3：复制 SDK JAR + SO 文件手动集成](/real-time-video-android-java/quick-start/implementing-video-call#Integration_3) 使用。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/quick-start/integrating-sdk.mdx:102"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/client-sdk/release-notes.mdx": [{"url": "/real-time-video-ios-oc/client-sdk/error-code#3", "line_content": "新增 1002074、1002075、1002076、1002077、1002078、1002079、1002080 等错误码。开启强制登录鉴权后，如果 Token 错误时，会返回这些错误码，详情请参考 <a href=\"/real-time-video-ios-oc/client-sdk/error-code#3\" target=\"_blank\" rel=\"noopener noreferrer\">常见错误码</a> 中的详细解释和处理建议。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/client-sdk/release-notes.mdx:2229"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/communication/using-token-authentication.mdx:272"}, {"url": "#使用-token", "line_content": "如需使用客户端生成 Token，请参考 [使用 Token](#使用-token)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/communication/using-token-authentication.mdx:427"}, {"url": "#token-过期处理机制", "line_content": "若 Token 过期，请参考 [Token 过期时的处理方式](#token-过期处理机制) 进行处理。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/communication/using-token-authentication.mdx:429"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/integration-with-zego-effects-sdk.mdx": [{"url": "/real-time-video-android-java/video/custom-video-capture#4_3", "line_content": "在接收到自定义采集的 [onStart](@onStart) 回调后，开发者通过自定义采集获取视频数据，再调用 ZEGO Effects SDK 的相关接口，进行 AI 美颜处理（请参考 [美颜](/ai-effects-android-java/guides/face-beautification)、[美型](/ai-effects-android-java/guides/shape-retouch)、[背景分割](/ai-effects-android-java/guides/background-segmentation)、[人脸检测](/ai-effects-android-java/guides/face-detection)、[挂件](https://doc-zh.zego.im/faq/AIEffect_Stickers?product=Effects&platform=android)、[滤镜](/ai-effects-android-java/guides/filters)），并将处理后的数据，返回给 ZEGO Express SDK（可参考 [自定义视频采集](/real-time-video-android-java/video/custom-video-capture#4_3) 中的 “向 SDK 发送视频帧数据”）。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/integration-with-zego-effects-sdk.mdx:241"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx": [{"url": "/zim-android/send-and-receive-messages#create", "line_content": "- 在管理房间内的麦位之前，需要先初始化 ZIM SDK，并设置通知回调，以便监听 ZIM 事件，接口调用详情请参考 [即时通讯 - 实现基本消息收发](/zim-android/send-and-receive-messages#create) 的 “2. 创建 ZIM 实例” 和 “3. 使用 EventHandler 协议”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:76"}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#4_1", "line_content": "- 在实现主体分割之前，需要先初始化 ZEGO Express SDK，同时设置通知回调，以便监听 Express 事件，接口调用详情请参考 [实时音视频 - 实现视频通话](/real-time-video-android-java/quick-start/implementing-video-call#4_1) 的 “初始化”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:77"}, {"url": "/zim-android/send-and-receive-messages#login", "line_content": "1. 登录 ZIM 服务，接口调用请参考 [即时通讯 - 实现基本消息收发](/zim-android/send-and-receive-messages#login) 的 “2. 登录 ZIM”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:85"}, {"url": "/zim-android/guides/room/manage-rooms#创建房间加入房间", "line_content": "2. 用户需要创建或加入 ZIM 房间，详情请参考 [即时通讯 - 房间管理](/zim-android/guides/room/manage-rooms#创建房间加入房间) 的 “创建房间、加入房间”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:86"}, {"url": "/zim-android/guides/room/room-properties#获取房间属性", "line_content": "- 加入 ZIM 房间后，用户可通过查询房间所有属性了解房间内的麦位信息，接口调用详情请参考 [即时通讯 - 房间属性管理](/zim-android/guides/room/room-properties#获取房间属性) 的 “获取房间属性”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:92"}, {"url": "/zim-android/guides/room/room-properties#获取房间属性", "line_content": "- 用户如需上麦，可通过修改房间属性，修改麦位信息，接口调用详情请参考 [即时通讯 - 房间属性管理](/zim-android/guides/room/room-properties#获取房间属性) 的 “设置房间属性”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:93"}, {"url": "/real-time-video-android-java/video/object-segmentation#4_7", "line_content": "1. 为在推流时传输经主体分割后的图像，需要设置透明通道，请参考 [实时音视频 - 主体分割](/real-time-video-android-java/video/object-segmentation#4_7) 的 “使用 Alpha 通道传输分割出的主体”，了解如何调用 [enableAlphaChannelVideoEncoder](@enableAlphaChannelVideoEncoder) 设置透明通道。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:99"}, {"url": "/real-time-video-android-java/video/common-video-configuration#4_3", "line_content": "2. 由于手机前置摄像头捕捉的画面与实际左右相反，需要开启屏幕镜像，以便预览或拉流时，获取正确方向画面，接口调用详情请参考 [实时音视频 - 常用视频配置](/real-time-video-android-java/video/common-video-configuration#4_3) 的 “4.3 设置镜像模式”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:101"}, {"url": "/real-time-video-android-java/video/object-segmentation#4_2", "line_content": "4. 如需预览本端的画面，需先将用于渲染的 View 的 backgroundColor 属性为 clearColor（透明色），接口调用详情请参考 [实时音视频 - 主体分割](/real-time-video-android-java/video/object-segmentation#4_2) 的 “对 view 进行特殊设置”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:105"}, {"url": "/real-time-video-android-java/video/object-segmentation#4_8", "line_content": "预览和推送本端画面，接口调用详情请参考 [实时音视频 - 主体分割](/real-time-video-android-java/video/object-segmentation#4_8) 的 “开始预览和推流”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:107"}, {"url": "/real-time-video-android-java/video/object-segmentation#4_5", "line_content": "5. 开启主体分割，接收分割结果，接口调用详情请参考 [实时音视频 - 主体分割](/real-time-video-android-java/video/object-segmentation#4_5) 的 “监听主体分割状态回调” 和 “使用主体分割实现多种业务功能”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:109"}, {"url": "/real-time-video-android-java/video/object-segmentation#4_2", "line_content": "1. 将用于渲染的 TextureView 的 opaque 属性为 false（透明色），接口调用详情请参考 [实时音视频 - 主体分割](/real-time-video-android-java/video/object-segmentation#4_2) 的 “对 view 进行特殊设置”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:121"}, {"url": "/real-time-video-android-java/video/object-segmentation#4_9", "line_content": "2. 拉取对方已实现主体分割的视频流，从而实现两个用户处在 “同一空间” 面对面对话的视觉效果。接口调用详情请参考 [实时音视频 - 主体分割](/real-time-video-android-java/video/object-segmentation#4_9) 的 “开始拉流”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:127"}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#5_2", "line_content": "3. 如需结束拉流，接口调用详情可参考 [实时音视频 - 实现视频通话](/real-time-video-android-java/quick-start/implementing-video-call#5_2) 的 “停止音视频通话”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:128"}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#5_2", "line_content": "1. 停止预览和推流，接口调用详情请参考 [实时音视频 - 实现视频通话](/real-time-video-android-java/quick-start/implementing-video-call#5_2) 的 “停止音视频通话”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:134"}, {"url": "/zim-android/guides/room/manage-rooms#3_3", "line_content": "2. 离开 ZIM 房间，接口调用详情请参考 [即时通讯 - 房间管理](/zim-android/guides/room/manage-rooms#3_3) 的 “离开房间”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:135"}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#5_2", "line_content": "3. 退出 RTC 房间，接口调用详情请参考 [实时音视频 - 实现视频通话](/real-time-video-android-java/quick-start/implementing-video-call#5_2) 的 “停止音视频通话”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/best-practice/object-segmentation/implementation.mdx:136"}]}, "根路径链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/other/push-the-whiteboard.mdx": [{"url": "/super-board-ios/product-desc/overview", "line_content": "本文主要讲述如何利用 ZEGO Express SDK 的混流功能，将音视频流和 [ZegoSuperBoard](/super-board-ios/product-desc/overview) 内容合并成一路流，输出到第三方平台，例如微信、视频号等，从而达到更好的传播和营销效果。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-java/other/push-the-whiteboard.mdx:14"}]}}, "实时音视频 (Android Kotlin) (Android: Kotlin)": {"根路径链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-kotlin/client-sdk/download-sdk.mdx": [{"url": "/super-board-android/download-sdk", "line_content": "- 如果需要使用“含白板功能”的 SDK，可到 [超级白板 - 下载 SDK](/super-board-android/download-sdk) 中下载“超级白板 SDK”，该 SDK 中已包含 ZegoExpress-Video SDK（含白板功能），无需单独下载。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/android-kotlin/client-sdk/download-sdk.mdx:15"}]}}, "实时音视频 (Cocos Creator TS) (Cocos Creator: TS)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/cocos-creator-ts/quick-start/run-example-code.mdx": [{"url": "/real-time-video-cocos-creator/quick-start/implementing-video-call#常见问题", "line_content": "若生成失败，请参考 [集成 SDK](/real-time-video-cocos-creator/quick-start/implementing-video-call#常见问题) 文档中的常见问题 1、2、3 进行操作。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/cocos-creator-ts/quick-start/run-example-code.mdx:115"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/cocos-creator-ts/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/cocos-creator-ts/communication/using-token-authentication.mdx:247"}, {"url": "#使用-token", "line_content": "如需使用客户端生成 Token，请参考 [使用 Token](#使用-token)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/cocos-creator-ts/communication/using-token-authentication.mdx:348"}, {"url": "#token-过期时的处理方式", "line_content": "若 Token 过期，请参考 [Token 过期时的处理方式](#token-过期时的处理方式) 进行处理。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/cocos-creator-ts/communication/using-token-authentication.mdx:350"}]}}, "实时音视频 (Electron JS) (Electron: JS)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/electron-js/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/electron-js/communication/using-token-authentication.mdx:249"}, {"url": "#使用-token", "line_content": "如需使用客户端生成 Token，请参考 [使用 Token](#使用-token)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/electron-js/communication/using-token-authentication.mdx:350"}, {"url": "#token-过期处理机制", "line_content": "若 Token 过期，请参考 [Token 过期时的处理方式](#token-过期处理机制) 进行处理。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/electron-js/communication/using-token-authentication.mdx:352"}]}}, "实时音视频 (Flutter Dart) (Flutter: Dart)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/video/screen-sharing.mdx": [{"url": "/real-time-video-android-java/video/screen-sharing#必选获取用户录制屏幕授权", "line_content": "Android 在使用屏幕录制功能时，请务必 [获取用户录制屏幕授权](/real-time-video-android-java/video/screen-sharing#必选获取用户录制屏幕授权)，否则将无法使用该功能。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/video/screen-sharing.mdx:62"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/other/media-player.mdx": [{"url": "/real-time-video-flutter/quick-start/implementing-video-call#设置权限", "line_content": "如果要使用混音能力，必须要 [设置麦克风权限](/real-time-video-flutter/quick-start/implementing-video-call#设置权限)，如果您不希望录制麦克风的声音，可以通过 [muteMicrophone](https://doc-zh.zego.im/unique-api/express-video-sdkhttps://doc-zh.zego.im/article/dart_flutter/zego_express_engine/ZegoExpressEngineDevice/muteMicrophone.html) 静音麦克风。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/other/media-player.mdx:206"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/live-streaming/direct-to-cdn.mdx": [{"url": "/real-time-video-android-java/quick-start/implementing-video-call#3_1", "line_content": "请参考 [快速开始 - 实现流程](/real-time-video-android-java/quick-start/implementing-video-call#3_1) 的 “创建引擎”、“登录房间”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/live-streaming/direct-to-cdn.mdx:29"}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#3_3", "line_content": "请参考 [快速开始 - 实现流程](/real-time-video-android-java/quick-start/implementing-video-call#3_3) 的 “3.3 推流”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/live-streaming/direct-to-cdn.mdx:62"}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#3_4", "line_content": "- 当开发者使用 ZEGO 配置的 CDN 进行直推时，则可以直接通过 streamID 进行拉流，请参考 [快速开始 - 实现流程](/real-time-video-android-java/quick-start/implementing-video-call#3_4) 的 “拉流”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/live-streaming/direct-to-cdn.mdx:104"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/client-sdk/release-notes.mdx": [{"url": "/real-time-video-flutter/video/screen-sharing#highlight", "line_content": "在桌面端使用屏幕共享时，可以为共享窗口添加一个高亮描边，并设置描边的颜色和宽度，提升共享窗口的识别度。详情请参考 [屏幕共享 - 描边](/real-time-video-flutter/video/screen-sharing#highlight)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/client-sdk/release-notes.mdx:20"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/communication/using-token-authentication.mdx:254"}, {"url": "#使用-token", "line_content": "如需使用客户端生成 Token，请参考 [使用 Token](#使用-token)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/communication/using-token-authentication.mdx:360"}, {"url": "#token-过期处理机制", "line_content": "若 Token 过期，请参考 [Token 过期时的处理方式](#token-过期处理机制) 进行处理。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/communication/using-token-authentication.mdx:362"}]}, "根路径链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/other/push-the-whiteboard.mdx": [{"url": "/super-board-ios/product-desc/overview", "line_content": "本文主要讲述如何利用 ZEGO Express SDK 的混流功能，将音视频流和 [ZegoSuperBoard](/super-board-ios/product-desc/overview) 内容合并成一路流，输出到第三方平台，例如微信、视频号等，从而达到更好的传播和营销效果。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/flutter-dart/other/push-the-whiteboard.mdx:16"}]}}, "实时音视频 (HarmonyOS ArkTS) (HarmonyOS: ArkTS)": {}, "实时音视频 (Linux C++) (Linux: C++)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/linux-cpp/client-sdk/release-notes.mdx": [{"url": "/real-time-video-ios-oc/client-sdk/error-code#3", "line_content": "新增 1002074、1002075、1002076、1002077、1002078、1002079、1002080 等错误码。开启强制登录鉴权后，如果 Token 错误时，会返回这些错误码，详情请参考 <a href=\"/real-time-video-ios-oc/client-sdk/error-code#3\" target=\"_blank\" rel=\"noopener noreferrer\">常见错误码</a> 中的详细解释和处理建议。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/linux-cpp/client-sdk/release-notes.mdx:1492"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/linux-cpp/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/linux-cpp/communication/using-token-authentication.mdx:256"}, {"url": "#使用-token", "line_content": "如需使用客户端生成 Token，请参考 [使用 Token](#使用-token)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/linux-cpp/communication/using-token-authentication.mdx:363"}, {"url": "#token-过期处理机制", "line_content": "若 Token 过期，请参考 [Token 过期时的处理方式](#token-过期处理机制) 进行处理。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/linux-cpp/communication/using-token-authentication.mdx:365"}]}}, "实时音视频 (Linux Java) (Linux: Java)": {}, "实时音视频 (React Native JS) (React Native: JS)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/rn-js/live-streaming/playing-stream-by-url.mdx": [{"url": "/real-time-video-rn/client-sdk/error-code#1004xxx-拉流相关的错误码", "line_content": "调用 [startPlayingStream](https://doc-zh.zego.im/unique-api/express-video-sdkhttps://doc-zh.zego.im/article/javascript_react-native/classes/_zegoexpressengine_.zegoexpressengine.html#startplayingstream) 接口开始拉流，拉流时如果出现错误，请参考 [常见错误码 - 1004xxx 拉流相关的错误码](/real-time-video-rn/client-sdk/error-code#1004xxx-拉流相关的错误码)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/rn-js/live-streaming/playing-stream-by-url.mdx:55"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/rn-js/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/rn-js/communication/using-token-authentication.mdx:250"}, {"url": "#使用-token", "line_content": "如需使用客户端生成 Token，请参考 [使用 Token](#使用-token)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/rn-js/communication/using-token-authentication.mdx:356"}, {"url": "#token-过期时的处理方式", "line_content": "若 Token 过期，请参考 [Token 过期时的处理方式](#token-过期时的处理方式) 进行处理。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/rn-js/communication/using-token-authentication.mdx:358"}]}}, "实时音视频 (Unity3D C#) (Unity3D: C#)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/video/screen-sharing.mdx": [{"url": "/real-time-video-android-java/video/screen-sharing#必选获取用户录制屏幕授权", "line_content": "1. 如需使用屏幕共享功能，请务必 [获取用户录制屏幕授权](/real-time-video-android-java/video/screen-sharing#必选获取用户录制屏幕授权)，否则将无法使用该功能。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/video/screen-sharing.mdx:61"}, {"url": "/real-time-video-ios-oc/video/screen-sharing#4", "line_content": "iOS 平台是基于苹果的 [Replaykit](https://developer.apple.com/documentation/ReplayKit) 框架实现屏幕录制，能够分享整个系统的屏幕内容。但需要当前 App （主 App 进程）额外提供一个 Extension 扩展组件（Extension 进程），用于录制屏幕，再结合 ZEGO Express SDK 相关 API 来实现屏幕共享功能，详情请参考 [屏幕共享（iOS）](/real-time-video-ios-oc/video/screen-sharing#4) 的 “实现流程” 说明。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/video/screen-sharing.mdx:119"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/live-streaming/playing-stream-by-url.mdx": [{"url": "/real-time-video-u3d-cs/client-sdk/error-code#1004xxx-拉流相关的错误码", "line_content": "拉流时，如果出现错误，请参考 [常见错误码 - 1004xxx 拉流相关的错误码](/real-time-video-u3d-cs/client-sdk/error-code#1004xxx-拉流相关的错误码)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/live-streaming/playing-stream-by-url.mdx:67"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/communication/using-token-authentication.mdx:248"}, {"url": "#使用-token", "line_content": "如需使用客户端生成 Token，请参考 [使用 Token](#使用-token)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/communication/using-token-authentication.mdx:354"}, {"url": "#token-过期时的处理方式", "line_content": "若 Token 过期，请参考 [Token 过期时的处理方式](#token-过期时的处理方式) 进行处理。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/communication/using-token-authentication.mdx:356"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/communication/pre-call-detection.mdx": [{"url": "/real-time-video-u3d-cs/quick-start/implementing-video-call#3_3", "line_content": "调用 [StartPreview](@StartPreview) 接口绑定摄像头预览画面的视图（详情请参考 [启用本地预览](/real-time-video-u3d-cs/quick-start/implementing-video-call#3_3)），在不推流的情况下启动视频采集并预览。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/u3d-cs/communication/pre-call-detection.mdx:121"}]}}, "实时音视频 (Unreal Engine C++) (Unreal Engine: C++)": {}, "实时音视频 (Web JS) (Web: JS)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/video/common-video-configuration.mdx": [{"url": "#如何选择视频的分辨率帧率码率", "line_content": "当通过摄像头采集源数据时，使用 “camera” 对象中的 “quality” 参数设置视频质量。该参数的组合值如下表，其中 1、2、3 为预设值，若取值为 4，则表示开发者可以自定义分辨率、帧率和码率。相关设置，请参考本文 [如何选择视频的分辨率/帧率/码率](#如何选择视频的分辨率帧率码率)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/video/common-video-configuration.mdx:57"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/communication/using-token-authentication.mdx:250"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/first-frame-event.mdx": [{"url": "./../quick-start/integrating-sdk.mdx#集成-sdk-2", "line_content": "- 已在项目中集成 ZEGO Express SDK，并实现了基本的音视频推拉流功能，详情请参考 [快速开始 - 集成](./../quick-start/integrating-sdk.mdx#集成-sdk-2) 和 [快速开始 - 实现流程](./../quick-start/implementing-video-call.mdx#实现流程)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/first-frame-event.mdx:19"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/safari-multiple-video-restriction.mdx": [{"url": "/real-time-video-web/best-practice/autoplay-policy#2_2", "line_content": "在取消静音前，需要用户交互过页面才能成功，详情请参考 [浏览器自动播放策略](/real-time-video-web/best-practice/autoplay-policy#2_2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/safari-multiple-video-restriction.mdx:70"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/debug-and-config.mdx": [{"url": "/console/project-info#2_2", "line_content": "[控制台 - 项目信息](/console/project-info#2_2) 。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/debug-and-config.mdx:35"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/multiplayer-video-call.mdx": [{"url": "/console/project-info#2_2", "line_content": "[控制台 - 项目信息](/console/project-info#2_2) 。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/multiplayer-video-call.mdx:55"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/framework/vue.mdx": [{"url": "/console/project-info#2_2", "line_content": "[控制台 - 项目信息](/console/project-info#2_2) 。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/framework/vue.mdx:74"}, {"url": "/real-time-video-web/quick-start/run-example-code#1_1", "line_content": "SDK 支持的浏览器兼容版本，请参考 [下载示例源码](/real-time-video-web/quick-start/run-example-code#1_1) 中的 “准备环境”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/framework/vue.mdx:132"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/framework/angular.mdx": [{"url": "/console/project-info#2_2", "line_content": "[控制台 - 项目信息](/console/project-info#2_2) 。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/framework/angular.mdx:69"}, {"url": "/real-time-video-web/quick-start/run-example-code#1_1", "line_content": "SDK 支持的浏览器兼容版本，请参考 [下载示例源码](/real-time-video-web/quick-start/run-example-code#1_1) 的 “准备环境”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/framework/angular.mdx:122"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/framework/react.mdx": [{"url": "/console/project-info#2_2", "line_content": "[控制台 - 项目信息](/console/project-info#2_2) 。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/framework/react.mdx:89"}, {"url": "/real-time-video-web/quick-start/run-example-code#1_1", "line_content": "SDK 支持的浏览器兼容版本，请参考 [下载示例源码](/real-time-video-web/quick-start/run-example-code#1_1) 的 “准备环境”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/best-practice/framework/react.mdx:155"}]}, "根路径链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/other/push-the-whiteboard.mdx": [{"url": "/super-board-ios/product-desc/overview", "line_content": "本文主要讲述如何利用 ZEGO Express SDK 的混流功能，将音视频流和 [ZegoSuperBoard](/super-board-ios/product-desc/overview) 内容合并成一路流，输出到第三方平台，例如微信、视频号等，从而达到更好的传播和营销效果。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/web/other/push-the-whiteboard.mdx:16"}]}}, "实时音视频 (Windows C#) (Windows: C#)": {}, "实时音视频 (Windows C++) (Windows: C++)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/video/custom-video-preprocessing.mdx": [{"url": "#1-开启自定义视频前处理", "line_content": "2. 调用 [enableCustomVideoProcessing](@enableCustomVideoProcessing) 接口，[开启自定义视频前处理](#1-开启自定义视频前处理) 功能。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/video/custom-video-preprocessing.mdx:45"}, {"url": "#2-获取原始视频数据进行视频前处理", "line_content": "3. [获取原始视频数据，进行视频前处理](#2-获取原始视频数据进行视频前处理)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/video/custom-video-preprocessing.mdx:47"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/other/local-media-recording.mdx": [{"url": "#方案四cdn-录制", "line_content": "<td><a href=\"#方案四cdn-录制\" >CDN 录制</a></td>", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/other/local-media-recording.mdx:54"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/live-streaming/playing-stream-by-url.mdx": [{"url": "/real-time-video-windows-cpp/client-sdk/error-code#1004xxx-拉流相关的错误码", "line_content": "拉流时，如果出现错误，请参考 [常见错误码 - 1004xxx 拉流相关的错误码](/real-time-video-windows-cpp/client-sdk/error-code#1004xxx-拉流相关的错误码)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/live-streaming/playing-stream-by-url.mdx:68"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/client-sdk/release-notes.mdx": [{"url": "/real-time-video-windows-cpp/video/screen-sharing#5_1", "line_content": "在桌面端使用屏幕共享时，可以为共享窗口添加一个高亮描边，并设置描边的颜色和宽度，提升共享窗口的识别度。详情请参考 [屏幕共享 - 描边](/real-time-video-windows-cpp/video/screen-sharing#5_1)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/client-sdk/release-notes.mdx:21"}, {"url": "/real-time-video-ios-oc/client-sdk/error-code#3", "line_content": "新增 1002074、1002075、1002076、1002077、1002078、1002079、1002080 等错误码。开启强制登录鉴权后，如果 Token 错误时，会返回这些错误码，详情请参考 <a href=\"/real-time-video-ios-oc/client-sdk/error-code#3\" target=\"_blank\" rel=\"noopener noreferrer\">常见错误码</a> 中的详细解释和处理建议。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/client-sdk/release-notes.mdx:1827"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/communication/using-token-authentication.mdx:276"}, {"url": "#使用-token", "line_content": "如需使用客户端生成 Token，请参考 [使用 Token](#使用-token)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/communication/using-token-authentication.mdx:428"}, {"url": "#token-过期时的处理方式", "line_content": "若 Token 过期，请参考 [Token 过期时的处理方式](#token-过期时的处理方式) 进行处理。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/communication/using-token-authentication.mdx:430"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/best-practice/integration-with-zego-effects-sdk.mdx": [{"url": "/real-time-video-macos-cpp/video/custom-video-capture#3-向-sdk-发送视频帧数据", "line_content": "在接收到自定义采集的 [onStart](@onStart) 回调后，开发者通过自定义采集获取视频数据，再调用 ZEGO Effects SDK 的相关接口，进行 AI 美颜处理（请参考 [美颜](/ai-effects-macos-c/guides/face-beautification)、[美型](/ai-effects-macos-c/guides/shape-retouch)、[背景分割](/ai-effects-macos-c/guides/background-segmentation)、[人脸检测](/ai-effects-macos-c/guides/face-detection)、[挂件](https://doc-zh.zego.im/faq/AIEffect_Stickers?product=Effects&platform=android)、[滤镜](/ai-effects-macos-c/guides/filters)），并将处理后的数据，返回给 ZEGO Express SDK（可参考 [自定义视频采集](/real-time-video-macos-cpp/video/custom-video-capture#3-向-sdk-发送视频帧数据) 中的 “3 向 SDK 发送视频帧数据”）。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/windows-cpp/best-practice/integration-with-zego-effects-sdk.mdx:210"}]}}, "实时音视频 (iOS Objective-C) (iOS: Objective-C)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/video/custom-video-preprocessing.mdx": [{"url": "#1-开启自定义视频前处理", "line_content": "2. 调用 [enableCustomVideoProcessing](@enableCustomVideoProcessing) 接口，[开启自定义视频前处理](#1-开启自定义视频前处理) 功能。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/video/custom-video-preprocessing.mdx:43"}, {"url": "#2-获取原始视频数据进行视频前处理", "line_content": "5. [获取原始视频数据，进行视频前处理](#2-获取原始视频数据进行视频前处理)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/video/custom-video-preprocessing.mdx:44"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/video/screen-sharing.mdx": [{"url": "/real-time-video-ios-oc/quick-start/implementing-video-call#导入-sdk", "line_content": "2. 在 Extension 中导入 ZEGO Express SDK，详情请参考 [快速开始 - 集成](/real-time-video-ios-oc/quick-start/implementing-video-call#导入-sdk) 中的 “导入 SDK”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/video/screen-sharing.mdx:150"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/other/local-media-recording.mdx": [{"url": "#方案四cdn-录制", "line_content": "<td><a href=\"#方案四cdn-录制\" >CDN 录制</a></td>", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/other/local-media-recording.mdx:50"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/live-streaming/cdn-stream-publishing-authentication.mdx": [{"url": "#生成鉴权密钥", "line_content": "- KEY：指您在 [生成鉴权密钥](#生成鉴权密钥) 中配置的加密 KEY。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/live-streaming/cdn-stream-publishing-authentication.mdx:67"}, {"url": "#生成-txtime", "line_content": "- txTime：指您在 [生成 txTime](#生成-txtime) 中生成的 txTime。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/live-streaming/cdn-stream-publishing-authentication.mdx:69"}, {"url": "#生成-wsabstime", "line_content": "- wsABStime：指您在 [生成 wsABStime](#生成-wsabstime) 中生成的 wsABStime，例如 65A006AA。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/live-streaming/cdn-stream-publishing-authentication.mdx:121"}, {"url": "#生成鉴权密钥", "line_content": "- KEY：指您在 [生成鉴权密钥](#生成鉴权密钥) 中配置的加密 KEY，例如 KEY123。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/live-streaming/cdn-stream-publishing-authentication.mdx:123"}, {"url": "#获取鉴权密钥", "line_content": "- KEY：指您在 [获取鉴权密钥](#获取鉴权密钥) 中获取的鉴权密钥。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/live-streaming/cdn-stream-publishing-authentication.mdx:167"}, {"url": "#生成-hwtime", "line_content": "- hwTime：指您在 [生成 hwTime](#生成-hwtime) 中生成的 hwTime。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/live-streaming/cdn-stream-publishing-authentication.mdx:169"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/client-sdk/release-notes.mdx": [{"url": "/real-time-video-ios-oc/client-sdk/error-code#3", "line_content": "新增 1002074、1002075、1002076、1002077、1002078、1002079、1002080 等错误码。开启强制登录鉴权后，如果 Token 错误时，会返回这些错误码，详情请参考 <a href=\"/real-time-video-ios-oc/client-sdk/error-code#3\" target=\"_blank\" rel=\"noopener noreferrer\">常见错误码</a> 中的详细解释和处理建议。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/client-sdk/release-notes.mdx:2263"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/communication/using-token-authentication.mdx:286"}, {"url": "#使用-token", "line_content": "如需使用客户端生成 Token，请参考 [使用 Token](#使用-token)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/communication/using-token-authentication.mdx:445"}, {"url": "#token-过期处理机制", "line_content": "若 Token 过期，请参考 [Token 过期时的处理方式](#token-过期处理机制) 进行处理。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/communication/using-token-authentication.mdx:447"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx": [{"url": "/zim-ios/send-and-receive-messages#create", "line_content": "- 在管理房间内的麦位之前，需要先初始化 ZIM SDK，并设置通知回调，以便监听 ZIM 事件，接口调用详情请参考 [即时通讯 - 实现基本消息收发](/zim-ios/send-and-receive-messages#create) 的 “2. 创建 ZIM 实例” 和 “3. 使用 EventHandler 协议”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:76"}, {"url": "/real-time-video-ios-oc/quick-start/implementing-video-call#初始化", "line_content": "- 在实现主体分割之前，需要先初始化 ZEGO Express SDK，同时设置通知回调，以便监听 Express 事件，接口调用请参考 [实时音视频 - 实现视频通话](/real-time-video-ios-oc/quick-start/implementing-video-call#初始化) 的 “初始化”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:77"}, {"url": "/real-time-video-ios-oc/quick-start/implementing-video-call#登录房间", "line_content": "- 为实现推流，用户需要先登录 RTC 房间，接口调用请参考 [实时音视频 - 实现视频通话](/real-time-video-ios-oc/quick-start/implementing-video-call#登录房间) 的 “登录房间”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:83"}, {"url": "/zim-ios/send-and-receive-messages#login", "line_content": "1. 登录 ZIM 服务，接口调用请参考 [即时通讯 - 实现基本消息收发](/zim-ios/send-and-receive-messages#login) 的 “登录 ZIM”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:85"}, {"url": "/zim-ios/guides/room/manage-rooms#创建房间、加入房间", "line_content": "2. 用户需要创建或加入 ZIM 房间，详情请参考 [即时通讯 - 房间管理](/zim-ios/guides/room/manage-rooms#创建房间、加入房间) 的 “创建房间、加入房间”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:86"}, {"url": "/zim-ios/guides/room/room-properties#获取房间属性", "line_content": "- 加入 ZIM 房间后，用户可通过查询房间所有属性了解房间内的麦位信息，接口调用详情请参考 [即时通讯 - 房间属性管理](/zim-ios/guides/room/room-properties#获取房间属性) 的 “获取房间属性”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:92"}, {"url": "/zim-ios/guides/room/room-properties#设置房间属性", "line_content": "- 用户如需上麦，可通过修改房间属性，修改麦位信息，接口调用详情请参考 [即时通讯 - 房间属性管理](/zim-ios/guides/room/room-properties#设置房间属性) 的 “设置房间属性”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:93"}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_7", "line_content": "1. 为在推流时传输经主体分割后的图像，需要设置透明通道，请参考 [实时音视频 - 主体分割](/real-time-video-ios-oc/video/object-segmentation#4_7) 的 “使用 Alpha 通道传输分割出的主体”，了解如何调用 [enableAlphaChannelVideoEncoder](@enableAlphaChannelVideoEncoder) 设置透明通道。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:100"}, {"url": "/real-time-video-ios-oc/video/common-video-configuration#设置镜像模式", "line_content": "2. 由于手机前置摄像头捕捉的画面与实际左右相反，需要开启屏幕镜像，以便预览或拉流时，获取正确方向画面，接口调用详情请参考 [实时音视频 - 常用视频配置](/real-time-video-ios-oc/video/common-video-configuration#设置镜像模式) 的 “设置镜像模式”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:102"}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_2", "line_content": "4. 如需预览本端的画面，需先将用于渲染的 View 的 backgroundColor 属性为 clearColor（透明色），接口调用详情请参考 [实时音视频 - 主体分割](/real-time-video-ios-oc/video/object-segmentation#4_2) 的 “对 view 进行特殊设置”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:106"}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_8", "line_content": "预览和推送本端画面，接口调用详情请参考 [实时音视频 - 主体分割](/real-time-video-ios-oc/video/object-segmentation#4_8) 的 “开始预览和推流”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:108"}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_5", "line_content": "5. 开启主体分割，接收分割结果，接口调用详情请参考 [实时音视频 - 主体分割](/real-time-video-ios-oc/video/object-segmentation#4_5) 的 “监听主体分割状态回调” 和 “使用主体分割实现多种业务功能”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:110"}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_2", "line_content": "1. 调用实时音视频接口将用于渲染的 View 的 backgroundColor 属性为 clearColor（透明色），接口调用详情请参考 [实时音视频 - 主体分割](/real-time-video-ios-oc/video/object-segmentation#4_2) 的 “对 view 进行特殊设置”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:124"}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_9", "line_content": "2. 拉取对方已实现主体分割的视频流，从而实现两个用户处在 “同一空间” 面对面对话的视觉效果。接口调用详情请参考 [实时音视频 - 主体分割](/real-time-video-ios-oc/video/object-segmentation#4_9) 的 “开始拉流”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:132"}, {"url": "/real-time-video-ios-oc/quick-start/implementing-video-call#5_2", "line_content": "3. 如需结束拉流，接口调用详情可参考 [实时音视频 - 实现视频通话](/real-time-video-ios-oc/quick-start/implementing-video-call#5_2) 的 “停止音视频通话”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:133"}, {"url": "/real-time-video-ios-oc/quick-start/implementing-video-call#5_2", "line_content": "1. 停止预览和推流，接口调用详情请参考 [实时音视频 - 实现视频通话](/real-time-video-ios-oc/quick-start/implementing-video-call#5_2) 的 “停止音视频通话”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:139"}, {"url": "/zim-ios/guides/room/manage-rooms#3_3", "line_content": "2. 离开 ZIM 房间，接口调用详情请参考 [即时通讯 - 房间管理](/zim-ios/guides/room/manage-rooms#3_3) 的 “离开房间”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:140"}, {"url": "/real-time-video-ios-oc/quick-start/implementing-video-call#5_2", "line_content": "3. 退出 RTC 房间，接口调用详情请参考 [实时音视频 - 实现视频通话](/real-time-video-ios-oc/quick-start/implementing-video-call#5_2) 的 “停止音视频通话”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/best-practice/object-segmentation/implementation.mdx:141"}]}, "根路径链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/other/push-the-whiteboard.mdx": [{"url": "/super-board-ios/product-desc/overview", "line_content": "本文主要讲述如何利用 ZEGO Express SDK 的混流功能，将音视频流和 [ZegoSuperBoard](/super-board-ios/product-desc/overview) 内容合并成一路流，输出到第三方平台，例如微信、视频号等，从而达到更好的传播和营销效果。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-oc/other/push-the-whiteboard.mdx:16"}]}}, "实时音视频 (iOS Swift) (iOS: Swift)": {"根路径链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-swift/client-sdk/download-sdk.mdx": [{"url": "/super-board-ios/download-sdk", "line_content": "- 如果需要使用“含白板功能”的 SDK，可到 [超级白板 - 下载 SDK](/super-board-ios/download-sdk) 中下载“超级白板 SDK”，该 SDK 中已包含 ZegoExpress-Video SDK（含白板功能），无需单独下载。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/ios-swift/client-sdk/download-sdk.mdx:16"}]}}, "实时音视频 (macOS C++) (macOS: C++)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/macos-cpp/video/custom-video-preprocessing.mdx": [{"url": "#1-开启自定义视频前处理", "line_content": "2. 调用 [enableCustomVideoProcessing](@enableCustomVideoProcessing) 接口，[开启自定义视频前处理](#1-开启自定义视频前处理) 功能。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/macos-cpp/video/custom-video-preprocessing.mdx:49"}, {"url": "#2-获取原始视频数据进行视频前处理", "line_content": "3. [获取原始视频数据，进行视频前处理](#2-获取原始视频数据进行视频前处理)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/macos-cpp/video/custom-video-preprocessing.mdx:51"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/macos-cpp/client-sdk/release-notes.mdx": [{"url": "/real-time-video-ios-oc/client-sdk/error-code#3", "line_content": "新增 1002074、1002075、1002076、1002077、1002078、1002079、1002080 等错误码。开启强制登录鉴权后，如果 Token 错误时，会返回这些错误码，详情请参考 <a href=\"/real-time-video-ios-oc/client-sdk/error-code#3\" target=\"_blank\" rel=\"noopener noreferrer\">常见错误码</a> 中的详细解释和处理建议。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/macos-cpp/client-sdk/release-notes.mdx:1698"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/macos-cpp/best-practice/integration-with-zego-effects-sdk.mdx": [{"url": "/real-time-video-macos-cpp/video/custom-video-capture#3-向-sdk-发送视频帧数据", "line_content": "在接收到自定义采集的 [onStart](@onStart) 回调后，开发者通过自定义采集获取视频数据，再调用 ZEGO Effects SDK 的相关接口，进行 AI 美颜处理（请参考 [美颜](/ai-effects-macos-c/guides/face-beautification)、[美型](/ai-effects-macos-c/guides/shape-retouch)、[背景分割](/ai-effects-macos-c/guides/background-segmentation)、[人脸检测](/ai-effects-macos-c/guides/face-detection)、[挂件](https://doc-zh.zego.im/faq/AIEffect_Stickers?product=Effects&platform=android)、[滤镜](/ai-effects-macos-c/guides/filters)），并将处理后的数据，返回给 ZEGO Express SDK（可参考 [自定义视频采集](/real-time-video-macos-cpp/video/custom-video-capture#3-向-sdk-发送视频帧数据) 中的 “3 向 SDK 发送视频帧数据”）。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/macos-cpp/best-practice/integration-with-zego-effects-sdk.mdx:218"}]}}, "实时音视频 (macOS Objective-C) (macOS: Objective-C)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/macos-oc/client-sdk/release-notes.mdx": [{"url": "/real-time-video-ios-oc/client-sdk/error-code#3", "line_content": "新增 1002074、1002075、1002076、1002077、1002078、1002079、1002080 等错误码。开启强制登录鉴权后，如果 Token 错误时，会返回这些错误码，详情请参考 <a href=\"/real-time-video-ios-oc/client-sdk/error-code#3\" target=\"_blank\" rel=\"noopener noreferrer\">常见错误码</a> 中的详细解释和处理建议。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/macos-oc/client-sdk/release-notes.mdx:1802"}]}}, "实时音视频 (macOS Swift) (macOS: Swift)": {}, "实时音视频 (uni-app JS) (uni-app: JS)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/uni-app/live-streaming/playing-stream-by-url.mdx": [{"url": "/real-time-video-uniapp/client-sdk/error-code#1004xxx-拉流相关的错误码", "line_content": "拉流时，如果出现错误，请参考 [常见错误码 - 1004xxx 拉流相关的错误码](/real-time-video-uniapp/client-sdk/error-code#1004xxx-拉流相关的错误码)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/uni-app/live-streaming/playing-stream-by-url.mdx:69"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/uni-app/communication/publish-multi-streams.mdx": [{"url": "/real-time-video-android-java/quick-start/implementing-video-call#3_1", "line_content": "请参考 [快速开始 - 实现流程](/real-time-video-android-java/quick-start/implementing-video-call#3_1) 的 “3.1 创建引擎”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/uni-app/communication/publish-multi-streams.mdx:42"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/uni-app/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/uni-app/communication/using-token-authentication.mdx:255"}, {"url": "#使用-token", "line_content": "如需使用客户端生成 Token，请参考 [使用 Token](#使用-token)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/uni-app/communication/using-token-authentication.mdx:356"}, {"url": "#token-过期时的处理方式", "line_content": "若 Token 过期，请参考 [Token 过期时的处理方式](#token-过期时的处理方式) 进行处理。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/uni-app/communication/using-token-authentication.mdx:358"}]}}, "实时音视频 (客户端 API) (All)": {}, "实时音视频 (小程序 JS) (小程序: JS)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/mini-program/other/implementing-video-call-with-wechat-native-components.mdx": [{"url": "/real-time-video-miniprogram/other/implementing-video-call-with-wechat-native-components#我在房间内的连接状态变化通知", "line_content": "您可以调用 SDK 的 [loginRoom ](@loginRoom) 接口，传入房间 ID 参数 “roomID”、“token” 和用户参数 “user”，登录房间。您可通过监听 [roomStateUpdate ](@roomStateUpdate) 回调实时监控自己在本房间内的连接状态，具体请参考 [常见通知回调](/real-time-video-miniprogram/other/implementing-video-call-with-wechat-native-components#我在房间内的连接状态变化通知) 中的“我在房间内的连接状态变化通知”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/mini-program/other/implementing-video-call-with-wechat-native-components.mdx:143"}, {"url": "#房间内流状态变更的通知", "line_content": "您可通过监听 [publisherStateUpdate ](@publisherStateUpdate) 回调知晓是否成功推送音视频，具体回调设置请参考 [常见通知回调](#房间内流状态变更的通知) 中的“房间内流状态变更的通知”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/mini-program/other/implementing-video-call-with-wechat-native-components.mdx:202"}, {"url": "#用户拉取音视频流的状态通知", "line_content": "您可通过监听 [playerStateUpdate ](@playerStateUpdate) 回调知晓是否成功拉取音视频，具体请参考 [常见通知回调](#用户拉取音视频流的状态通知) 中的“用户拉取音视频流的状态通知”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/mini-program/other/implementing-video-call-with-wechat-native-components.mdx:231"}, {"url": "#房间内流状态变更的通知", "line_content": "远端用户推送的 “streamID” 可以从 [roomStreamUpdate](@roomStreamUpdate) 回调中获得，具体回调设置请参考 [常见通知回调](#房间内流状态变更的通知) 中的“房间内流状态变更的通知”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/mini-program/other/implementing-video-call-with-wechat-native-components.mdx:233"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/mini-program/communication/using-token-authentication.mdx": [{"url": "/console/development-assistance/temporary-token#2", "line_content": "- **Token 校验**：当您通过自己的服务端生成 Token 后，若需要校验 Token 正确性，可以使用 [ZEGO 控制台的自助校验工具](https://console.zego.im/devAssistance)，详情请参考开发辅助文档的 [Token 校验章节](/console/development-assistance/temporary-token#2)。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/mini-program/communication/using-token-authentication.mdx:232"}]}}, "实时音视频 (服务端 API) (Server)": {"锚点链接无效": {"/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/server/api-reference/moderation/start-censor-audio.mdx": [{"url": "/console/cloud-market/shumei-moderation#3", "line_content": "如果您需要将此次审核任务的审核结果，通过其他的回调地址抛出，可以通过本参数实现。如果不需要，可以在 ZEGO 控制台配置统一的回调地址，详情请参考 [控制台 - 云市场 - 数美内容审核](/console/cloud-market/shumei-moderation#3)。</td>", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/server/api-reference/moderation/start-censor-audio.mdx:74"}, {"url": "/console/cloud-market/shumei-moderation#3", "line_content": "如果您需要将此次审核任务的审核状态，通过其他的回调地址抛出，可以通过本参数实现。如果不需要，可以在 ZEGO 后台配置统一的回调地址，详情请参考 [控制台 - 云市场 - 数美内容审核](/console/cloud-market/shumei-moderation#3)。</td>", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/server/api-reference/moderation/start-censor-audio.mdx:81"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/server/api-reference/moderation/start-censor-video.mdx": [{"url": "/console/cloud-market/shumei-moderation#3", "line_content": "| └ ResultCallbackUrl | String | 否 | 自定义审核结果的回调地址。<br/>如果您需要将此次审核任务的审核结果，通过其他的回调地址抛出，可以通过本参数实现。如果不需要，可以在 ZEGO 控制台配置统一的回调地址，详情请参考 [控制台 - 云市场 - 数美内容审核](/console/cloud-market/shumei-moderation#3)。 |", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/server/api-reference/moderation/start-censor-video.mdx:59"}, {"url": "/console/cloud-market/shumei-moderation#3", "line_content": "| └ StatusCallbackUrl | String | 否 | 自定义审核状态回调地址。<br/>如果您需要将此次审核任务的审核状态，通过其他的回调地址抛出，可以通过本参数实现。如果不需要，可以在 ZEGO 后台配置统一的回调地址，详情请参考 [控制台 - 云市场 - 数美内容审核](/console/cloud-market/shumei-moderation#3)。 |", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/server/api-reference/moderation/start-censor-video.mdx:60"}, {"url": "/console/cloud-market/shumei-moderation#3", "line_content": "| └ ResultCallbackUrl | String | 否 | 自定义审核结果的回调地址。<br/>如果您需要将此次审核任务的审核结果，通过其他的回调地址抛出，可以通过本参数实现。如果不需要，可以在 ZEGO 控制台配置统一的回调地址，详情请参考 [控制台 - 云市场 - 数美内容审核](/console/cloud-market/shumei-moderation#3)。 |", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/server/api-reference/moderation/start-censor-video.mdx:66"}, {"url": "/console/cloud-market/shumei-moderation#3", "line_content": "| └ StatusCallbackUrl | String | 否 | 自定义审核状态回调地址。<br/>如果您需要将此次审核任务的审核状态，通过其他的回调地址抛出，可以通过本参数实现。如果不需要，可以在 ZEGO 后台配置统一的回调地址，详情请参考 [控制台 - 云市场 - 数美内容审核](/console/cloud-market/shumei-moderation#3)。 |", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/server/api-reference/moderation/start-censor-video.mdx:67"}], "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/server/callback/receiving-callback.mdx": [{"url": "/console-old/project-management#4", "line_content": "- **2021-11-16** 及之前注册 [ZEGO 控制台](https://console.zego.im) 的用户，请参考 [控制台（旧版） - 项目管理](/console-old/project-management#4) 中的 “高级配置”。", "file_with_line": "/Users/<USER>/Desktop/shy/uikit_docs/core_products/real-time-voice-video/zh/server/callback/receiving-callback.mdx:56"}]}}}, "urls_by_category": {"anchor": [{"url": "/console/development-assistance/temporary-token#2", "count": 12}, {"url": "#使用-token", "count": 10}, {"url": "/real-time-video-ios-oc/client-sdk/error-code#3", "count": 6}, {"url": "/console/cloud-market/shumei-moderation#3", "count": 6}, {"url": "#token-过期处理机制", "count": 5}, {"url": "#token-过期时的处理方式", "count": 5}, {"url": "/console/project-info#2_2", "count": 5}, {"url": "#1-开启自定义视频前处理", "count": 4}, {"url": "#2-获取原始视频数据进行视频前处理", "count": 4}, {"url": "#方案四cdn-录制", "count": 3}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#5_2", "count": 3}, {"url": "/real-time-video-web/quick-start/run-example-code#1_1", "count": 3}, {"url": "/real-time-video-ios-oc/quick-start/implementing-video-call#5_2", "count": 3}, {"url": "/zim-android/guides/room/room-properties#获取房间属性", "count": 2}, {"url": "/real-time-video-android-java/video/object-segmentation#4_2", "count": 2}, {"url": "/real-time-video-android-java/video/screen-sharing#必选获取用户录制屏幕授权", "count": 2}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#3_1", "count": 2}, {"url": "/real-time-video-macos-cpp/video/custom-video-capture#3-向-sdk-发送视频帧数据", "count": 2}, {"url": "#生成鉴权密钥", "count": 2}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_2", "count": 2}, {"url": "#房间内流状态变更的通知", "count": 2}, {"url": "/real-time-video-android-java/video/custom-video-capture#4_1", "count": 1}, {"url": "/real-time-video-android-java/video/custom-video-capture#开启自定义视频采集", "count": 1}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#设置权限", "count": 1}, {"url": "/real-time-video-android-java/client-sdk/error-code#1004xxx-拉流相关的错误码", "count": 1}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#Integration_3", "count": 1}, {"url": "/real-time-video-android-java/video/custom-video-capture#4_3", "count": 1}, {"url": "/zim-android/send-and-receive-messages#create", "count": 1}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#4_1", "count": 1}, {"url": "/zim-android/send-and-receive-messages#login", "count": 1}, {"url": "/zim-android/guides/room/manage-rooms#创建房间加入房间", "count": 1}, {"url": "/real-time-video-android-java/video/object-segmentation#4_7", "count": 1}, {"url": "/real-time-video-android-java/video/common-video-configuration#4_3", "count": 1}, {"url": "/real-time-video-android-java/video/object-segmentation#4_8", "count": 1}, {"url": "/real-time-video-android-java/video/object-segmentation#4_5", "count": 1}, {"url": "/real-time-video-android-java/video/object-segmentation#4_9", "count": 1}, {"url": "/zim-android/guides/room/manage-rooms#3_3", "count": 1}, {"url": "/real-time-video-cocos-creator/quick-start/implementing-video-call#常见问题", "count": 1}, {"url": "/real-time-video-flutter/quick-start/implementing-video-call#设置权限", "count": 1}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#3_3", "count": 1}, {"url": "/real-time-video-android-java/quick-start/implementing-video-call#3_4", "count": 1}, {"url": "/real-time-video-flutter/video/screen-sharing#highlight", "count": 1}, {"url": "/real-time-video-rn/client-sdk/error-code#1004xxx-拉流相关的错误码", "count": 1}, {"url": "/real-time-video-ios-oc/video/screen-sharing#4", "count": 1}, {"url": "/real-time-video-u3d-cs/client-sdk/error-code#1004xxx-拉流相关的错误码", "count": 1}, {"url": "/real-time-video-u3d-cs/quick-start/implementing-video-call#3_3", "count": 1}, {"url": "#如何选择视频的分辨率帧率码率", "count": 1}, {"url": "./../quick-start/integrating-sdk.mdx#集成-sdk-2", "count": 1}, {"url": "/real-time-video-web/best-practice/autoplay-policy#2_2", "count": 1}, {"url": "/real-time-video-windows-cpp/client-sdk/error-code#1004xxx-拉流相关的错误码", "count": 1}, {"url": "/real-time-video-windows-cpp/video/screen-sharing#5_1", "count": 1}, {"url": "/real-time-video-ios-oc/quick-start/implementing-video-call#导入-sdk", "count": 1}, {"url": "#生成-txtime", "count": 1}, {"url": "#生成-wsabstime", "count": 1}, {"url": "#获取鉴权密钥", "count": 1}, {"url": "#生成-hwtime", "count": 1}, {"url": "/zim-ios/send-and-receive-messages#create", "count": 1}, {"url": "/real-time-video-ios-oc/quick-start/implementing-video-call#初始化", "count": 1}, {"url": "/real-time-video-ios-oc/quick-start/implementing-video-call#登录房间", "count": 1}, {"url": "/zim-ios/send-and-receive-messages#login", "count": 1}, {"url": "/zim-ios/guides/room/manage-rooms#创建房间、加入房间", "count": 1}, {"url": "/zim-ios/guides/room/room-properties#获取房间属性", "count": 1}, {"url": "/zim-ios/guides/room/room-properties#设置房间属性", "count": 1}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_7", "count": 1}, {"url": "/real-time-video-ios-oc/video/common-video-configuration#设置镜像模式", "count": 1}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_8", "count": 1}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_5", "count": 1}, {"url": "/real-time-video-ios-oc/video/object-segmentation#4_9", "count": 1}, {"url": "/zim-ios/guides/room/manage-rooms#3_3", "count": 1}, {"url": "/real-time-video-uniapp/client-sdk/error-code#1004xxx-拉流相关的错误码", "count": 1}, {"url": "/real-time-video-miniprogram/other/implementing-video-call-with-wechat-native-components#我在房间内的连接状态变化通知", "count": 1}, {"url": "#用户拉取音视频流的状态通知", "count": 1}, {"url": "/console-old/project-management#4", "count": 1}], "internal": {"anchor": [], "non_anchor": [{"url": "/super-board-ios/product-desc/overview", "count": 4}, {"url": "/super-board-android/download-sdk", "count": 1}, {"url": "/super-board-ios/download-sdk", "count": 1}]}, "external": {"anchor": [], "non_anchor": [], "old-doc": [{"url": "https://doc-zh.zego.im/article/19494", "count": 2}, {"url": "https://doc-zh.zego.im/article/javascript_react-native/classes/_zegoexpressdefines_.zegomediaplayer.html#loadresource", "count": 2}, {"url": "https://doc-zh.zego.im/article/12107", "count": 2}, {"url": "https://doc-zh.zego.im/article/19011", "count": 2}, {"url": "https://doc-zh.zego.im/article/6213", "count": 2}, {"url": "https://doc-zh.zego.im/article/10363", "count": 2}, {"url": "https://doc-zh.zego.im/article/dart_flutter/zego_express_engine/ZegoMediaPlayer/loadResource.html", "count": 1}, {"url": "https://doc-zh.zego.im/article/16142", "count": 1}, {"url": "https://doc-zh.zego.im/article/8665", "count": 1}, {"url": "https://doc-zh.zego.im/article/21309#2", "count": 1}, {"url": "https://doc-zh.zego.im/article/15533", "count": 1}, {"url": "https://doc-zh.zego.im/article/14271", "count": 1}, {"url": "https://doc-zh.zego.im/article/3234", "count": 1}, {"url": "https://doc-zh.zego.im/article/8620", "count": 1}, {"url": "https://doc-zh.zego.im/article/3546", "count": 1}, {"url": "https://doc-zh.zego.im/article/19492#13", "count": 1}, {"url": "https://doc-zh.zego.im/article/12107#2_2", "count": 1}, {"url": "https://doc-zh.zego.im/article/18429", "count": 1}, {"url": "https://doc-zh.zego.im/article/13948", "count": 1}, {"url": "https://doc-zh.zego.im/article/8663", "count": 1}, {"url": "https://doc-zh.zego.im/article/6435", "count": 1}, {"url": "https://doc-zh.zego.im/article/3126", "count": 1}, {"url": "https://doc-zh.zego.im/article/8660", "count": 1}, {"url": "https://doc-zh.zego.im/article/8668", "count": 1}, {"url": "https://doc-zh.zego.im/article/javascript_uni-app/classes/_zegoexpressdefines_.zegomediaplayer.html#loadresource", "count": 1}, {"url": "https://doc-zh.zego.im/article/overview?key=ExpressVideoSDK&platform=web&language=javascript", "count": 1}, {"url": "https://doc-zh.zego.im/article/19450", "count": 1}, {"url": "https://doc-zh.zego.im/article/19484", "count": 1}, {"url": "https://doc-zh.zego.im/article/19451", "count": 1}, {"url": "https://doc-zh.zego.im/article/19485", "count": 1}, {"url": "https://doc-zh.zego.im/article/19487", "count": 1}, {"url": "https://doc-zh.zego.im/article/19486", "count": 1}, {"url": "https://doc-zh.zego.im/article/19488", "count": 1}, {"url": "https://doc-zh.zego.im/article/19489", "count": 1}, {"url": "https://doc-zh.zego.im/article/19490", "count": 1}, {"url": "https://doc-zh.zego.im/article/20527", "count": 1}, {"url": "https://doc-zh.zego.im/article/19491", "count": 1}, {"url": "https://doc-zh.zego.im/article/19707", "count": 1}, {"url": "https://doc-zh.zego.im/article/19492", "count": 1}, {"url": "https://doc-zh.zego.im/article/19493", "count": 1}, {"url": "https://doc-zh.zego.im/article/19495", "count": 1}, {"url": "https://doc-zh.zego.im/article/20678", "count": 1}, {"url": "https://doc-zh.zego.im/article/19496", "count": 1}, {"url": "https://doc-zh.zego.im/article/19498", "count": 1}, {"url": "https://doc-zh.zego.im/article/19497", "count": 1}, {"url": "https://doc-zh.zego.im/article/19500", "count": 1}, {"url": "https://doc-zh.zego.im/article/2708", "count": 1}, {"url": "https://doc-zh.zego.im/article/2879", "count": 1}, {"url": "https://doc-zh.zego.im/article/2881", "count": 1}, {"url": "https://doc-zh.zego.im/article/2884", "count": 1}, {"url": "https://doc-zh.zego.im/article/14192", "count": 1}, {"url": "https://doc-zh.zego.im/article/1287", "count": 1}]}, "relative": {"anchor": [], "non_anchor": []}, "other": []}}