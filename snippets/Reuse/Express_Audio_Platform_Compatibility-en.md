实时语音支持 iOS、Android、Windows、macOS、HarmonyOS、Web、小程序并支持平台间互通，具体的兼容性要求见下表。
<table>
  <colgroup>
    <col width="20%">
    <col width="57%">
    <col width="23%">
  </colgroup>
<tbody><tr>
<th>平台</th>
<th>支持版本</th>
<th>支持架构</th>
</tr>
<tr>
<td>iOS</td>
<td>9.0 或以上版本</td>
<td><ul><li>arm64</li><li>armv7</li><li>x86_64（模拟器）</li><li>arm64（模拟器）</li><li>x86_64（Mac Catalyst）</li><li>arm64（Mac Catalyst）</li></ul></td>
</tr>
<tr>
<td>Android</td>
<td>4.4 或以上版本</td>
<td><ul><li>arm64-v8a</li><li>armeabi-v7a</li><li>x86</li><li>x86_64</li></ul></td>
</tr>
<tr>
<td>Windows</td>
<td>Windows 7 或以上版本</td>
<td><ul><li>x86</li><li>x64</li></ul></td>
</tr>
<tr>
<td>Linux</td>
<td><ul><li>Ubuntu 16.04 或以上版本</li><li>CentOS</li></ul></td>
<td>x86_64</td>
</tr>
<tr>
<td>HarmonyOS</td>
<td>HarmonyOS 2.0.0 或以上版本</td>
<td><ul><li>arm64-v8a</li><li>x86_64</li></ul></td>
</tr>
<tr>
<td>macOS</td>
<td>10.11 或以上版本</td>
<td><ul><li>x86_64</li><li>arm64</li></ul></td>
</tr>
<tr>
<td>Web</td>
<td><ul><li>Chrome 58 或以上版本</li><li>Firefox 56 或以上版本</li><li>Safari 11 或以上版本</li><li>Opera 45 或以上版本</li><li>QQ 浏览器 Windows 10.1 或以上版本、macOS 4.4 或以上版本</li><li>360 安全浏览器极速模式</li></ul>&nbsp;&nbsp;<p>更多兼容性要求，请参考 <a target="_blank" href="/real-time-video-web/introduction/browser-restrictions">限制说明 - 浏览器兼容性说明</a>。</p></td>
<td>-</td>
</tr>
<tr>
<td>小程序</td>
<td>微信小程序基础库 1.7.0 或以上版本</td>
<td>-</td>
</tr>
<tr>
<td>Flutter</td>
<td><ul><li>Flutter 1.12 或以上版本</li><li>iOS 9.0 或以上版本</li><li>Android 4.4 或以上版本
</li><li>Windows 7 或以上版本（注意：目前 SDK 在 Windows 上仅支持音频功能，暂不支持视频功能）</li></ul></td>
<td>-</td>
</tr>
<tr>
<td>Electron</td>
<td><ul><li>Electron 5.0.8 或以上版本</li><li>Windows 7 及以上 / macOS 10.11 及以上操作系统</li></ul></td>
<td>-</td>
</tr>
<tr>
<td>uni-app</td>
<td><ul><li>HBuilderX 3.0.0 或以上版本</li><li>iOS 9.0 或以上版本</li><li>Android 4.4 或以上版本</li></ul></td>
<td>-</td>
</tr>
<tr>
<td>Unity3D</td>
<td><ul><li>Unity 2018.4.21f1 或以上版本</li><li>Android：Android 4.4 或以上版本</li><li>iOS：Xcode 13.0 或以上版本，iOS 9.0 或以上版本</li><li>macOS：macOS 10.11 或以上版本</li><li>Windows：Windows 7 或以上版本</li></ul></td>
<td>-</td>
</tr>
<tr>
<td>React Native</td>
<td><ul><li>React Native 0.60.0 或以上版本</li><li>iOS 9.0 或以上版本</li><li>Android 版本不低于 4.0.3</li></ul></td>
<td>-</td>
</tr>
</tbody></table>
