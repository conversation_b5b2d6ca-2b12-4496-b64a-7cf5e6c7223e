# Migrating to ZEGOCLOUD Maven

1. Add the `jitpack` configuration.

- If your Android Gradle Plugin is **7.1.0 or later**: enter your project's root directory, open the `settings.gradle` file to add the jitpack to `dependencyResolutionManagement` > `repositories` like this:

    ``` groovy
    dependencyResolutionManagement {
        repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
        repositories {
            google()
            mavenCentral()
            maven { url 'https://maven.zego.im' }  // <- Add this line.
            maven { url 'https://www.jitpack.io' }
        }
    }
    ```

    <Warning title="Warning">
    If you can't find the above fields in `settings.gradle`, it's probably because your Android Gradle Plugin version is lower than v7.1.0.

    For more details, see [Android Gradle Plugin Release Note v7.1.0](https://developer.android.com/studio/releases/gradle-plugin#settings-gradle).
    </Warning>

- If your Android Gradle Plugin is **earlier than 7.1.0**: enter your project's root directory, open the `build.gradle` file to add the jitpack to `allprojects`->`repositories` like this:

    ```groovy
    allprojects {
        repositories {
            google()
            mavenCentral()
            maven { url 'https://maven.zego.im' }   // <- Add this line.
            maven { url "https://jitpack.io" }
        }
    }
    ```
