import Content from "/snippets/uikit/AndroidEnviromentRequirementEn.md"

# Quick start

- - -


<Video src="https://www.youtube.com/embed/HqzoiKZF_lM"/>

## Prepare the environment

<Content/>

## Integrate the SDK

1. **Add ZegoUIKitPrebuiltVideoConference as dependencies**

    a. Add the `jitpack` configuration.

    If your Android Gradle Plugin is **7.1.0 or later**: enter your project's root directory, open the `settings.gradle` file to add the jitpack to `dependencyResolutionManagement` > `repositories` like this:

    ``` groovy settings.gradle {6-7}
    dependencyResolutionManagement {
       repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
       repositories {
          google()
          mavenCentral()
          maven { url 'https://maven.zego.im' }   // <- Add this line.
          maven { url 'https://www.jitpack.io' } // <- Add this line.
       }
    }
    ```

    <Warning title="Warning">

    If you can't find the above fields in `settings.gradle`, it's probably because your Android Gradle Plugin version is lower than v7.1.0.

    For more details, see [Android Gradle Plugin Release Note v7.1.0](https://developer.android.com/studio/releases/gradle-plugin#settings-gradle).
    </Warning>

    If your Android Gradle Plugin is **earlier than 7.1.0**: enter your project's root directory, open the `build.gradle` file to add the jitpack to `allprojects`->`repositories` like this:

    ```groovy build.gradle {5-6}
    allprojects {
        repositories {
            google()
            mavenCentral()
            maven { url 'https://maven.zego.im' }   // <- Add this line.
            maven { url "https://jitpack.io" }  // <- Add this line.
        }
    }
    ```

    b. Modify your app-level `build.gradle` file:
    ```groovy build.gradle {3}
    dependencies {
        ...
        implementation 'com.github.ZEGOCLOUD:zego_uikit_prebuilt_video_conference_android:+'    // Add this line in your module-level build.gradle file's dependencies, usually named [app].
    }
    ```

2. **Using the ZegoUIKitPrebuiltVideoConferenceFragment in your project**

    1. Go to [ZEGOCLOUD Admin Console](https://console.zegocloud.com/), get the `appID` and `appSign` of your project.
    2. Specify the `userID` and `userName` for connecting the Video Conference Kit service.
    3. Create a `conferenceID` that represents the conference you want to start.

    <Note title="Note">
    - `userID` and `conferenceID` can only contain numbers, letters, and underlines (_).
    - Using the same `conferenceID` will enter the same video conference.
    </Note>

    ```java
    public class ConferenceActivity extends AppCompatActivity {

        @Override
        protected void onCreate(Bundle savedInstanceState) {
            super.onCreate(savedInstanceState);
            setContentView(R.layout.activity_conference);

            addFragment();
        }

        public void addFragment() {
            long appID = yourAppID;
            String appSign = yourAppSign;

            String conferenceID = conferenceID;
            String userID = userID;
            String userName = userName;

            ZegoUIKitPrebuiltVideoConferenceConfig config = new ZegoUIKitPrebuiltVideoConferenceConfig();
            ZegoUIKitPrebuiltVideoConferenceFragment fragment = ZegoUIKitPrebuiltVideoConferenceFragment.newInstance(appID, appSign, userID, userName,conferenceID,config);

            getSupportFragmentManager().beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .commitNow();
        }
    }
    ```

    Modify the auto-created `activity_conference.xml` file:
    ```xml activity_conference.xml
    <?xml version="1.0" encoding="utf-8"?>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
      android:id="@+id/fragment_container"
      android:layout_width="match_parent"
      android:layout_height="match_parent">

    </androidx.constraintlayout.widget.ConstraintLayout>
    ```

Now, you can start a video conference by starting your `ConferenceActivity`.


## Run & Test

Now you have finished all the steps!

You can simply click the **Run** on Android Studio to run and test your App on your device.


## Related guide

[Custom prebuilt UI](./customize-prebuilt-features/01-overview.mdx)


## Resources


<Button primary-color="NavyBlue" target="_blank" href="https://github.com/ZEGOCLOUD/zego_uikit_prebuilt_video_conference_example_android">Sample Code</Button>

Click here to get the complete sample code.
