import UIKitInvitationPrerequisitesZIMSignaling from "/snippets/uikit/UIKit_Invitation_Prerequisites_ZIMSignaling-en.md";
import Content from "/snippets/uikit/AndroidEnviromentRequirementEn.md"

# Quick start (with cohosting)

<Frame width="512" height="auto" caption="">
  <img src="https://storage.zego.im/sdk-doc/Pics/ZegoUIKit/Flutter/live_with_cohosting2.gif" />
</Frame>

## Prerequisites

<UIKitInvitationPrerequisitesZIMSignaling/>

## Prepare the environment

<Content/>

## Integrate the SDK

### Add ZegoUIKitPrebuiltLiveStreaming as dependencies

1. Add the `jitpack` configuration.
- If your Android Gradle Plugin is **7.1.0 or later**: enter your project's root directory, open the `settings.gradle` file to add the jitpack to `dependencyResolutionManagement` > `repositories` like this:

``` groovy
dependencyResolutionManagement {
   repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
   repositories {
      google()
      mavenCentral()
      maven { url 'https://maven.zego.im' }   // <- Add this line.
      maven { url 'https://www.jitpack.io' } // <- Add this line.
   }
}
```

<Warning title="Warning">

If you can't find the above fields in `settings.gradle`, it's probably because your Android Gradle Plugin version is lower than v7.1.0.

For more details, see [Android Gradle Plugin Release Note v7.1.0](https://developer.android.com/studio/releases/gradle-plugin#settings-gradle).
</Warning>

- If your Android Gradle Plugin is **earlier than 7.1.0**: enter your project's root directory, open the `build.gradle` file to add the jitpack to `allprojects`->`repositories` like this:

```groovy
allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.zego.im' }   // <- Add this line.
        maven { url "https://jitpack.io" }  // <- Add this line.
    }
}
```

2. Modify your app-level `build.gradle` file:

```groovy
dependencies {
    ...
    implementation 'com.github.ZEGOCLOUD:zego_uikit_prebuilt_live_streaming_android:+'    // Add this line to your module-level build.gradle file's dependencies, usually named [app].
    implementation 'com.github.ZEGOCLOUD:zego_uikit_signaling_plugin_android:+'  // Add this line to your module-level build.gradle file's dependencies, usually named [app].
}
```

### Using the Live Streaming Kit


- Specify the `userID` and `userName` for connecting the Live Streaming Kit service.
- `liveID` represents the live streaming you want to start or watch (only supports single-host live streaming for now).

<Note title="Note">

- `userID`, `userName`, and `liveID` can only contain numbers, letters, and underlines (_).
- Using the same `liveID` will enter the same live streaming.
</Note>

<Warning title="Warning">

With the same `liveID`, only one user can enter the live stream as host. Other users need to enter the live stream as the audience.
</Warning>


<CodeGroup>

```java Java
public class LiveActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_call);

        addFragment();
    }

    public void addFragment() {
        long appID = yourAppID;
        String appSign = yourAppSign;
        String userID = yourUserID;
        String userName = yourUserName;

        boolean isHost = getIntent().getBooleanExtra("host", false);
        String yourLiveID = getIntent().getStringExtra("liveID");

        ZegoUIKitPrebuiltLiveStreamingConfig config;
        if (isHost) {
            config = ZegoUIKitPrebuiltLiveStreamingConfig.host(true);
        } else {
            config = ZegoUIKitPrebuiltLiveStreamingConfig.audience(true);
        }

        ZegoUIKitPrebuiltLiveStreamingFragment fragment = ZegoUIKitPrebuiltCallFragment.newInstance(
            appID, appSign, userID, userName,yourLiveID,config);
        getSupportFragmentManager().beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commitNow();
    }
}
```

```kotlin Kotlin
class LiveActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_live)
        addFragment()
    }

    private fun addFragment() {
        val appID: Long = yourAppID
        val appSign = yourAppSign
        val userID = YourUserID
        val userName = YourUserName

        val isHost = intent.getBooleanExtra("host", false)
        val yourLiveID = intent.getStringExtra("liveID")

        val config: ZegoUIKitPrebuiltLiveStreamingConfig
        config = if (isHost) {
            ZegoUIKitPrebuiltLiveStreamingConfig.host(true)
        } else {
            ZegoUIKitPrebuiltLiveStreamingConfig.audience(true)
        }

        val fragment = ZegoUIKitPrebuiltLiveStreamingFragment.newInstance(
            appID, appSign, userID, userName, yourLiveID, config
        )
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commitNow()
    }
}
```
</CodeGroup>


Then, you can start live streaming by starting your `LiveActivity`.


## Run & Test

Now you have finished all the steps!

You can simply click the **Run** on Android Studio to run and test your App on the device.


## Related guides

<CardGroup cols={2}>
  <Card title="How to upgrade UIKits with a plugin to V2.0.0" href="https://docs.zegocloud.com/faq/UIKit_Plugin2.0_upgrade?product=UIKits_CallKit&platform=android" target="_blank" />
</CardGroup>


## Resources

<CardGroup cols={2}>
  <Card title="Sample code" href="https://github.com/ZEGOCLOUD/zego_uikit_prebuilt_live_streaming_example_android" target="_blank">
    Click here to get the complete sample code.
  </Card>
</CardGroup>
