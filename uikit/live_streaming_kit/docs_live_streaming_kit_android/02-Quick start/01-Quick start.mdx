import Content from "/snippets/uikit/AndroidEnviromentRequirementEn.md"

# Quick start

<Video src="https://www.youtube.com/embed/bDd3jB17dPY"/>

## Prepare the environment

<Content/>

## Integrate the SDK

### Add ZegoUIKitPrebuiltLiveStreaming as dependencies

1. Add the `jitpack` configuration.
- If your Android Gradle Plugin is **7.1.0 or later**: enter your project's root directory, open the `settings.gradle` file to add the jitpack to `dependencyResolutionManagement` > `repositories` like this:

``` groovy
dependencyResolutionManagement {
   repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
   repositories {
      google()
      mavenCentral()
      maven { url 'https://maven.zego.im' }  // <- Add this line.
      maven { url 'https://www.jitpack.io' } // <- Add this line.
   }
}
```

<Warning title="Warning">

If you can't find the above fields in `settings.gradle`, it's probably because your Android Gradle Plugin version is lower than v7.1.0.

For more details, see [Android Gradle Plugin Release Note v7.1.0](https://developer.android.com/studio/releases/gradle-plugin#settings-gradle).
</Warning>

- If your Android Gradle Plugin is **earlier than 7.1.0**: enter your project's root directory, open the `build.gradle` file to add the jitpack to `allprojects`->`repositories` like this:

```groovy
allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.zego.im' }   // <- Add this line.
        maven { url "https://jitpack.io" }  // <- Add this line.
    }
}
```

2. Modify your app-level `build.gradle` file:
```groovy
dependencies {
    ...
    implementation 'com.github.ZEGOCLOUD:zego_uikit_prebuilt_live_streaming_android:+'    // Add this line to your module-level build.gradle file's dependencies, usually named [app].
}
```

### Using the ZegoUIKitPrebuiltLiveStreamingFragment in your project

- Go to [ZEGOCLOUD Admin Console](https://console.zegocloud.com/), get the `appID` and `appSign` of your project.
- Specify the `userID` and `userName` for connecting the Live Streaming Kit service.
- `liveID` represents the live streaming you want to start or watch (only supports single-host live streaming for now).


<Note title="Note">

- `userID`, `userName`, and `liveID` can only contain numbers, letters, and underlines (_).
- Using the same `liveID` will enter the same live streaming.
</Note>

<Warning title="Warning">

With the same `liveID`, only one user can enter the live stream as host. Other users need to enter the live stream as the audience.
</Warning>


<CodeGroup>
```java Java
public class LiveActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_call);

        addFragment();
    }

    public void addFragment() {
        long appID = yourAppID;
        String appSign = yourAppSign;
        String userID = yourUserID;
        String userName = yourUserName;

        boolean isHost = getIntent().getBooleanExtra("host", false);
        String liveID = getIntent().getStringExtra("liveID");

        ZegoUIKitPrebuiltLiveStreamingConfig config;
        if (isHost) {
            config = ZegoUIKitPrebuiltLiveStreamingConfig.host();
        } else {
            config = ZegoUIKitPrebuiltLiveStreamingConfig.audience();
        }
        ZegoUIKitPrebuiltLiveStreamingFragment fragment = ZegoUIKitPrebuiltCallFragment.newInstance(
            appID, appSign, userID, userName,liveID,config);
        getSupportFragmentManager().beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commitNow();
    }
}
```

```kotlin Kotlin
class LiveActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_live)
        addFragment()
    }

    private fun addFragment() {
        val appID: Long = yourAppID
        val appSign = yourAppSign
        val userID = YourUserID
        val userName = YourUserName

        val isHost = intent.getBooleanExtra("host", false)
        val liveID = intent.getStringExtra("liveID")

        val config: ZegoUIKitPrebuiltLiveStreamingConfig = if (isHost) {
            ZegoUIKitPrebuiltLiveStreamingConfig.host()
        } else {
            ZegoUIKitPrebuiltLiveStreamingConfig.audience()
        }
        val fragment = ZegoUIKitPrebuiltLiveStreamingFragment.newInstance(
            appID, appSign, userID, userName, liveID, config
        )
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commitNow()
    }
}
```
</CodeGroup>


Then, you can start live streaming by starting your `LiveActivity`.


## Run & Test

Now you have finished all the steps!

You can simply click the **Run** on Android Studio to run and test your App on the device.

## Resources

<CardGroup cols={2}>
  <Card title="Sample code" href="https://github.com/ZEGOCLOUD/zego_uikit_prebuilt_live_streaming_example_android" target="_blank">
    Click here to get the complete sample code.
  </Card>
</CardGroup>
