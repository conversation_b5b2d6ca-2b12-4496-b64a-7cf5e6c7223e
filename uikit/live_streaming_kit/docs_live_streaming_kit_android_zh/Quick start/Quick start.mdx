import UIKitCreateAccountAndServicesZh from "/snippets/uikit/UIKitCreateAccountAndServicesZh.md";
import AndroidEnviromentRequirementZh from "/snippets/uikit/AndroidEnviromentRequirementZh.md";

# 快速开始

- - -


## 准备环境

在开始集成互动直播 UIKit 前，请确保开发环境满足以下要求：

<AndroidEnviromentRequirementZh/>

## 前提条件

<UIKitCreateAccountAndServicesZh/>

## 实现流程

### 集成 SDK

1. 添加 `jitpack` 配置。
   - 如果您的 Android Gradle 插件是 **7.1.0 或更高版本**：
   进入项目根目录，打开 `settings.gradle` 文件，以如下方式在 `dependencyResolutionManagement` > `repositories` 中添加 jitpack：
      ```groovy
      dependencyResolutionManagement {
         repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
         repositories {
            google()
            mavenCentral()
            maven { url 'https://maven.zego.im&#39;  }  // <- 添加这行。
            maven { url 'https://www.jitpack.io&#39;  } // <- 添加这行。
         }
      }
      ```

        <Warning title="注意">

        如果 `settings.gradle` 中找不到上述字段，可能是因为 Android Gradle 插件版本低于 v7.1.0。

        更多详细信息，请参阅 [Android Gradle 插件发布说明 v7.1.0](https://developer.android.com/studio/releases/gradle-plugin#settings-gradle)。
        </Warning>

    - 如果您的 Android Gradle 插件是 **7.1.0 之前** 的版本：
      进入项目根目录，打开 `build.gradle` 文件，以如下方式在 `allprojects`->`repositories` 中添加 jitpack：

      ```groovy
      allprojects {
         repositories {
            google()
            mavenCentral()
            maven { url 'https://maven.zego.im&#39;  }   // <- 添加这行。
            maven { url "https://jitpack.io&#44;  }  // <- 添加这行。
         }
      }
      ```

2. 修改应用级别的 `build.gradle` 文件。
    ```groovy
    dependencies {
        ...
        implementation 'com.github.ZEGOCLOUD:zego_uikit_prebuilt_live_streaming_android:+'    // 添加这行到您的模块级 build.gradle 文件的依赖部分，通常命名为 [app]。
    }
    ```

### 使用 ZegoUIKitPrebuiltLiveStreamingFragment

- 在 [ZEGO 控制台](https://console.zego.im) 创建项目，并申请有效的 AppID 和 AppSign，详情请参考 [控制台 - 项目管理](https://doc-zh.zego.im/article/12107) 中的“项目信息”。
- 指定 `userID` 和 `userName` 来指定用户以实现直播服务。`liveID` 代表您想要开始或观看的一场直播（目前仅支持一个主播）。

<Warning title="注意">
- 使用相同的 `liveID` 将进入相同的直播间，且直播间只能有一个用户作为主播加入，其他用户需要作为观众加入。
- `userID`、`userName` 和 `liveID` 只能包含数字、字母和下划线 (_)。
- UIKit 默认语言为英文，如需修改为中文，请修改 `ZegoUIKitPrebuiltLiveStreamingConfig.translationText`。
</Warning>


<CodeGroup>
```java title="Java"
public class LiveActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_call);

        addFragment();
    }

    public void addFragment() {
        long appID = yourAppID; // 替换为您的 AppID
        String appSign = yourAppSign; // 替换为您的 AppSign
        String userID = yourUserID; // 替换为您的 UserID
        String userName = yourUserName; // 替换为您的 UserName

        boolean isHost = getIntent().getBooleanExtra("host", false);
        String liveID = getIntent().getStringExtra("liveID");

        ZegoUIKitPrebuiltLiveStreamingConfig config;
        if (isHost) {
            config = ZegoUIKitPrebuiltLiveStreamingConfig.host();
        } else {
            config = ZegoUIKitPrebuiltLiveStreamingConfig.audience();
        }
            //修改为中文
            config.translationText = new ZegoTranslationText(ZegoUIKitLanguage.CHS);
        ZegoUIKitPrebuiltLiveStreamingFragment fragment = ZegoUIKitPrebuiltCallFragment.newInstance(
            appID, appSign, userID, userName, liveID, config);
        getSupportFragmentManager().beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commitNow();
    }
}
```
```kotlin title="Kotlin"
class LiveActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_live)
        addFragment()
    }

    private fun addFragment() {
        val appID: Long = yourAppID // 替换为您的 AppID
        val appSign = yourAppSign // 替换为您的 AppSign
        val userID = YourUserID // 替换为您的 UserID
        val userName = YourUserName // 替换为您的 UserName

        val isHost = intent.getBooleanExtra("host", false)
        val liveID = intent.getStringExtra("liveID")

        val config: ZegoUIKitPrebuiltLiveStreamingConfig = if (isHost) {
            ZegoUIKitPrebuiltLiveStreamingConfig.host()
        } else {
            ZegoUIKitPrebuiltLiveStreamingConfig.audience()
        }
        //修改为中文
        config.translationText = ZegoTranslationText(ZegoUIKitLanguage.CHS)
        val fragment = ZegoUIKitPrebuiltLiveStreamingFragment.newInstance(
            appID, appSign, userID, userName, liveID, config
        )
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commitNow()
    }
}
```
</CodeGroup>

然后，您可以通过启动 `LiveActivity` 开始直播。

## 运行 & 测试

现在您已经完成了所有步骤。

您只需在 Android Studio 中点击 **运行** 就可以在设备上运行和测试应用。

## 相关资源

<CardGroup cols={2}>
<Card title="示例代码" href="https://github.com/ZEGOCLOUD/zego_uikit_prebuilt_live_streaming_example_android" target="_blank">
  点击此处获取完整的示例代码。
</Card>
</CardGroup>
