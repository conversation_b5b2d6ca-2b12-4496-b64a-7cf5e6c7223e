# Quick start

This doc will guide you to integrate the In-app Chat Kit and start a chat quickly.

## Prerequisites

import ZIM_ZIMKIT_Prerequisites_Features from "/snippets/uikit/in_app_chat/ZIM_ZIMKIT_Prerequisites_Features-en.mdx"

<ZIM_ZIMKIT_Prerequisites_Features />

- Environment-specific requirements:
    - Android Studio Arctic Fox (2020.3.1) or later
    - Android SDK Packages: Android SDK 30, Android SDK Platform - Tools 30
    - An Android device or Simulator that is running on Android 5.0 or later and supports audio and video. We recommend you use a real device.
    - Android device and your computer are connected to the internet.

## Integrate the SDK

<Steps>
<Step title="Add com.github.ZEGOCLOUD:zego_inapp_chat_uikit_android as dependencies">
- **Add the `jitpack` configuration**


<Accordion title="Android Gradle Plugin is 7.1.0 or later" defaultOpen="true">

Enter your project's root directory, open the `settings.gradle` file to add the jitpack to `dependencyResolutionManagement` > `repositories` like this:

``` groovy settings.gradle {6-7}
dependencyResolutionManagement {
   repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
   repositories {
      google()
      mavenCentral()
      maven { url 'https://maven.zego.im' }   // <- Add this line.
      maven { url 'https://www.jitpack.io' } // <- Add this line.
   }
}
```

<Warning title="Warning">

If you can't find the above fields in `settings.gradle`, it's probably because your Android Gradle Plugin version is lower than v7.1.0.

For more details, see [Android Gradle Plugin Release Note v7.1.0](https://developer.android.com/studio/releases/gradle-plugin#settings-gradle).
</Warning>
</Accordion>

<Accordion title="Android Gradle Plugin is earlier than 7.1.0" defaultOpen="false">

Enter your project's root directory, open the `build.gradle` file to add the jitpack to `allprojects`->`repositories` like this:

```groovy build.gradle {5-6}
allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.zego.im' }   // <- Add this line.
        maven { url "https://jitpack.io" }  // <- Add this line.
    }
}
```
</Accordion>

- **Modify your app-level `build.gradle` file**


```groovy {3}
dependencies {
    ...
    implementation 'com.github.ZEGOCLOUD:zego_inapp_chat_uikit_android:+'    // add this line in your module-level build.gradle file's dependencies, usually named [app].
}
```
</Step>
<Step title="Call the init method to initialize the In-app Chat Kit">
```java MyApplication.java {15}

import android.app.Application;

import com.zegocloud.zimkit.services.ZIMKit;

public class MyApplication extends Application {
    public static MyApplication sInstance;

    @Override
    public void onCreate() {
        super.onCreate();
        sInstance = this;
        Long appId = ;    // The AppID you get from ZEGOCLOUD Admin Console.
        String appSign = ;    // The App Sign you get from ZEGOCLOUD Admin Console.
        ZIMKit.initWith(this,appId,appSign);
        // Online notification for the initialization (use the following code if this is needed).
        ZIMKit.initNotifications();
    }
}
```
</Step>
<Step title="Call the connectUser method on the login page to log in to the In-app Chat Kit.">

import ZIM_ZIMKit_SDK_warning from "/snippets/uikit/in_app_chat/ZIM_ZIMKit_SDK_warning-en.mdx"

<ZIM_ZIMKit_SDK_warning />

```java MyZIMKitActivity.java {27-34}

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import im.zego.zim.enums.ZIMErrorCode;
import com.zegocloud.zimkit.services.ZIMKit;

public class MyZIMKitActivity extends AppCompatActivity {
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    public void buttonClick() {
        // userId and userName: 1 to 32 characters, can only contain digits, letters, and the following special characters: '~', '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '=', '-', '`', ';', '’', ',', '.', '<', '>', '/', '\'
        String userId = ; // Your ID as a user.
        String userName = ; // You name as a user.
        String userAvatar = ; // The image you set as the user avatar must be network image. e.g., https://storage.zego.im/IMKit/avatar/avatar-0.png
        connectUser(userId, userName,userAvatar);
    }

    public void connectUser(String userId, String userName,String userAvatar) {
        // Logs in.
        ZIMKit.connectUser(userId,userName,userAvatar, errorInfo -> {
            if (errorInfo.code == ZIMErrorCode.SUCCESS) {
                // Operation after successful login. You will be redirected to other modules only after successful login. In this sample code, you will be redirected to the conversation module.
                toConversationActivity();
            } else {

            }
        });
    }

    // Integrate the conversation list into your Activity as a Fragment
    private void toConversationActivity() {
      // Redirect to the conversation list (Activity) you created.
      Intent intent = new Intent(this,ConversationActivity.class);
      startActivity(intent);
    }
}
```
</Step>
<Step title="Display the conversation component of the In-app Chat Kit">

The layout of the `ConversationActivity` is specified in  `activity_conversation.xml`:

<CodeGroup>

```java ConversationActivity.java {6}
   public class ConversationActivity extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_conversation);
    }

}
```


```xml activity_conversation.xml {9}
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <fragment
        android:id="@+id/frag_conversation_list"
        android:name="com.zegocloud.zimkit.components.conversation.ui.ZIMKitConversationFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
```

</CodeGroup>

Ideally, by this point, your app will look like this:

<Frame width="200" height="auto" caption="">
    <img src="https://storage.zego.im/sdk-doc/Pics/zimkit_android/zimkit_android_conversation_en_new.jpeg" />
</Frame>
</Step>
</Steps>


## Start a chat

The In-app Chat Kit supports the following features:

import InappChatUIkit_Startasession from "/snippets/uikit/in_app_chat/InappChatUIkit_Startasession-en.mdx"

<Accordion title="Start a one-on-one chat" defaultOpen="false">

<InappChatUIkit_Startasession />

1. Get the `userId` that is generated using your own business logic. (the `userId` here refers to the peer user you want to chat with.)
2. Fill in the `userId` parameter and run the following code:

```java {2}
private void startSingleChat(String userId) {
    ZIMKitRouter.toMessageActivity(this, userId, ZIMKitConversationType.ZIMKitConversationTypePeer);
}
```

</Accordion>

<Accordion title="Start a group chat" defaultOpen="false">

1. Get the `ids` and `groupName` that is generated using your own business logic. (the `ids` here refers to the ID list of the users that you want to invite to the group chat.)
2. Fill in the `ids` and `groupName` parameters and run the following code:

```java {5-19}
public void createGroupChat(List<String> ids, String groupName) {
    if (ids == null || ids.isEmpty()) {
        return;
    }
    ZIMKit.createGroup(groupName, ids, (groupInfo, inviteUserErrors, errorInfo) -> {
        if (errorInfo.code == ZIMErrorCode.SUCCESS) {
            if (!inviteUserErrors.isEmpty()) {
                // Implement the logic for the prompt window based on your business logic when
                // there is a non-existing user ID in the group.
            } else {
                // Directly enter the chat page when the group chat is created successfully.
                ZIMKitRouter.toMessageActivity(this, groupInfo.getId(),
                        ZIMKitConversationType.ZIMKitConversationTypeGroup);
            }
        } else {
            // Implement the logic for the prompt window based on the returned error info
            // when failing to create a group chat.
        }
    });
}
```

</Accordion>

<Accordion title="Join a group chat" defaultOpen="false">

1. Get the `groupId` that is generated using your own business logic. (the `groupID` here refers to the group chat you want to join.)
2. Fill in the `groupId` parameter and run the following code:

```java {2-10}
public void joinGroupChat(String groupId) {
    ZIMKit.joinGroup(groupId, (groupInfo, errorInfo) -> {
        if (errorInfo.code == ZIMErrorCode.SUCCESS) {
            // Enter the group chat page after joining the group chat successfully.
            ZIMKitRouter.toMessageActivity(this, groupInfo.getId(), ZIMKitConversationType.ZIMKitConversationTypeGroup);
        } else {
            // Implement the logic for the prompt window based on the returned error info
            // when failing to join the group chat.
        }
    });
}
```

</Accordion>

## Related guides

<CardGroup cols={2}>
    <Card title="Component overview" href="./03-UI components/01-Overview.mdx">
        Click here to explore more UI components.
    </Card>
    <Card title="Run the sample code" href="https://github.com/ZEGOCLOUD/zego_inapp_chat_uikit_example_android" target="_blank">
        A quick guide to help you run the sample code.
    </Card>
</CardGroup>

## Get support

import ZIMKit_GetSupport from "/snippets/uikit/in_app_chat/ZIMKit_GetSupport-en.mdx"

<ZIMKit_GetSupport />
