---
articleID: 13932
---

import { getPlatformData } from "/snippets/utils-content-parser.js"
import MarkMessageType from '/core_products/zim/en/snippets/MessageTypeEn.mdx'
import MarkSendMessageEvent from '/core_products/zim/en/snippets/SendMessageEventEN.mdx'

export const createMap = {
'Android': <a href='@create' target='_blank'>create</a>,
}
export const setEventHandlerMap = {
'Android': <a href='@setEventHandler' target='_blank'>setEventHandler</a>,
}
export const onReceivePeerMessageMap = {
'Android': <a href='@onReceivePeerMessage' target='_blank'>onReceivePeerMessage</a>,
}
export const onReceiveGroupMessageMap = {
'Android': <a href='@onReceiveGroupMessage' target='_blank'>onReceiveGroupMessage</a>,
}
export const onReceiveRoomMessageMap = {
'Android': <a href='@onReceiveRoomMessage' target='_blank'>onReceiveRoomMessage</a>,
}
export const ZIMUserInfoMap = {
'Android': <a href='@-ZIMUserInfo' target='_blank'>ZIMUserInfo</a>,
}
export const loginMap = {
'Android': <a href='@login__2' target='_blank'>login</a>,
}
export const sendMessageMap = {
'Android': <a href='@sendMessage' target='_blank'>sendMessage</a>,
}
export const onMessageSentMap = {
'Android': <a href='@onMessageSent' target='_blank'>onMessageSent</a>,
}
export const ZIMConversationTypeMap = {
'Android': <a href='@-ZIMConversationType' target='_blank'>ZIMConversationType</a>,
}
export const onMessageAttachedMap = {
'Android': <a href='@onMessageAttached' target='_blank'>onMessageAttached</a>,
}
export const ZIMMessageMap = {
'Android': <a href='@-ZIMMessage' target='_blank'>ZIMMessage</a>,
}
export const logoutMap = {
'Android': <a href='@logout' target='_blank'>logout</a>,
}
export const destroyMap = {
'Android': <a href='@destroy' target='_blank'>destroy</a>,
}

# Send and receive messages


This document describes how to use the ZIM SDK (In-app Chat) to send and receive messages.


## Solution

The following shows the access solution that the ZIM SDK provides.

<Frame width="auto" height="auto">
  <img src="https://storage.zego.im/sdk-doc/Pics/InappChat/im_solution_en.png" />
</Frame>

In this solution, you will need to implement the following business logic based on your actual business requirements:

- The logic for managing the users on the client, and the logic for distributing user ID for users to log in.

## Prerequisites

Before you begin, make sure:

- Go to [ZEGOCLOUD Admin Console](https://console.zegocloud.com), and do the following:
- Create a project, get the **AppID** and **AppSign**.
- Activate the **In-app Chat** service (as shown in the following figure).
![](https://storage.zego.im/sdk-doc/Pics/InappChat/ActivateZIMinConsole2.png)


- Android Studio 2020.3.1 or later
- Android SDK Packages: Android SDK 25, Android SDK Build-Tools 25.0.2, Android SDK Platform-Tools 25.x.x or later.
- An Android device or Simulator that is running on Android 4.4 or later and supports audio and video. We recommend you use a real device.

<Warning title="Warning">

For SDK `2.3.0` or later, the AppSign authentication mode and Token-based authentication mode are both supported.

If you want to change your authentication mode, please refer to the [Upgrade the authentication mode from using the AppSign to Token](https://docs.zegocloud.com/faq/token_upgrade).
</Warning>

## Integrate the SDK

### Optional: Create a new project

<Accordion title="Create a new project" defaultOpen="false">
1. Open Android Studio, select **File > New > Project**.

<Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Android/ExpressSDK/Integration/android_new_project.png" /></Frame>

2. Configure your new project with **Application name** and **Project location**.

<Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Android/ExpressSDK/Integration/android_new_project_finish.png" /></Frame>

3. All other items in the panel can be left as their defaults, click **Next** and then click **Finish**.

</Accordion>

### Import the SDK

> The Android ABIs are currently supported by the SDK: armeabi-v7a, arm64-v8a, x86, x86_64.

Choose either of the following methods to integrate the SDK into your project.

#### Method 1: Integrate the SDK automatically

1. Set up repositories

- If your Android Gradle Plugin is **v7.1.0 or later**: go to the root directory of your project, open the `settings.gradle` file, and add the following line to the `dependencyResolutionManagement`:

    ```groovy
    ...
    dependencyResolutionManagement {
        repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
        repositories {
            maven { url 'https://maven.zego.im' }
            mavenCentral()
            google()
        }
    }
    ```

    <Warning title="Warning">

    If you can not find the above fields in `settings.gradle`, it's probably because your Android Gradle Plugin version is lower than v7.1.0.

    For more details, see [Android Gradle Plugin Release Note v7.1.0](https://developer.android.com/build/releases/past-releases/agp-7-1-0-release-notes#settings-gradle).
    </Warning>

- If your Android Gradle Plugin is **earlier than 7.1.0**: go to the root directory of your project, open the `build.gradle` file, and add the following line to the `allprojects`:

    ```groovy
    ...
    allprojects {
        repositories {
            maven { url 'https://maven.zego.im' }
            mavenCentral()
            google()
        }
    }
    ```

2. Declare dependencies:

Go to the `app` directory, open the `build.gradle` file, and add the following line to the `dependencies`. (**x.y.z** is the SDK version number, to obtain the latest version number, see [Release Note](./../docs_zim_rn/Client%20SDKs/ZIM%20release%20notes.mdx))

```groovy
...
dependencies {
    ...
    implementation 'im.zego:zim:x.y.z'
}
```

#### Method 2: Manually add the SDK to the project

1. Download the latest version of SDK from [SDK downloads](./Client%20SDKs/SDK%20downloads.mdx).

2. Extract the files from the SDK packages into your project directory, for example, `app/libs`.

<Frame width="auto" height="auto">
  <img src="https://storage.zego.im/sdk-doc/Pics/Android/ZIM/android_studio_im_sdk.jpg" />
</Frame>

3. Add SDK Import Statements. Open the file `app/build.gradle`, and add the following contents:

1. Add the `ndk` node inside the `defaultConfig` node to specify the supported ABIs.

    <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Android/ExpressSDK/Integration/add_ndk_node.png" /></Frame>

    ```groovy
    ndk {
        abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
    }
    ```

2. Add the `sourceSets` node inside the `android` node to specify the directory containing the SDK files.

    <Note title="Note">

    The directory **libs** is only an example for illustration, you can fill in based on the actual situation.
    </Note>

    <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Android/ExpressSDK/Integration/add_sourceSets_node.png" /></Frame>

    ```groovy
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    ```

3. Add the following code in the `dependencies` node.

    ```groovy
    dependencies {
        implementation fileTree(dir: 'libs', include: ['*.jar'])
        ......
    }
    ```

## Add permissions

Permissions can be set as needed.

Go to the directory `app/src/main`, open the `AndroidManifest.xml` file, and add the following code:

```xml
<!-- Permissions required by the SDK -->
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

## Prevent class name obfuscation

To prevent the ZEGO SDK public class names from being obfuscated, you can add the following code in the file `proguard-rules.pro`.

```txt
-keep class **.zego.**{*;}
```

## Implementation steps

### Get the sample code

To download and run the sample code, see [Sample app](./Sample%20app.mdx).

For the sample code related to this feature, check out the source files in the `app/src/main/java/im/zego/zimexample/ui/room/ZIMPeerSessionActivity` directory in the SDK sample code package.

### Import the class file

  Import the class file to your project:

```java
import im.zego.zim.ZIM
```

### Create a ZIM SDK instance

Creating a ZIM instance is the very first step, an instance corresponds to a user logging in to the system as a client.

So, let's suppose we have two clients now, client A and client B. To send and receive messages to each other, both of them will need to call the {getPlatformData(props,createMap)} method with the AppID and AppSign you get in the previous **Prerequisites** steps to create a ZIM SDK instance of their own:

```java
// Create a ZIM SDK object and pass the AppID, AppSign, and Application in Android.
ZIMAppConfig appConfig = new ZIMAppConfig();
appConfig.appID = 0; // Replace the value with your AppID
appConfig.appSign = ""; // Replace the value with your AppSign
zim = ZIM.create(appConfig, application);
```

Since most developers only need to instantiate ZIM once throughout the entire process, We recommends using the [getInstance](@getInstance) method to obtain the created instance object.

```java
// After successfully calling the create method to create an instance, you can obtain the zim object via the getInstance method.
// Calling the getInstance method before calling the create method or after calling the destroy method to destroy the instance will return null.
ZIM zim = ZIM.getInstance();
```

### Set an event handler object


Before a client user's login, you will need to call the {getPlatformData(props,setEventHandlerMap)} method to implement an event handler object, and customize the event callbacks, such as you can receive callback notifications when SDK errors occur or receive message related callback notifications.

```java
zim.setEventHandler(new ZIMEventHandler() {
    @Override
    public void onPeerMessageReceived(ZIM zim, ArrayList<ZIMMessage> messageList, ZIMMessageReceivedInfo info, String fromUserID) {    // Implement the callback for receiving the one-to-one messages.
    }
});
```
For more details about related callbacks, see {getPlatformData(props,onReceivePeerMessageMap)}, {getPlatformData(props,onReceiveGroupMessageMap)}, and {getPlatformData(props,onReceiveRoomMessageMap)}.

### Log in to the ZIM SDK

For client A and client B to send and receive messages after creating the ZIM SDK instance, they will need to log in to the ZIM SDK.

To log in, Clients A and B both need to call the {getPlatformData(props,loginMap)} interface with the userID and the [ZIMLoginConfig](@-ZIMLoginConfig) object to log in.


<Warning title="Warning">

- You can custom the **userID** and **userName**, and we recommend you set the  **userID** to a meaningful value and associate them with the account system of your application.
- For SDK 2.3.0 or later: The SDK uses the AppSign authentication mode by default. You can leave the token parameter blank when logging in.
- If you choose the token authentication, please refer to the [Authentication](./Guides/Users/<USER>
</Warning>


```java
// userID must be within 32 bytes, and can only contain letters, numbers, and the following special characters: '~', '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '=', '-', '`', ';', '’', ',', '.', '<', '>', '/', '\'.
// userName must be within 256 bytes, no special characters limited.
String userID = "";
ZIMLoginConfig config = new ZIMLoginConfig();
config.userName = "";

// When logging in:
// If you are using the Token-based authentication mode, you will need to fill in the Token which you generated by referring to the [Authentication] doc.
// If you are using the AppSign mode for authentication (the default auth mode for v2.3.0 or later), leave the Token parameter blank.
config.token = "";

zim.login(userID, config, new ZIMLoggedInCallback() {
@Override
public void onLoggedIn(ZIMError error) {
      // You can know whether the login is successful according to the ZIMError.
}
});
```

### Send one-to-one messages

Client A can send messages to client B after logging in successfully.

Currently, the ZIM SDK supports the following message types:

<MarkMessageType />

To send one-to-one messages, for example, client A wants to send a message to client B, then client A needs to call the {getPlatformData(props,sendMessageMap)} method with client B's userID, message content, and message type {getPlatformData(props,ZIMConversationTypeMap)}.
And client A can be notified whether the message is delivered successfully through the callback {getPlatformData(props,onMessageSentMap)}.


- {getPlatformData(props,onMessageAttachedMap)} callback: The callback on the message not sent yet. Before the message is sent, you can get a temporary {getPlatformData(props,ZIMMessageMap)} message for you to implement your business logic as needed. For example, you can get the ID of the message before sending it. Or when sending a message with large content, such as a video, you can get the localMessageID of the message before the message is uploaded to implement a Loading UI effect.

```java
// The following shows how to send one-to-one message, the [conversationType] needs to be set to [ZIMConversationTypePeer].
String conversationID = "xxxx";

ZIMTextMessage zimMessage = new ZIMTextMessage();
zimMessage.message = "Message content";

ZIMMessageSendConfig config = new ZIMMessageSendConfig();
// Set priority for the message. 1: Low (by default). 2: Medium. 3: High.
config.priority = ZIMMessagePriority.LOW;
// Set the offline push notificaition config.
ZIMPushConfig pushConfig = new ZIMPushConfig();
pushConfig.title = "Title of the offline push";
pushConfig.content= "Content of the offline push";
pushConfig.extendedData = "Extended info of the offline push";
config.pushConfig = pushConfig;

// In 1-on-1 chats, the conversationID is the peer user ID. In group chats, the conversationID is the groupID. In the chat room, the conversationID is the roomID.
zim.sendMessage(zimMessage, conversationID, ZIMConversationType.Peer,config, new ZIMMessageSentCallback() {
@Override
public void onMessageAttached(ZIMMessage zimMessage){
      // The callback on the message not sent yet. You can get a temporary object here and this object must be the same as that created zimMessage object. You can make your own business logic using this as needed, for example, display a UI ahead of time.
}
@Override
public void onMessageSent(ZIMMessage zimMessage, ZIMError error) {
    // Implement the event callback on message sent.
}
});
```

### Receive one-to-one messages

After client B logs in, he will receive client A's message through the callback [onPeerMessageReceived](@onPeerMessageReceived) which is already set in the [setEventHandler](@setEventHandler) method.


<Warning title="Warning">
When a message is received, you need to determine the message is a Text message or a Command message because these two message types are based on the basic message. You need to convert the basic message class to a concrete message type and then retrieve the message content from the `Message` field.
</Warning>


```java
zim.setEventHandler(new ZIMEventHandler() {
    @Override
    public void onPeerMessageReceived(ZIM zim, ArrayList<ZIMMessage> messageList, ZIMMessageReceivedInfo info, String fromUserID) {

        for (ZIMMessage zimMessage : messageList) {
            if (zimMessage instanceof ZIMTextMessage)
            {
                ZIMTextMessage zimTextMessage = (ZIMTextMessage) zimMessage;
                Log.e(TAG, "Received message:"+ zimTextMessage.message);
            }
        }
    }
});
```

### Log out

For a client to log out, call the {getPlatformData(props,logoutMap)} method.

```java
zim.logout();
```

### Destroy the ZIM SDK instance

To destroy the ZIM SDK instance, call the {getPlatformData(props,destroyMap)} method.

```java
zim.destroy();
```