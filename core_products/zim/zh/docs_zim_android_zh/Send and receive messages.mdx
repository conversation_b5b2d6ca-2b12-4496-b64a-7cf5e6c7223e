---
articleID: 11568
---

import MarkMessageType from '/core_products/zim/zh/snippets/MessageType.mdx'
import MarkSendMessageEvent from '/core_products/zim/zh/snippets/SendMessageEvent.mdx'
import MessageSequenceDiagramCommon from '/core_products/zim/zh/snippets/MessageSequenceDiagramCommonzh.mdx'

# 实现基本消息收发

---

本文介绍如何使用 ZIM SDK 快速实现基本的单聊会话消息收发功能。


## 1 前提条件

在使用 ZIM SDK 前，请确保开发环境满足以下要求：

* Android Studio 2020.3.1 或以上版本。

- Android SDK 25、Android SDK Build-Tools 25.0.2、Android SDK Platform-Tools 25.x.x 或以上版本。
- Android 4.4 或以上版本的设备或模拟器（推荐使用真机）。
- 已在 [ZEGO 控制台](https://console.zego.im) 创建项目，获取到了接入 ZIM SDK 服务所需的 AppID、AppSign。ZIM 服务权限不是默认开启的，使用前，请先在 [ZEGO 控制台](https://console.zego.im) 自助开通 ZIM 服务（详情请参考 [项目管理 - 即时通讯](https://doc-zh.zego.im/article/14994)），若无法开通 ZIM 服务，请联系 ZEGO 技术支持开通。

<Warning title="注意">

`2.3.0 及以上`版本的 SDK，开始支持 “AppSign 鉴权”，同时仍支持 “Token 鉴权”，若您需要升级鉴权方式，可参考 [ZIM 如何从 AppSign 鉴权升级为 Token 鉴权](http://doc-zh.zego.im/faq/token_upgrade_zim)。
</Warning>



## 2 集成 SDK

### 2.1 （可选）新建项目

<details class="zg-primary">
    <summary>此步骤以如何创建新项目为例，如果是集成到已有项目，可忽略此步。</summary>

1. 打开 Android Studio，选择 “File > New > New Project” 菜单。

    <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Android/ExpressSDK/Integration/android_new_project.png" /></Frame>

2. 填写项目名及项目存储路径。

    <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Android/ExpressSDK/Integration/android_new_project_finish.png" /></Frame>

3. 其他按照默认设置，单击 “Next”，最后单击 “Finish” 完成新工程创建。

</details>

### 2.2 导入 SDK

目前支持的平台架构包括：armeabi-v7a、arm64-v8a、x86、x86_64。

开发者可通过以下任意一种方式实现集成 SDK。

#### 方式一：自动集成 SDK

1. 配置 repositories 源

    - 当您的 Android Gradle Plugin 为 v7.1.0 或以上版本：进入项目根目录，打开 “settings.gradle” 文件，在 “dependencyResolutionManagement” 中加入如下代码。

        ```groovy
        ...
        dependencyResolutionManagement {
            repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
            repositories {
                maven { url 'https://maven.zego.im' }
                mavenCentral()
                google()
            }
        }
        ```

        <Warning title="注意">

        如果您在 “settings.gradle” 中找不到上述字段，可能是因为您的 Android Gradle Plugin 版本低于 v7.1.0。

        相关信息请参考 [Android Gradle Plugin Release Note v7.1.0](https://developer.android.google.cn/build/releases/past-releases/agp-7-1-0-release-notes#settings-gradle)。
        </Warning>

    - 若您的 Android Gradle Plugin 为 v7.1.0 以下版本：进入项目根目录，打开 “build.gradle” 文件，在 “allprojects” 中加入如下代码。

        ```groovy
        ...
        allprojects {
            repositories {
                maven { url 'https://maven.zego.im' }
                mavenCentral()
                google()
            }
        }
        ```

2. 声明依赖

    进入 “app” 目录，打开 “build.gradle” 文件，在 “dependencies” 中添加 `implementation 'im.zego:zim:x.y.z'`，请从 [发布日志](https://doc-zh.zego.im/article/11594) 查询 SDK 最新版本，并将 `x.y.z` 修改为具体的版本号。

    ```groovy
    ...
    dependencies {
        ...
        implementation 'im.zego:zim:x.y.z'
    }
    ```

#### 方式二：复制 SDK 文件手动集成

1. 请参考 [下载 SDK](./Client%20SDKs/SDK%20downloads.mdx)，下载最新版本的 SDK。

2. 将 SDK 包解压至项目目录下，例如 “app/libs”。

    <Frame width="512" height="auto" caption="">
      <img src="https://storage.zego.im/sdk-doc/Pics/Android/ZIM/android_studio_im_sdk.jpg" />
    </Frame>

3. 添加 SDK 引用。进入到 “app” 目录，打开 “build.gradle” 文件。

    1. 在 “defaultConfig” 节点添加 “ndk” 节点，指定支持的架构。

        <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Android/ExpressSDK/Integration/add_ndk_node.png" /></Frame>

        ```groovy
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
        }
        ```

        <Note title="说明">
        根据实际情况决定要支持的架构。通常在发布 App 时只需要保留 "armeabi-v7a" 和 "arm64-v8a" 即可，可以减少 APK 包大小。
        </Note>

    2. 在 “android” 节点添加 “sourceSets” 节点，指定 “libs” 所在目录。

        <Note title="说明">
        示例代码中 “libs” 目录仅为举例，开发者可根据实际路径填写。
        </Note>

        <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Android/ExpressSDK/Integration/add_sourceSets_node.png" /></Frame>

        ```groovy
        sourceSets {
            main {
                jniLibs.srcDirs = ['libs']
            }
        }
        ```

    3. 在 “dependencies” 节点引入 “libs” 下所有的 jar。

        ```groovy
        dependencies {
            implementation fileTree(dir: 'libs', include: ['*.jar'])
            ......
        }
        ```

### 2.3 设置权限

开发者可以根据实际应用需要，设置应用所需权限。

进入 “app/src/main” 目录，打开 “AndroidManifest.xml” 文件，添加权限。

```xml
<!-- SDK 必须使用的权限 -->
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 2.4 防止混淆代码

在 “proguard-rules.pro” 文件中，为 SDK 添加 `-keep` 类的配置，防止混淆 SDK 公共类名称。

```txt
-keep class **.zego.**{*;}
```

## 3 实现基本收发消息

以下流程中，我们以客户端 A 和 B 的消息交互为例：

<Frame width="512" height="auto" caption="">
  <img src="https://storage.zego.im/sdk-doc/Pics/ZIM/quick_start_Implementation.png" />
</Frame>

### 3.1 实现流程

请参考 [跑通示例源码](./Sample%20code.mdx) 获取源码，相关功能的源码请查看 “app/src/main/java/im/zego/zimexample/ui/chat/peer/ZIMPeerChatActivity.java” 目录下的文件。

<a name="create"></a>

#### 1. 导入类文件

在项目文件中引入类文件。

```java
import im.zego.zim.ZIM;
```

#### 2. 创建 ZIM 实例

首先我们需要在 SDK 中创建 ZIM 实例，一个实例对应的是一个用户，表示一个用户以客户端的身份登录系统。

例如，客户端 A、B 分别调用 [create](@create) 接口，传入在 [1 前提条件](#1-前提条件) 中获取到的 AppID、AppSign，创建了 A、B 的实例：
```java
// 创建 ZIM 对象，传入 appID、appSign 与 Android 中的 Application
// 请注意：ZIM 从 2.3.0 版本开始支持 AppSign 鉴权，SDK 也默认为 AppSign 鉴权，如果您需要切换鉴权方式：
// (1) 2.3.3 及以上版本的 SDK，支持鉴权方式的自主切换; (2) 2.3.0 ~ 2.3.1 版本的 SDK，需要切换为 “Token 鉴权” 时，请联系 ZEGO 技术支持处理
ZIMAppConfig appConfig = new ZIMAppConfig();
appConfig.appID = 0;  //替换为您申请到的 AppID
appConfig.appSign = "";   //替换为您申请到的 AppSign
zim = ZIM.create(appConfig, application);
```

由于大多数开发者，在整个流程中，只需要将 ZIM 实例化一次。因此，ZEGO 建议您调用 [getInstance](@getInstance) 方法获取已创建的实例对象。

```java
// 在成功调用 create 方法创建实例后，可以通过 getInstance 方法获取 zim 对象。
// 在调用 create 方法之前，或者在调用 destroy 方法销毁实例之后，调用 getInstance 方法将返回 null。
zim = ZIM.getInstance();
```

<a name="setEventHandler"></a>

#### 3. 设置 setEventHandler 回调

在客户端登录前，开发者可以通过调用 [setEventHandler](@setEventHandler) 接口，自定义 ZIM 中的事件回调，接收到 SDK 异常、消息通知回调等的通知。

```java
zim.setEventHandler(new ZIMEventHandler() {
    @Override
    public void onPeerMessageReceived(ZIM zim, ArrayList<ZIMMessage> messageList, ZIMMessageReceivedInfo info, String fromUserID) {
        // 收到“单聊”通信的消息回调
    }
});
```

<a name="login"></a>

详细的接口介绍，请参考 [onPeerMessageReceived](@onPeerMessageReceived)。

#### 4. 登录 ZIM

创建实例后，客户端 A 和 B 需要登录 ZIM，只有登录成功后才可以开始发送、接收消息、更新 Token 等。

客户端需要使用各自的用户信息进行登录。调用 [login](@login__2) 接口，传入 userID 和 [ZIMLoginConfig](@-ZIMLoginConfig) 对象，进行登录。

<Warning title="注意">

- “userID”、“userName” 支持开发者自定义规则生成。建议开发者将 “userID” 设置为一个有意义的值，可将其与自己的业务账号系统进行关联。
- `2.3.0` 或以上版本的 SDK，默认鉴权方式为 “AppSign 鉴权”，登录 ZIM 时 Token 传入空字符串即可。
- 如果您使用的是 “Token 鉴权”，请参考 [使用 Token 鉴权](./Guides/Users/<USER>
</Warning>

```java
// userID 最大 32 字节的字符串。仅支持数字，英文字符 和 '!', '#', '$', '%', '&', '(', ')', '+', '-', ':', ';', '<', '=', '.', '>', '?', '@', '[', ']', '^', '_', '{', '}', '|', '~'。
// userName 最大 256 字节的字符串，无特殊字符限制。
String userID = "";
ZIMLoginConfig config = new ZIMLoginConfig();
config.userName = "";
// 使用 Token 鉴权，需要开发者填入 "使用 Token 鉴权" 文档生成的 Token，详情请参考 [使用 Token 鉴权]
// 使用 AppSign 鉴权 (2.3.0 或以上版本的默认鉴权方式)，Token 参数填空字符串
config.token = "";

zim.login(userID, config, new ZIMLoggedInCallback() {
    @Override
    public void onLoggedIn(ZIMError error) {
          // 开发者可根据 ZIMError 来判断是否登录成功。
    }
 });
```

#### 5. 发送单聊消息

客户端 A 登录成功后，可以向客户端 B 发送消息。

目前 ZIM 支持的消息类型如下：

<MarkMessageType />

以下为发送`单聊文本消息`为例：客户端 A 可以调用 [sendMessage](@sendMessage) 接口，传入客户端 B 的 userID、消息内容、消息类型 [ZIMConversationType](@-ZIMConversationType)，即可发送一条`文本消息`到 B 的客户端。

- [onMessageSent](@onMessageSent) 回调，判断消息是否发送成功。
- [onMessageAttached](@onMessageAttached) 回调，在消息发送前，可以获得一个临时的 [ZIMMessage](@-ZIMMessage)，以便您添加一些业务处理逻辑。例如：在发送消息前，获取到该条消息的 ID 信息。开发者在发送“视频”等内容较大的消息时，可以在消息上传完成前，获取对应该条消息的 localMessageID，实现发送前 Loading 的效果。

```java
// 以下为单聊场景

// conversationID 为 消息需要发送的会话 ID
// 单聊时，conversationID 即是对方的 userID；群组时，conversationID 即是群组的 groupID；房间时，conversationID 即是房间的 roomID
String conversationID = "xxxx";

ZIMTextMessage zimMessage = new ZIMTextMessage();
zimMessage.message = "消息内容";

ZIMMessageSendConfig config = new ZIMMessageSendConfig();
// 消息优先级，取值为 低:1 默认,中:2,高:3
config.priority = ZIMMessagePriority.LOW;
// 设置消息的离线推送配置
ZIMPushConfig pushConfig = new ZIMPushConfig();
pushConfig.title = "离线推送的标题";
pushConfig.content= "离线推送的内容";
pushConfig.payload = "离线推送的扩展信息";
config.pushConfig = pushConfig;

// 在单聊场景中，ZIMConversationType 设置为 PEER
zim.sendMessage(zimMessage, conversationID, ZIMConversationType.PEER, config, new ZIMMessageSentCallback() {
    @Override
    public void onMessageAttached(ZIMMessage zimMessage){
         // 发送前的回调，客户可以在这里获取一个临时对象，该对象与开发者创建的 zimMessage 对象属于同一对象，开发者可利用此特性做一些业务逻辑，如提前展示 UI 等
    }
    @Override
    public void onMessageSent(ZIMMessage zimMessage, ZIMError error) {
        // 开发者可以通过该回调监听消息是否发送成功。
    }
});
```

#### 6. 接收单聊消息

客户端 B 登录 ZIM 后，将会收到在 [setEventHandler](@setEventHandler) 回调中设置的 [onPeerMessageReceived](@onPeerMessageReceived) 监听接口，收到客户端 A 发送过来的消息。

<Warning title="注意">

收到消息时，由于类型是基类，首先需要判断消息类型是 Text 还是 Command，开发者需要强转基类为具体的子类，然后从 message 字段获取消息内容。
</Warning>

```java
zim.setEventHandler(new ZIMEventHandler() {
    @Override
    public void onPeerMessageReceived(ZIM zim, ArrayList<ZIMMessage> messageList, ZIMMessageReceivedInfo info, String fromUserID) {
      for (ZIMMessage zimMessage : messageList) {
          if (zimMessage instanceof ZIMTextMessage)
          {
            ZIMTextMessage zimTextMessage = (ZIMTextMessage) zimMessage;
            Log.e(TAG, "收到的消息:"+ zimTextMessage.message);
          }
      }
   }
});
```

#### 7. 退出登录

如果客户端需要退出登录，直接调用 [logout](@logout) 接口即可。

```java
zim.logout();
```

#### 8. 销毁 ZIM 实例

如果不需要 ZIM 实例，可直接调用 [destroy](@destroy) 接口，销毁实例。

```java
zim.destroy();
```

### 3.2 API 时序图

通过以上的实现流程描述，API 接口调用时序图如下：

<MessageSequenceDiagramCommon />

<MarkSendMessageEvent platform="android" />

## 相关文档

- [如何获取 SDK 的堆栈信息？](https://doc-zh.zego.im/faq/IM_sdkStack)
- [如何获取 SDK 的日志信息？](https://doc-zh.zego.im/faq/IM_sdkLog)
- [如何设置消息的优先级更为合理？](https://doc-zh.zego.im/faq/IM_Message_Priority)
- [什么时候使用自定义消息？](https://doc-zh.zego.im/faq/IM_CustomMessage)
- [如何限制只有好友之间才能互发消息？](https://doc-zh.zego.im/faq/IM_FriendMeassge)
- [支持发送消息给自己吗？](http://doc-zh.zego.im/faq/IM_send_toSelf)
