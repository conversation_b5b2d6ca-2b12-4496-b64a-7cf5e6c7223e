---
articleID: 15349
---

import MessageSequenceDiagramJS from '/core_products/zim/zh/snippets/MessageSequenceDiagramJSzh.mdx'
import MarkMessageType from '/core_products/zim/zh/snippets/MessageType.mdx'
import MarkSendMessageEvent from '/core_products/zim/zh/snippets/SendMessageEvent.mdx'

# 实现基本消息收发

---

本文介绍如何使用 ZIM SDK 快速实现基本的单聊会话消息收发功能。
<Note title="说明">

uni-app x SDK 是一个包括原生 iOS/Android 以及 Web/小程序 平台 ZIM SDK 的 UTS 插件。
</Note>


## 1 前提条件

在使用 ZIM SDK 前，请确保：

- 已在 [ZEGO 控制台](https://console.zego.im) 创建项目，获取到了接入 ZIM SDK 服务所需的 AppID、AppSign。ZIM 服务权限不是默认开启的，使用前，请先在 [ZEGO 控制台](https://console.zego.im) 自助开通 ZIM 服务（详情请参考 [项目管理 - 即时通讯](https://doc-zh.zego.im/article/14994)），若无法开通 ZIM 服务，请联系 ZEGO 技术支持开通。
- 已获取登录 SDK 所需的 Token，详情请参考 [使用 Token 鉴权](./Guides/Users/<USER>

## 2 集成 SDK

### 2.1 （可选）新建项目

<Accordion title="此步骤以如何创建新项目为例，如果是集成到已有项目，可忽略此步。" defaultOpen="false">
1. 启动 HBuilderX，选择“文件 > 新建 > 项目”菜单。

<Frame width="512" height="auto" caption=""><Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/QuickStart/New_Project_uniapp.png" /></Frame></Frame>

2. 在出现的表单中，选择 “uni-app” 平台，并勾选 “uni-app x” 后填写项目名称。

<Frame width="512" height="auto" caption=""><Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/QuickStart/Choose_platform_uniappx.png" /></Frame></Frame>

3. 单击“创建”，即可创建项目。

</Accordion>

### 2.2 导入 SDK

以下两种方式可以任选一种导入。

<Tabs>
<Tab title="从 ZEGO 官网下载">
1. 请参考 [下载](./Client%20SDKs/SDK%20downloads.mdx) 页面，获取最新版本的 SDK 到本地，并将得到的 “zego-zim-uts.zip” 文件解压缩。

2. 将解压缩后的文件夹，直接复制到项目工程根目录下的 uni_modules 文件夹，如果没有该目录，请手动创建。

    <Frame width="512" height="auto" caption=""><Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/ZIM/UniAppX/unix_1.png" /></Frame></Frame>
</Tab>
<Tab title="从插件市场获取">
从 uni-app x 插件市场获取  [ZIM uni-app x SDK ](https://ext.dcloud.net.cn/plugin?name=zego-zim-uts)。

<Frame width="auto" height="auto" >
  <img src="https://media-resource.spreading.io/docuo/workspace741/896bc39e2e65b82d5670b01b7c131c30/0414706250.jpeg" alt="ZIMuniappx.jpeg"/>
</Frame>
</Tab>
</Tabs>

### 2.3 开发 Native 应用（自定义调试基座）

#### 2.3.1 制作自定义调试基座

<Note title="说明">

uni-app x 官方自定义调试基座使用说明，请参考 [使用自定义基座运行](https://uniapp.dcloud.net.cn/tutorial/run/run-app.html#customplayground)。
</Note>

1. 选择 “运行 > 运行到手机或模拟器 > 制作自定义调试基座” 菜单。

    <Frame width="512" height="auto" caption=""><Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/ZIM/UniApp/uni_1.png" /></Frame></Frame>

2. 在弹出的界面中，按照 [uni-app 教程](https://uniapp.dcloud.net.cn/tutorial/run/run-app.html#customplayground)，填写相关信息，并单击“打包”进行云打包。

    <Frame width="512" height="auto" caption=""><Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/ZIM/UniAppX/unix_2.png" /></Frame></Frame>

    打包成功后，控制台会收到 uni-app 的相关提示。

    <Frame width="512" height="auto" caption=""><Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/QuickStart/Packaged_successfully_uniapp.png" /></Frame></Frame>

#### 2.3.2 切换运行基座为自定义调试基座

自定义调试基座，请选择“运行 > 运行到手机或模拟器 > 运行到 Android App 基座 > 使用自定义基座运行”菜单。

### 2.4 开发 Web 或者 小程序应用

#### 2.4.1 在项目中引入 npm sdk

切换到项目 uni_modules/zego-zim-uts 目录下，安装 npm sdk 库，安装成功后可以在该目录下看到 node_modules 目录：

``` bash
cd uni_modules/zego-zim-uts/
npm run install
```

## 3 实现基本收发消息

以下流程中，我们以客户端 A 和 B 的消息交互为例，实现 1v1 通信功能：

<Frame width="512" height="auto" caption="">
  <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/ZIM/quick_start_Implementation_Web.png" /></Frame>
</Frame>


### 3.1 实现流程

请参考 [跑通示例源码](./Sample%20code.mdx) 获取源码。


#### 1. 导入 SDK 文件

请参考 [2.2 导入 SDK](#22-导入-sdk)，导入 SDK 文件。

#### 2. 创建 ZIM 实例

首先我们需要在项目中创建 ZIM 实例，一个实例对应的是一个用户，表示一个用户以客户端的身份登录系统。

例如，客户端 A、B 分别调用 [create](@create) 接口，传入在 [1 前提条件](#1-前提条件) 中获取到的 AppID，创建了 A、B 的实例：

```typescript
import {
    ZIM,
    ZIMAppConfig,
    ZIMLoginConfig,
    ZIMMessage,
    ZIMMessageSendConfig,
    ZIMMessageSendNotification
} from '@/uni_modules/zego-zim-uts';

// 静态同步方法，创建 zim 实例，传入 AppID 和 AppSign
// create 方法仅第一次调用时会创建 ZIM 实例，后续调用会返回 null。
ZIM.create({ appID: 0, appSign: '' } as ZIMAppConfig);
// 通过 getInstance 获取单实例，避免热更新导致 create 多次创建返回 null。
const zim = ZIM.getInstance();
```

#### 3. 监听回调事件

在客户端登录前，开发者需要注册 ZIM 中的事件回调，接收到 SDK 异常、收到消息等通知。

```typescript
// 注册监听“运行时错误信息”的回调
zim.onError(function (errorInfo) {
    console.log('error', errorInfo.code, errorInfo.message);
});

// 注册监听“网络连接状态变更”的回调
zim.onConnectionStateChanged(function ({ state, event, extendedData }) {
    console.log('connectionStateChanged', state, event, extendedData);
});

// 注册监听“收到单聊消息”的回调
zim.onPeerMessageReceived(function ({ messageList, info, fromConversationID }) {
    console.log('peerMessageReceived', messageList, info, fromConversationID);
});

// 注册监听“Token 即将过期”的回调
zim.onTokenWillExpire(function ({ second }) {
    console.log('tokenWillExpire', second);
    // 可以在这里调用 renewToken 接口来更新 token
    // 新 token 生成可以参考上文
    zim.renewToken(token)
        .then(function({ token }){
            // 更新成功
        })
        .catch(function(err){
            // 更新失败
        })
});
```

详细的接口介绍，请参考 [onConnectionStateChanged](@connectionStateChanged)、[onPeerMessageReceived](@peerMessageReceived)、[onTokenWillExpire](@tokenWillExpire)。

<a id="login"></a>

#### 4. 登录 ZIM

创建实例后，客户端 A 和 B 需要登录 ZIM，只有登录成功后才可以开始发送、接收消息等。

客户端需要使用各自的用户信息进行登录。调用 [login](@login__2) 接口进行登录，传入 `userID` 和 [ZIMLoginConfig](@-ZIMLoginConfig) 对象，进行登录。

<Warning title="注意">

- “userID”、“userName” 支持开发者自定义规则生成。建议开发者将 “userID” 设置为一个有意义的值，可将其与自己的业务账号系统进行关联。
- 默认鉴权方式为 “AppSign 鉴权”，登录 ZIM 时Token 传入空字符串即可。
- 如果您使用的是 “Token 鉴权”，请参考 [使用 Token 鉴权](./Guides/Users/<USER>
</Warning>

```typescript
// userID 最大 32 字节的字符串。仅支持数字，英文字符 和 '!', '#', '$', '%', '&', '(', ')', '+', '-', ':', ';', '<', '=', '.', '>', '?', '@', '[', ']', '^', '_', '{', '}', '|', '~'。
// userName 最大 256 字节的字符串，无特殊字符限制。
const userID = 'xxxx';
const config: ZIMLoginConfig = {
    userName: 'xxxx',
    token: '',
    customStatus: '',
    isOfflineLogin: false,
};

// 登录时：
// 使用 Token 鉴权，需要开发者填入 "使用 Token 鉴权" 文档生成的 Token，详情请参考 [使用 Token 鉴权]
// 使用 AppSign 鉴权 (2.3.0 或以上版本的默认鉴权方式)，Token 参数填空字符串

zim.login(userID, config)
    .then(function () {
        // 登录成功
    })
    .catch(function (err) {
        // 登录失败
    });
```

#### 5. 发送消息

客户端 A 登录成功后，可以向客户端 B 发送消息。


目前 ZIM 支持的消息类型如下：

<MarkMessageType />

以下为发送`单聊文本消息`为例：客户端 A 可以调用 [sendMessage](@sendMessage) 接口，传入客户端 B 的 userID、消息内容、消息类型 conversationType 等参数，即可发送一条`文本消息`到 B 的客户端。

- [ZIMMessageSentResult](@-ZIMMessageSentResult) 回调，判断消息是否发送成功。
- [onMessageAttached](@onMessageAttached-ZIMMessageSendNotification) 回调，在消息发送前，可以获得一个临时的 [ZIMMessage](@-ZIMMessage)，以便您添加一些业务处理逻辑。例如：在发送消息前，获取到该条消息的 ID 信息。开发者在发送“视频”等内容较大的消息时，可以在消息上传完成前，缓存该消息对象，直到收到 SDK 发送成功通知时，通过比较对象相同来实现发送前 Loading 的效果。

```typescript
// 发送单聊 `Text` 信息

const toConversationID = ''; // 对方 userID
const conversationType = 0; // 会话类型，取值为 单聊：0，房间：1，群组：2
const config: ZIMMessageSendConfig = {
    priority: 1, // 设置消息优先级，取值为 低：1（默认），中：2，高：3
};
const notification: ZIMMessageSendNotification = {
    onMessageAttached: function(message) {
        // todo: Loading
    }
}

const messageTextObj: ZIMMessage = { type: 1, message: 'xxxx' };

zim.sendMessage(messageTextObj, toConversationID, conversationType, config, notification)
    .then(function ({ message }) {
        // 发送成功
    })
    .catch(function (err) {
        // 发送失败
    });
```

#### 6. 接收消息

客户端 B 登录 ZIM 后，通过 [onPeerMessageReceived](@peerMessageReceived) 监听接口，收到客户端 A 发送过来的消息。


```typescript
// 注册监听“收到单聊消息”的回调
zim.onPeerMessageReceived(function ({ messageList, info, fromConversationID }) {
    console.log('peerMessageReceived', messageList, info, fromConversationID);
});
```

#### 7. 退出登录

如果客户端需要退出登录，直接调用 [logout](@logout) 接口即可。

```java
zim.logout();
```

#### 8. 销毁 ZIM 实例

如果不需要 ZIM 实例，可直接调用 [destroy](@destroy) 接口，销毁实例。

```java
zim.destroy();
```

### 3.2 API 时序图

通过以上的实现流程描述，API 接口调用时序图如下：

<MessageSequenceDiagramJS />

<MarkSendMessageEvent platform="UTS" />

## 相关文档

- [如何获取 SDK 的日志信息？](https://doc-zh.zego.im/faq/IM_sdkLog)
- [如何设置消息的优先级更为合理？](https://doc-zh.zego.im/faq/IM_Message_Priority)
- [什么时候使用自定义消息？](https://doc-zh.zego.im/faq/IM_CustomMessage)
- [如何限制只有好友之间才能互发消息？](https://doc-zh.zego.im/faq/IM_FriendMeassge)
- [支持发送消息给自己吗？](http://doc-zh.zego.im/faq/IM_send_toSelf)
