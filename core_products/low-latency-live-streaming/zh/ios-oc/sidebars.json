{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 13404}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 20978}, {"type": "doc", "label": "基本概念", "id": "introduction/basic-concept", "articleID": 14946}, {"type": "doc", "label": "产品优势", "id": "introduction/product-advantages", "articleID": 14948}, {"type": "doc", "label": "应用场景", "id": "introduction/application-scenarios", "articleID": 14696}, {"type": "doc", "label": "限制说明", "id": "introduction/restriction", "articleID": 14702}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 13406}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 13408}, {"type": "category", "label": "升级指南", "collapsed": false, "items": [{"type": "doc", "label": "3.0.0 版本及以上升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3", "articleID": 16564}]}, {"type": "category", "label": "常用错误码", "collapsed": false, "items": [{"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 13784}]}]}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "category", "label": "基础服务", "collapsed": false, "items": [{"type": "doc", "label": "超低延迟直播价格说明", "id": "pricing/basic-services/l3", "articleID": 14711}]}, {"type": "category", "label": "增值服务", "collapsed": false, "items": [{"type": "doc", "label": "服务端混流价格说明", "id": "pricing/value-added-services/server-side-stream-mixing", "articleID": 14714}, {"type": "doc", "label": "直播连麦价格说明", "id": "pricing/value-added-services/co-hosting", "articleID": 14723}, {"type": "doc", "label": "CDN 直播价格说明", "id": "pricing/value-added-services/cdn-live-streaming", "articleID": 14705}]}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 13411}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 13413}, {"type": "doc", "label": "快速实现超低延迟直播", "id": "quick-start/implementing-live-streaming", "articleID": 13415}, {"type": "doc", "label": "场景化音视频配置", "id": "quick-start/scenario-based-audio-video-configuration", "articleID": 16540}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 14776, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "单流转码", "id": "live-streaming/single-stream-transcoding", "articleID": 17786, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "RTMP 推流到 ZEGO 服务器", "id": "live-streaming/obs-push", "articleID": 14736, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/use-token-authentication", "articleID": 14755, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 14770, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "网络测速", "id": "communication/testing-network", "articleID": 14767, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "设备检测", "id": "communication/pre-call-detection", "articleID": 14764, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "多源采集", "id": "communication/multi-source-capture", "articleID": 16910, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "同时推多路流", "id": "communication/push-multiple-streams", "articleID": 14794, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "媒体补充增强信息（SEI）", "id": "communication/sei", "articleID": 14841, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "云代理", "id": "communication/cloud-proxy", "articleID": 18103, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "音视频流加密", "id": "communication/encrypt-streams", "articleID": 14844, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "游戏语音", "id": "communication/range-audio", "articleID": 16839, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "房间连接状态说明", "id": "room/room-connection-status", "articleID": 14773, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 14782, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "登录多房间", "id": "room/multi-room-login", "articleID": 14838, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音频频谱与音量变化", "id": "audio/sound-level-spectrum", "articleID": 14815, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 14818, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音频 3A 处理", "id": "audio/audio-3a-processing", "articleID": 14824, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "变声/混响/立体声", "id": "audio/audio-effects", "articleID": 14812, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "场景化 AI 降噪", "id": "audio/scenario-based-ai-noise-reduction", "articleID": 14827, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频采集与渲染", "id": "audio/custom-audio-capture-and-rendering", "articleID": 14831, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频处理", "id": "audio/custom-audio-processing", "articleID": 14835, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "AI 变声", "id": "audio/ai-voice-changer", "articleID": 18440, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "原始音频数据获取", "id": "audio/get-audio-raw-data", "articleID": 21542}]}, {"type": "category", "label": "视频能力", "collapsed": false, "items": [{"type": "doc", "label": "常用视频配置", "id": "video/common-video-configuration", "articleID": 14758, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "视频采集旋转", "id": "video/video-capture-rotation", "articleID": 16145, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "屏幕共享", "id": "video/screen-sharing", "articleID": 14733, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "水印和截图", "id": "video/watermark-and-screenshot", "articleID": 14788, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "设置视频编码方式", "id": "video/set-video-encoding", "articleID": 14791, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频采集", "id": "video/custom-video-capture", "articleID": 14803, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频渲染", "id": "video/custom-video-rendering", "articleID": 14809, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频前处理", "id": "video/custom-video-preprocessing", "articleID": 14806, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "超分辨率", "id": "video/super-resolution", "articleID": 16516, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "主体分割", "id": "video/object-segmentation", "articleID": 16521, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "H.265", "id": "video/h265", "articleID": 14800, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "视频大小流和分层编码", "id": "video/small-large-video-stream-and-layered-encoding", "articleID": 17954, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "推流视频增强", "id": "video/publish-video-enhancement", "articleID": 18880, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "其他能力", "collapsed": false, "items": [{"type": "doc", "label": "媒体播放器", "id": "other/media-player", "articleID": 14779, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音效文件播放器", "id": "other/audio-effect-player", "articleID": 14821, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音视频录制", "id": "other/local-media-recording", "articleID": 14797, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "将白板推送到第三方平台", "id": "other/push-the-whiteboard", "articleID": 20784, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "播放透明礼物特效", "id": "other/play-transparent-gift-effects", "articleID": 17500, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "最佳实践", "collapsed": false, "items": [{"type": "doc", "label": "和 AI 美颜的搭配使用", "id": "best-practice/integration-with-zego-effects-sdk", "articleID": 14847}, {"type": "doc", "label": "秀场直播秒开方案", "id": "best-practice/instant-startup-solution", "articleID": 20789}, {"type": "doc", "label": "调试与配置", "id": "best-practice/debug-and-config", "articleID": 14851}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19750", "articleID": 19750}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=HybridHierarchicalDeliverySystem&platform=ios"}]}