{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 16015}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 20981}, {"type": "doc", "label": "基本概念", "id": "introduction/basic-concept", "articleID": 16016}, {"type": "doc", "label": "产品优势", "id": "introduction/product-advantages", "articleID": 16017}, {"type": "doc", "label": "应用场景", "id": "introduction/application-scenarios", "articleID": 16018}, {"type": "doc", "label": "限制说明", "id": "introduction/restriction", "articleID": 16021}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 16028}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 16020}, {"type": "category", "label": "升级指南", "collapsed": false, "items": [{"type": "doc", "label": "3.0.0 版本及以上升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3", "articleID": 18530}]}, {"type": "category", "label": "常用错误码", "collapsed": false, "items": [{"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 16049}]}]}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "category", "label": "基础服务", "collapsed": false, "items": [{"type": "doc", "label": "超低延迟直播价格说明", "id": "pricing/basic-services/l3", "articleID": 16023}]}, {"type": "category", "label": "增值服务", "collapsed": false, "items": [{"type": "doc", "label": "服务端混流价格说明", "id": "pricing/value-added-services/server-side-stream-mixing", "articleID": 16024}, {"type": "doc", "label": "直播连麦价格说明", "id": "pricing/value-added-services/co-hosting", "articleID": 16027}, {"type": "doc", "label": "CDN 直播价格说明", "id": "pricing/value-added-services/cdn-live-streaming", "articleID": 16058}]}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 16051}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 16029}, {"type": "doc", "label": "快速实现超低延迟直播", "id": "quick-start/implementing-live-streaming", "articleID": 16050}, {"type": "doc", "label": "场景化音视频配置", "id": "quick-start/scenario-based-audio-video-configuration", "articleID": 16836}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 16061, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "单流转码", "id": "live-streaming/single-stream-transcoding", "articleID": 21539, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "RTMP 推流到 ZEGO 服务器", "id": "live-streaming/obs-push", "articleID": 16835, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "使用本地导播", "id": "live-streaming/use-local-broadcast", "articleID": 18454, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/use-token-authentication", "articleID": 16030, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 16033, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "网络测速", "id": "communication/testing-network", "articleID": 16032, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "设备检测", "id": "communication/pre-call-detection", "articleID": 16060, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "同时推多路流", "id": "communication/push-multiple-streams", "articleID": 16040, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "媒体补充增强信息（SEI）", "id": "communication/sei", "articleID": 16047, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "云代理", "id": "communication/cloud-proxy", "articleID": 18900, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "音视频轨道替换", "id": "communication/media-track-replacement", "articleID": 16071, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "游戏语音", "id": "communication/range-audio", "articleID": 16838, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "房间连接状态说明", "id": "room/room-connection-status", "articleID": 16034, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 16036, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "登录多房间", "id": "room/multi-room-login", "articleID": 16046, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音频频谱与音量变化", "id": "audio/sound-level-spectrum", "articleID": 16062, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 16042, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音频 3A 处理", "id": "audio/audio-3a-processing", "articleID": 16044, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "变声/混响/立体声", "id": "audio/audio-effects", "articleID": 18148, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "混音", "id": "audio/audio-aux", "articleID": 16073, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "AI 降噪", "id": "audio/scenario-based-ai-noise-reduction", "articleID": 16571, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频采集与渲染", "id": "audio/custom-audio-capture-and-rendering", "articleID": 16063, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "原始音频数据获取", "id": "audio/get-audio-raw-data", "articleID": 21558, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "视频能力", "collapsed": false, "items": [{"type": "doc", "label": "常用视频配置", "id": "video/common-video-configuration", "articleID": 16059, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "基础美颜", "id": "video/basic-beauty", "articleID": 16037, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "屏幕共享", "id": "video/screen-sharing", "articleID": 16053, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "设置视频编码方式", "id": "video/set-video-encoding", "articleID": 16039, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频采集", "id": "video/custom-video-capture", "articleID": 16041, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "背景虚化及虚拟背景", "id": "video/background-virtualization", "articleID": 17811, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "其他能力", "collapsed": false, "items": [{"type": "doc", "label": "音效文件播放器", "id": "other/audio-effect-player", "articleID": 16043, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "将白板推送到第三方平台", "id": "other/push-the-whiteboard", "articleID": 20786, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "最佳实践", "collapsed": false, "items": [{"type": "doc", "label": "视频编码格式选择", "id": "best-practice/set-video-encoding", "articleID": 16075}, {"type": "category", "label": "浏览器渲染及播放", "collapsed": false, "items": [{"type": "doc", "label": "使用 Vue 实现音视频功能", "id": "best-practice/framework/vue", "articleID": 16078}, {"type": "doc", "label": "使用 Angular 实现音视频功能", "id": "best-practice/framework/angular", "articleID": 16080}, {"type": "doc", "label": "使用 React 实现音视频功能", "id": "best-practice/framework/react", "articleID": 16082}, {"type": "doc", "label": "使用 uni-app 在浏览器渲染和播放（Web）", "id": "best-practice/framework/uni-app", "articleID": 16084}]}, {"type": "doc", "label": "自动播放策略", "id": "best-practice/autoplay-policy", "articleID": 16086}, {"type": "doc", "label": "调试与配置", "id": "best-practice/debug-and-config", "articleID": 16048}, {"type": "doc", "label": "限制说明", "id": "introduction/restriction", "articleID": 16088}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19755", "articleID": 19755}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=HybridHierarchicalDeliverySystem&platform=web"}]}