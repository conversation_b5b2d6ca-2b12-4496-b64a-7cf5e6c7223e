{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 21117}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 21134}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 21123}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 21273}, {"type": "category", "label": "常用错误码", "collapsed": false, "items": [{"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 21132}]}]}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "category", "label": "基础服务", "collapsed": false, "items": [{"type": "doc", "label": "超低延迟直播价格说明", "id": "pricing/basic-services/l3", "articleID": 21118}]}, {"type": "category", "label": "增值服务", "collapsed": false, "items": [{"type": "doc", "label": "服务端混流价格说明", "id": "pricing/value-added-services/server-side-stream-mixing", "articleID": 21119}, {"type": "doc", "label": "直播连麦价格说明", "id": "pricing/value-added-services/co-hosting", "articleID": 21121}, {"type": "doc", "label": "CDN 直播价格说明", "id": "pricing/value-added-services/cdn-live-streaming", "articleID": 21122}]}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 21124}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 21125}, {"type": "doc", "label": "快速实现超低延迟直播", "id": "quick-start/implementing-live-streaming", "articleID": 21036}, {"type": "doc", "label": "场景化音视频配置", "id": "quick-start/scenario-based-audio-video-configuration", "articleID": 21187}, {"type": "doc", "label": "设置回调", "id": "quick-start/setting-callback", "articleID": 21397}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 21126, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/use-token-authentication", "articleID": 21127, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "多源采集", "id": "communication/multi-source-capture", "articleID": 21128, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "视频能力", "collapsed": false, "items": [{"type": "doc", "label": "常用视频配置", "id": "video/common-video-configuration", "articleID": 21129, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "屏幕共享", "id": "video/screen-sharing", "articleID": 21130, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "主体分割", "id": "video/object-segmentation", "articleID": 21131, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/21133", "articleID": 21133}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=HybridHierarchicalDeliverySystem&platform=electron"}]}