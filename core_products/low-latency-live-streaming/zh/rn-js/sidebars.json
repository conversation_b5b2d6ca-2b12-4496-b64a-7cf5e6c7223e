{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 20994}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 21038}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 21000}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 20995}, {"type": "category", "label": "常用错误码", "collapsed": false, "items": [{"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 21029}]}]}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "category", "label": "基础服务", "collapsed": false, "items": [{"type": "doc", "label": "超低延迟直播价格说明", "id": "pricing/basic-services/l3", "articleID": 20996}]}, {"type": "category", "label": "增值服务", "collapsed": false, "items": [{"type": "doc", "label": "服务端混流价格说明", "id": "pricing/value-added-services/server-side-stream-mixing", "articleID": 20997}, {"type": "doc", "label": "直播连麦价格说明", "id": "pricing/value-added-services/co-hosting", "articleID": 20998}, {"type": "doc", "label": "CDN 直播价格说明", "id": "pricing/value-added-services/cdn-live-streaming", "articleID": 20999}]}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 21001}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 21002}, {"type": "doc", "label": "快速实现超低延迟直播", "id": "quick-start/implementing-live-streaming", "articleID": 21003}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 21005, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "RTMP 推流到 ZEGO 服务器", "id": "live-streaming/obs-push", "articleID": 21004, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/use-token-authentication", "articleID": 21006, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 21007, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "网络测速", "id": "communication/testing-network", "articleID": 21009, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "多源采集", "id": "communication/multi-source-capture", "articleID": 21182, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 21010, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "登录多房间", "id": "room/multi-room-login", "articleID": 21011, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音量变化", "id": "audio/captured-sound", "articleID": 21014, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 21067, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "视频能力", "collapsed": false, "items": [{"type": "doc", "label": "常用视频配置", "id": "video/common-video-configuration", "articleID": 21018, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "视频画面旋转", "id": "video/video-capture-rotation", "articleID": 21019, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "屏幕共享", "id": "video/screen-sharing", "articleID": 21020, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "设置视频编码方式", "id": "video/set-video-encoding", "articleID": 21021, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频采集", "id": "video/custom-video-capture", "articleID": 21022, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "主体分割", "id": "video/object-segmentation", "articleID": 21023, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "H.265", "id": "video/h265", "articleID": 21024, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "其他能力", "collapsed": false, "items": [{"type": "doc", "label": "媒体播放器", "id": "other/media-player", "articleID": 21025, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音视频录制", "id": "other/local-media-recording", "articleID": 21026, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "播放透明礼物特效", "id": "other/play-transparent-gift-effects", "articleID": 21027, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "最佳实践", "collapsed": false, "items": [{"type": "doc", "label": "限制说明", "id": "best-practice/restrictions", "articleID": 21028}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/21064", "articleID": 21064}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=HybridHierarchicalDeliverySystem&platform=react-native"}]}