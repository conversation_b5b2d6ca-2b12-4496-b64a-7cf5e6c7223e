{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 17185}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 20982}, {"type": "doc", "label": "基本概念", "id": "introduction/basic-concept", "articleID": 17140}, {"type": "doc", "label": "产品优势", "id": "introduction/product-advantages", "articleID": 17141}, {"type": "doc", "label": "应用场景", "id": "introduction/application-scenarios", "articleID": 17142}, {"type": "doc", "label": "限制说明", "id": "introduction/restriction", "articleID": 17143}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 17150}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 17144}, {"type": "category", "label": "升级指南", "collapsed": false, "items": [{"type": "doc", "label": "3.0.0 版本及以上升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3", "articleID": 17210}, {"type": "doc", "label": "3.8.1 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3-81", "articleID": 18184}]}, {"type": "category", "label": "常用错误码", "collapsed": false, "items": [{"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 17182}]}]}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "category", "label": "基础服务", "collapsed": false, "items": [{"type": "doc", "label": "超低延迟直播价格说明", "id": "pricing/basic-services/l3", "articleID": 17147}]}, {"type": "category", "label": "增值服务", "collapsed": false, "items": [{"type": "doc", "label": "服务端混流价格说明", "id": "pricing/value-added-services/server-side-stream-mixing", "articleID": 17148}, {"type": "doc", "label": "直播连麦价格说明", "id": "pricing/value-added-services/co-hosting", "articleID": 17149}, {"type": "doc", "label": "CDN 直播价格说明", "id": "pricing/value-added-services/cdn-live-streaming", "articleID": 17145}]}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 17152}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 17151}, {"type": "doc", "label": "快速实现超低延迟直播", "id": "quick-start/implementing-live-streaming", "articleID": 17184}, {"type": "doc", "label": "场景化音视频配置", "id": "quick-start/scenario-based-audio-video-configuration", "articleID": 17153}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 17164, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "单流转码", "id": "live-streaming/single-stream-transcoding", "articleID": 18304, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "RTMP 推流到 ZEGO 服务器", "id": "live-streaming/obs-push", "articleID": 17188, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/use-token-authentication", "articleID": 17157, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 17162, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "网络测速", "id": "communication/testing-network", "articleID": 17161, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "设备检测", "id": "communication/pre-call-detection", "articleID": 17160, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "多源采集", "id": "communication/multi-source-capture", "articleID": 17209, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "媒体补充增强信息（SEI）", "id": "communication/sei", "articleID": 17178, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "云代理", "id": "communication/cloud-proxy", "articleID": 18185, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "游戏语音", "id": "communication/range-audio", "articleID": 17180, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "房间连接状态说明", "id": "room/room-connection-status", "articleID": 17163, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 17165, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "登录多房间", "id": "room/multi-room-login", "articleID": 17177, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音频频谱与音量变化", "id": "audio/sound-level-spectrum", "articleID": 17172, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 17173, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音频 3A 处理", "id": "audio/audio-3a-processing", "articleID": 17175, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "变声/混响/立体声", "id": "audio/audio-effects", "articleID": 17171, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "场景化 AI 降噪", "id": "audio/scenario-based-ai-noise-reduction", "articleID": 17176, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频采集与渲染", "id": "audio/custom-audio-capture-and-rendering", "articleID": 17216, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "AI 变声", "id": "audio/ai-voice-changer", "articleID": 18595, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "视频能力", "collapsed": false, "items": [{"type": "doc", "label": "常用视频配置", "id": "video/common-video-configuration", "articleID": 17158, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "视频画面旋转", "id": "video/video-capture-rotation", "articleID": 17214, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "视频采集旋转", "id": "video/video-capture-rotation", "articleID": 17159, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "屏幕共享", "id": "video/screen-sharing", "articleID": 17195, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "水印和截图", "id": "video/watermark-and-screenshot", "articleID": 17167, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "设置视频编码方式", "id": "video/set-video-encoding", "articleID": 17168, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频采集", "id": "video/custom-video-capture", "articleID": 21494, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频渲染", "id": "video/custom-video-rendering", "articleID": 21495, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频前处理", "id": "video/custom-video-preprocessing", "articleID": 21497, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "超分辨率", "id": "video/super-resolution", "articleID": 17179, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "主体分割", "id": "video/object-segmentation", "articleID": 17918, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "H.265", "id": "video/h265", "articleID": 17170, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "视频大小流和分层编码", "id": "video/small-large-video-stream-and-layered-encoding", "articleID": 18031, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "推流视频增强", "id": "video/publish-video-enhancement", "articleID": 18929, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "其他能力", "collapsed": false, "items": [{"type": "doc", "label": "媒体播放器", "id": "other/media-player", "articleID": 17215, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音效文件播放器", "id": "other/audio-effect-player", "articleID": 17174, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音视频录制", "id": "other/local-media-recording", "articleID": 17169, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "将白板推送到第三方平台", "id": "other/push-the-whiteboard", "articleID": 20787, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "最佳实践", "collapsed": false, "items": [{"type": "doc", "label": "调试与配置", "id": "best-practice/debug-and-config", "articleID": 17181}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19754", "articleID": 19754}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=HybridHierarchicalDeliverySystem&platform=flutter"}]}