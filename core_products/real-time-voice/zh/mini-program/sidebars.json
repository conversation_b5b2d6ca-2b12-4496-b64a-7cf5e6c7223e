{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 5624}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 16696}, {"type": "category", "label": "发布日志", "collapsed": false, "items": [{"type": "doc", "label": "微信小程序发布日志", "id": "client-sdk/release-notes/we-chat", "articleID": 18254}, {"type": "doc", "label": "支付宝小程序发布日志", "id": "client-sdk/release-notes/alipay", "articleID": 18256}]}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "doc", "label": "实时语音价格说明", "id": "introduction/pricing/rtc", "articleID": 11472}, {"type": "doc", "label": "服务端混流价格说明", "id": "introduction/pricing/server-side-stream-mixing", "articleID": 13298}, {"type": "doc", "label": "CDN 直播价格说明", "id": "introduction/pricing/cdn-live-streaming", "articleID": 14118}]}, {"type": "doc", "label": "限制说明", "id": "best-practice/restrictions", "articleID": 7595}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 6201}, {"type": "category", "label": "升级指南", "collapsed": false, "items": [{"type": "doc", "label": "3.0.0 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3", "articleID": 18287}]}, {"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 10169}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "category", "label": "微信小程序", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/we-chat/run-example-code", "articleID": 18274}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/we-chat/integrating-sdk", "articleID": 18273}, {"type": "doc", "label": "实现音频通话", "id": "quick-start/we-chat/implementing-voice-call", "articleID": 18272}]}, {"type": "category", "label": "支付宝小程序", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/alipay/run-example-code", "articleID": 18277}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/alipay/integrating-sdk", "articleID": 18281}, {"type": "doc", "label": "实现音频通话", "id": "quick-start/alipay/implementing-voice-call", "articleID": 18280}]}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/using-token-authentication", "articleID": 14050, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 4971, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 4970, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音频频谱与音量变化", "id": "audio/sound-level-spectrum", "articleID": 5762, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "混响", "id": "audio/reverberation", "articleID": 14190, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 4968, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "使用 CDN 直播", "id": "live-streaming/using-cdn-for-live-streaming", "articleID": 5758, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "CDN 推流鉴权", "id": "live-streaming/cdn-stream-publishing-authentication", "articleID": 15840, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通过 URL 拉流", "id": "live-streaming/playing-stream-by-url", "articleID": 5757, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "直推 CDN", "id": "live-streaming/direct-to-cdn", "articleID": 5761, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19739", "articleID": 19739}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=ExpressVideo&platform=wxxcx"}]}