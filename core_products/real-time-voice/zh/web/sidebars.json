{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 4827}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 16695}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "doc", "label": "实时语音价格说明", "id": "introduction/pricing/rtc", "articleID": 11471}, {"type": "doc", "label": "服务端混流价格说明", "id": "introduction/pricing/server-side-stream-mixing", "articleID": 13297}, {"type": "doc", "label": "CDN 直播价格说明", "id": "introduction/pricing/cdn-live-streaming", "articleID": 14117}]}, {"type": "doc", "label": "限制说明", "id": "best-practice/restrictions", "articleID": 7594}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 5171}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 12587}, {"type": "category", "label": "升级指南", "collapsed": false, "items": [{"type": "doc", "label": "3.0.0 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3", "articleID": 18531}]}, {"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 5696}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 4658}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 6839}, {"type": "doc", "label": "实现音频通话", "id": "quick-start/implementing-voice-call", "articleID": 9540}, {"type": "doc", "label": "场景化音视频配置", "id": "quick-start/scenario-based-audio-video-configuration", "articleID": 16810}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/using-token-authentication", "articleID": 14049, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 9542, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "媒体补充增强信息（SEI）", "id": "communication/sei", "articleID": 17932, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "流量控制", "id": "communication/traffic-control", "articleID": 21305, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "云代理", "id": "communication/cloud-proxy", "articleID": 18899, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "地理围栏", "id": "communication/geofencing", "articleID": 18149, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "游戏语音", "id": "communication/range-audio", "articleID": 12289, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "房间连接状态说明", "id": "room/room-connection-status", "articleID": 18663, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 4969, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音频频谱与音量变化", "id": "audio/sound-level-spectrum", "articleID": 5121, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 18065, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "变声/混响/立体声", "id": "audio/custom-audio-processing", "articleID": 18147, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "混音", "id": "audio/audio-mixing", "articleID": 5724, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "AI 降噪", "id": "audio/scenario-based-ai-noise-reduction", "articleID": 16492, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频采集", "id": "audio/custom-audio-capture-and-rendering", "articleID": 18034, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "原始音频数据获取", "id": "audio/get-audio-raw-data", "articleID": 21557, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 4967, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "使用 CDN 直播", "id": "live-streaming/using-cdn-for-live-streaming", "articleID": 18657, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "CDN 推流鉴权", "id": "live-streaming/cdn-stream-publishing-authentication", "articleID": 18658, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "超低延迟直播", "id": "live-streaming/low-latency-live-streaming", "articleID": 9710, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "使用本地导播", "id": "live-streaming/use-local-broadcast", "articleID": 18456, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "播放器插件", "collapsed": false, "items": [{"type": "doc", "label": "播放器简介", "id": "player-plugin/overview", "articleID": 18459}, {"type": "doc", "label": "发布日志", "id": "player-plugin/release-notes", "articleID": 18461}, {"type": "doc", "label": "接入指引", "id": "player-plugin/integration", "articleID": 18463}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19738", "articleID": 19738}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=ExpressVideo&platform=web"}]}