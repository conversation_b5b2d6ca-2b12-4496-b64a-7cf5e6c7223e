{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 13192}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 16697}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "doc", "label": "实时语音价格说明", "id": "introduction/pricing/rtc", "articleID": 13193}, {"type": "doc", "label": "服务端混流价格说明", "id": "introduction/pricing/server-side-stream-mixing", "articleID": 13299}, {"type": "doc", "label": "CDN 直播价格说明", "id": "introduction/pricing/cdn-live-streaming", "articleID": 14119}]}, {"type": "doc", "label": "实时音视频 SDK 与实时语音 SDK 差异", "id": "introduction/difference-between-audio-and-video-sdk", "articleID": 15807}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 13194}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 15906}, {"type": "category", "label": "升级指南", "collapsed": false, "items": [{"type": "doc", "label": "2.23.0 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v2-23", "articleID": 16200}, {"type": "doc", "label": "3.0.0 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3", "articleID": 16882}, {"type": "doc", "label": "3.8.1 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3-81", "articleID": 18182}]}, {"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 13198}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 13195}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 13196}, {"type": "doc", "label": "实现音频通话", "id": "quick-start/implementing-voice-call", "articleID": 13197}, {"type": "doc", "label": "场景化音视频配置", "id": "quick-start/scenario-based-audio-video-configuration", "articleID": 16879}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/using-token-authentication", "articleID": 14379, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话前检测", "id": "communication/pre-call-detection", "articleID": 15703, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 15304, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "网络测速", "id": "communication/testing-network", "articleID": 15848, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "媒体补充增强信息（SEI）", "id": "communication/sei", "articleID": 15711, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "云代理", "id": "communication/cloud-proxy", "articleID": 17138, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "地理围栏", "id": "communication/geofencing", "articleID": 17817, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "音视频流加密", "id": "communication/secure-encryption-audio", "articleID": 15727, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "游戏语音", "id": "communication/range-audio", "articleID": 15717, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "房间连接状态说明", "id": "room/room-connection-status", "articleID": 15706, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 15310, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "登录多房间", "id": "room/multi-room-login", "articleID": 15710, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音频频谱与音量变化", "id": "audio/sound-level-spectrum", "articleID": 15712, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 15713, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音频 3A 处理", "id": "audio/audio-3a-processing", "articleID": 15313, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "变声/混响/立体声", "id": "audio/custom-audio-processing", "articleID": 15312, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "场景化 AI 降噪", "id": "audio/scenario-based-ai-noise-reduction", "articleID": 16881, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "AI 变声", "id": "audio/ai-voice-changer", "articleID": 18594, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 CDN 直播", "id": "live-streaming/using-cdn-for-live-streaming", "articleID": 16345, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "CDN 推流鉴权", "id": "live-streaming/cdn-stream-publishing-authentication", "articleID": 15833, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通过 URL 拉流", "id": "live-streaming/playing-stream-by-url", "articleID": 15708, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "超低延迟直播", "id": "live-streaming/low-latency-live-streaming", "articleID": 15718, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "其他能力", "collapsed": false, "items": [{"type": "doc", "label": "音效文件播放器", "id": "other/audio-effect-player", "articleID": 15714, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "最佳实践", "collapsed": false, "items": [{"type": "doc", "label": "调试与配置", "id": "best-practice/debug-and-config", "articleID": 15725}, {"type": "doc", "label": "限制说明", "id": "best-practice/restrictions", "articleID": 15724}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19740", "articleID": 19740}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=ExpressVideo&platform=flutter"}]}