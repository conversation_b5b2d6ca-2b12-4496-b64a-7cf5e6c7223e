{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 17984}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 18857}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "doc", "label": "实时语音价格说明", "id": "introduction/pricing/rtc", "articleID": 17986}, {"type": "doc", "label": "服务端混流价格说明", "id": "introduction/pricing/server-side-stream-mixing", "articleID": 17987}, {"type": "doc", "label": "CDN 直播价格说明", "id": "introduction/pricing/cdn-live-streaming", "articleID": 17988}]}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 17989}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 17985}, {"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 18027}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 17990}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 17991}, {"type": "doc", "label": "实现音频通话", "id": "quick-start/implementing-voice-call", "articleID": 17992}, {"type": "doc", "label": "场景化音视频配置", "id": "quick-start/scenario-based-audio-video-configuration", "articleID": 17993}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/using-token-authentication", "articleID": 17994, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 17995, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "网络测速", "id": "communication/testing-network", "articleID": 18020, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "多源采集", "id": "communication/multi-source-capture", "articleID": 18025, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "同时推多路流", "id": "communication/push-multiple-streams", "articleID": 18003, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "媒体补充增强信息（SEI）", "id": "communication/sei", "articleID": 18005, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "流量控制", "id": "communication/traffic-control", "articleID": 18021, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "云代理", "id": "communication/cloud-proxy", "articleID": 18024, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "地理围栏", "id": "communication/geofencing", "articleID": 18026, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "游戏语音", "id": "communication/range-audio", "articleID": 18016, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "万人范围音视频", "id": "communication/mass-scale-range-audio-video", "articleID": 18022, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "多人状态实时同步", "id": "communication/sync-users-status", "articleID": 18023, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 18001, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "登录多房间", "id": "room/multi-room-login", "articleID": 18002, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音频频谱与音量变化", "id": "audio/sound-level-spectrum", "articleID": 18007, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 18066, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音频 3A 处理", "id": "audio/audio-3a-processing", "articleID": 18011, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "变声/混响/立体声", "id": "audio/custom-audio-processing", "articleID": 18006, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "混音", "id": "audio/audio-mixing", "articleID": 18019, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "场景化 AI 降噪", "id": "audio/scenario-based-ai-noise-reduction", "articleID": 18012, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频采集与渲染", "id": "audio/custom-audio-capture-and-rendering", "articleID": 18013, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频处理", "id": "audio/custom-audio-processing", "articleID": 18014, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "原始音频数据获取", "id": "audio/get-audio-raw-data", "articleID": 18015, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 17997, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "使用 CDN 直播", "id": "live-streaming/using-cdn-for-live-streaming", "articleID": 17998, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "CDN 推流鉴权", "id": "live-streaming/cdn-stream-publishing-authentication", "articleID": 17999, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通过 URL 拉流", "id": "live-streaming/playing-stream-by-url", "articleID": 17996, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "超低延迟直播", "id": "live-streaming/low-latency-live-streaming", "articleID": 18017, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "直推 CDN", "id": "live-streaming/direct-to-cdn", "articleID": 18018, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "其他能力", "collapsed": false, "items": [{"type": "doc", "label": "媒体播放器", "id": "other/media-player", "articleID": 18000, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音效文件播放器", "id": "other/audio-effect-player", "articleID": 18010, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音视频录制", "id": "other/record-local-media", "articleID": 18004, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19744", "articleID": 19744}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=ExpressVideo&platform"}]}