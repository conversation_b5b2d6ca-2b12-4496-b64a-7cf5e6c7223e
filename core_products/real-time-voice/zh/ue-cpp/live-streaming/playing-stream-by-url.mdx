---
articleID: 17996
---
# 通过 URL 拉流

---

## 功能简介

当推流端使用第三方推流工具（例如 OBS 软件、网络摄像头 IP Camera 等）将流推到 CDN 时，或通过使用 ZEGO SDK 转推 CDN 功能将音视频画面推送到第三方 CDN 上时，可使用直接传入 URL 地址的方式进行拉流。

## 前提条件

在实现 URL 拉流功能之前，请确保：

- 已在项目中集成 ZEGO Express SDK，实现基本的实时语音功能，详情请参考 [快速开始 - 集成](!ExpressAudioSDK-Integration/SDK_Integration) 和 [快速开始 - 实现流程](!ExpressAudioSDK-Integration/Solution_Implementation)。
- 已在 [ZEGO 控制台](https://console.zego.im) 创建项目，并申请有效的 AppID 和 AppSign，详情请参考 [控制台 - 项目管理](/console/project-info) 中的“项目信息”。
- 已联系 ZEGO 技术支持开通 URL 拉流功能。
- 已将音视频流推送到 CDN，并知晓相应的 URL，详情请参考 [使用 CDN 直播](!Publisher_Player_Advanced/RelayToCDN)。

## 使用步骤

### 配置拉流参数

直接通过 CDN 的 URL 地址拉流，需要使用 [ZegoCDNConfig](@-ZegoCDNConfig) 对象来填入 URL 参数，如果对应的 CDN 配置了拉流鉴权，还需要通过 “authParam” 字段填入鉴权参数。

<Note title="说明">


- 鉴权参数，即 URL 的 “?” 之后的字符串（不包括 “?”）。例如从 CDN 拉流的 URL 为 “rtmp://xxxx.yyy.zzz?a=qqq&b=www” 时，则鉴权参数为 “a=qqq&b=www”。
- 拉流 URL 的鉴权参数主要用于防盗链，具体鉴权规则请联系具体的 CDN 厂商或 ZEGO 技术支持咨询。若无鉴权参数，请忽略 “authParam” 字段。

</Note>



- 调用示例

    ```cpp
    // 设置 CDN 参数
    ZegoCDNConfig cdnConfig;
    // URL 为 CDN 拉流地址
    cdnConfig.url = "rtmp://xxxxxxxx";
    // 如果需要鉴权则要设置鉴权参数，如果不需要鉴权可以不设置（鉴权参数不能带"?"字符）
    cdnConfig.authParam = "xxx";

    config.cdnConfig = &cdnConfig;
    ```

### 开始拉流

<Warning title="注意">


- 通过 URL 拉流时，不能直接通过填入流 ID 进行拉流，实际拉流画面以 URL 为准。
- 虽此时流 ID 不能用于拉流，但 SDK 内部仍以流 ID 作为唯一标识，用于后续拉流相关回调中。因此 StreamID 仍需要在整个 AppID 内全局唯一。

</Warning>




- 调用示例

    ```cpp
    // 设置 CDN 参数
    ZegoCDNConfig cdnConfig;
    // URL 为 CDN 拉流地址
    cdnConfig.url = "rtmp://xxxxxxxx";
    // 如果需要鉴权则要设置鉴权参数，如果不需要鉴权可以不设置（鉴权参数不能带"?"字符）
    cdnConfig.authParam = "xxx";

    // 开始拉流
    ZegoPlayerConfig config;
    config.cdnConfig = &cdnConfig;
    // 填写了 url 参数后，sdk 会从 url 拉取音视频流，但此时依然需要传递一个唯一的 streamID 到 SDK，SDK 内部会以该 streamID 标识这条流
    engine->startPlayingStream(streamID, &canvas, config);

    // 停止拉流时传递的是拉流时传入的 streamID
    engine->stopPlayingStream(streamID);
    ```

拉流时，如果出现错误，请参考 [常见错误码 - 1004xxx 拉流相关的错误码](/real-time-voice-ue/client-sdk/error-code#5)。


### （可选）监听拉流相关事件通知

<Accordion title="监听拉流相关事件通知" defaultOpen="false">
可以通过 [onPlayerStateUpdate](https://doc-zh.zego.im/zh/api?doc=Express_Video_SDK_API~CPP~class~zego-express-i-zego-event-handler#on-player-state-update) 来监听从 CDN 拉流的结果。

<Warning title="注意">


如果不是使用 ZEGO SDK 进行推流，而是使用第三方推流工具直接进行推流、但是使用 ZEGO SDK 进行拉流，这种场景下推流方没有使用 ZEGO SDK 加入房间，拉流方默认收不到 [onRoomStreamUpdate ](https://doc-zh.zego.im/zh/api?doc=Express_Video_SDK_API~CPP~class~zego-express-i-zego-event-handler#on-room-stream-update) 的回调，可以使用 [后台流增加](https://doc-zh.zego.im/article/1333) 与 [后台流删除](https://doc-zh.zego.im/article/1331) 的功能，让拉流端可以收到 [onRoomStreamUpdate ](https://doc-zh.zego.im/zh/api?doc=Express_Video_SDK_API~CPP~class~zego-express-i-zego-event-handler#on-room-stream-update) 的回调。

</Warning>



- 接口原型

    ```cpp
    /**
     * 拉流状态变更回调
     *
     * 在拉流成功后，可以通过该回调接口获取拉流状态变更的通知。
     * 开发者可根据 state 参数是否在 [正在请求拉流状态] 来大体判断用户的拉流网络情况。
     *
     * @param streamID 流 ID
     * @param state 拉流状态
     * @param errorCode 拉流状态变更对应的错误码。请参考常见错误码文档 [https://doc-zh.zego.im/zh/4380.html]
     * @param extendedData 扩展信息
     */
    virtual void onPlayerStateUpdate(const std::string& /*streamID*/, ZegoPlayerState /*state*/, int /*errorCode*/, const std::string& /*extendedData*/);
    ```

- 调用示例

    ```cpp
    class MyEventHandler :public IZegoEventHandler
    {
    public:
        void onPlayerStateUpdate(const std::string& /*streamID*/, ZegoPlayerState /*state*/, int /*errorCode*/, const std::string& /*extendedData*/) {
        // 调用拉流接口成功后，当拉流器状态发生变更，如出现网络中断导致推流异常等情况，SDK在重试拉流的同时，会通过该回调通知
        // 未拉流状态，在拉流前处于该状态。如果拉流过程出现稳态的异常，例如 AppID 和 AppSign 不正确，都会进入未拉流状态
        //ZEGO_PLAYER_STATE_NO_PLAY = 0,

        // 正在请求拉流状态，拉流操作执行成功后会进入正在请求拉流状态，通常通过该状态进行应用界面的展示。如果因为网络质量不佳产生的中断，SDK 会进行内部重试，也会回到正在请求拉流状态
        //ZEGO_PLAYER_STATE_PLAY_REQUESTING = 1,

        // 正在拉流状态，进入该状态表明拉流已经成功，用户可以正常通信
        // ZEGO_PLAYER_STATE_PLAYING = 2
        // 通过 URL 拉流时，回调参数中的 stream_id 即为调用拉流API时的流 ID，用于唯一标识当次拉流事件。
        }
    };
    auto handler=std::make_shared<MyEventHandler>();
    engine->setEventHandler(handler);
    ```
</Accordion>
