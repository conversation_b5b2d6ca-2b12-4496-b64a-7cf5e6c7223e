{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 6654}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 16701}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "doc", "label": "实时语音价格说明", "id": "introduction/pricing/rtc", "articleID": 11475}, {"type": "doc", "label": "服务端混流价格说明", "id": "introduction/pricing/server-side-stream-mixing", "articleID": 13303}, {"type": "doc", "label": "CDN 直播价格说明", "id": "introduction/pricing/cdn-live-streaming", "articleID": 14123}]}, {"type": "doc", "label": "限制说明", "id": "best-practice/restrictions", "articleID": 13212}, {"type": "doc", "label": "实时音视频 SDK 与实时语音 SDK 差异", "id": "introduction/difference-between-audio-and-video-sdk", "articleID": 16843}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 6655}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 12589}, {"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 6665}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 6656}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 6658}, {"type": "doc", "label": "实现音频通话", "id": "quick-start/implementing-voice-call", "articleID": 8611}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/using-token-authentication", "articleID": 14381, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 15904, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音频频谱与音量变化", "id": "audio/sound-level-spectrum", "articleID": 21017, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 13218, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 18855, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "其他能力", "collapsed": false, "items": [{"type": "doc", "label": "媒体播放器", "id": "other/media-player", "articleID": 16228, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音视频录制", "id": "other/record-local-media", "articleID": 18831, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19743", "articleID": 19743}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=ExpressVideo&platform=react-native"}]}