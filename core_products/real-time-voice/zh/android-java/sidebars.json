{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 4438}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 16688}, {"type": "doc", "label": "接口变更说明", "id": "client-sdk/api-changes-notes", "articleID": 12581}, {"type": "doc", "label": "测试环境废弃说明", "id": "introduction/abandon-test-environment", "visible": false}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "doc", "label": "实时语音价格说明", "id": "introduction/pricing/rtc", "articleID": 11465}, {"type": "doc", "label": "服务端混流价格说明", "id": "introduction/pricing/server-side-stream-mixing", "articleID": 13291}, {"type": "doc", "label": "CDN 直播价格说明", "id": "introduction/pricing/cdn-live-streaming", "articleID": 14111}]}, {"type": "doc", "label": "限制说明", "id": "best-practice/restrictions", "articleID": 7591}, {"type": "doc", "label": "实时音视频 SDK 与实时语音 SDK 差异", "id": "introduction/difference-between-audio-and-video-sdk", "articleID": 15797}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 3564}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 12581}, {"type": "category", "label": "升级指南", "collapsed": false, "items": [{"type": "doc", "label": "2.20.0 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v2-20", "articleID": 17560}, {"type": "doc", "label": "3.0.0 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3", "articleID": 16554}]}, {"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 5690}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 3583}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 3575}, {"type": "doc", "label": "实现音频通话", "id": "quick-start/implementing-voice-call", "articleID": 7636}, {"type": "doc", "label": "场景化音视频配置", "id": "quick-start/scenario-based-audio-video-configuration", "articleID": 16505}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/using-token-authentication", "articleID": 14046, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 4990, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "网络测速", "id": "communication/testing-network", "articleID": 6669, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "多源采集", "id": "communication/multi-source-capture", "articleID": 16905, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "同时推多路流", "id": "communication/push-multiple-streams", "articleID": 5031, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "媒体补充增强信息（SEI）", "id": "communication/sei", "articleID": 6071, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "流量控制", "id": "communication/traffic-control", "articleID": 16390, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "云代理", "id": "communication/cloud-proxy", "articleID": 16894, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "地理围栏", "id": "communication/geofencing", "articleID": 17769, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "游戏语音", "id": "communication/range-audio", "articleID": 12286, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "万人范围音视频", "id": "communication/mass-scale-range-audio-video", "articleID": 16496, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "多人状态实时同步", "id": "communication/sync-users-status", "articleID": 16500, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "房间连接状态说明", "id": "room/room-connection-status", "articleID": 18661, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 4988, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "登录多房间", "id": "room/multi-room-login", "articleID": 5004, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音频频谱与音量变化", "id": "audio/sound-level-spectrum", "articleID": 5076, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 18063, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音频 3A 处理", "id": "audio/audio-3a-processing", "articleID": 15252, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "变声/混响/立体声", "id": "audio/custom-audio-processing", "articleID": 5094, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "混音", "id": "audio/audio-mixing", "articleID": 5087, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "场景化 AI 降噪", "id": "audio/scenario-based-ai-noise-reduction", "articleID": 15219, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频采集与渲染", "id": "audio/custom-audio-capture-and-rendering", "articleID": 5084, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频处理", "id": "audio/custom-audio-processing", "articleID": 5090, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "原始音频数据获取", "id": "audio/get-audio-raw-data", "articleID": 5079, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "AI 变声", "id": "audio/ai-voice-changer", "articleID": 18437, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 4985, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "使用 CDN 直播", "id": "live-streaming/using-cdn-for-live-streaming", "articleID": 5037, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "CDN 推流鉴权", "id": "live-streaming/cdn-stream-publishing-authentication", "articleID": 15836, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通过 URL 拉流", "id": "live-streaming/playing-stream-by-url", "articleID": 5034, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "超低延迟直播", "id": "live-streaming/low-latency-live-streaming", "articleID": 6786, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "直推 CDN", "id": "live-streaming/direct-to-cdn", "articleID": 5040, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "其他能力", "collapsed": false, "items": [{"type": "doc", "label": "媒体播放器", "id": "other/media-player", "articleID": 4997, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音效文件播放器", "id": "other/audio-effect-player", "articleID": 5721, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音视频录制", "id": "other/record-local-media", "articleID": 5000, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19732", "articleID": 19732}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19505}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=ExpressAudio&platform=android"}]}