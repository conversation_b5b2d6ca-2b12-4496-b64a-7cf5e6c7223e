{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 13199}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 16698}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "doc", "label": "实时语音价格说明", "id": "introduction/pricing/rtc", "articleID": 13200}, {"type": "doc", "label": "服务端混流价格说明", "id": "introduction/pricing/server-side-stream-mixing", "articleID": 13300}, {"type": "doc", "label": "CDN 直播价格说明", "id": "introduction/pricing/cdn-live-streaming", "articleID": 14120}]}, {"type": "doc", "label": "实时音视频 SDK 与实时语音 SDK 差异", "id": "introduction/difference-between-audio-and-video-sdk", "articleID": 15808}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 13201}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 13202}, {"type": "category", "label": "升级指南", "collapsed": false, "items": [{"type": "doc", "label": "3.0.0 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3", "articleID": 16791}]}, {"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 13211}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 13203}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 13204}, {"type": "doc", "label": "实现音频通话", "id": "quick-start/implementing-voice-call", "articleID": 13205}, {"type": "doc", "label": "场景化音视频配置", "id": "quick-start/scenario-based-audio-video-configuration", "articleID": 16632}, {"type": "doc", "label": "设置回调", "id": "quick-start/setting-callback", "articleID": 21399}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/using-token-authentication", "articleID": 14488, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "多源采集", "id": "communication/multi-source-capture", "articleID": 17816, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "流量控制", "id": "communication/traffic-control", "articleID": 16634, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "地理围栏", "id": "communication/geofencing", "articleID": 17915, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 6666, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19741", "articleID": 19741}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=ExpressVideo&platform=electron"}]}