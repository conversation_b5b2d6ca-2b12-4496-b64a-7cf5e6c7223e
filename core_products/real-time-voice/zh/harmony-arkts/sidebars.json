{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 19515}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 19516}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "doc", "label": "实时语音价格说明", "id": "introduction/pricing/rtc", "articleID": 19518}, {"type": "doc", "label": "服务端混流价格说明", "id": "introduction/pricing/server-side-stream-mixing", "articleID": 19519}]}, {"type": "doc", "label": "限制说明", "id": "best-practice/restrictions", "articleID": 19520}, {"type": "doc", "label": "已知问题及解决方案", "id": "introduction/known-issues-and-solutions", "articleID": 20445}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 19521}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 19517}, {"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 19535}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 19522}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 19523}, {"type": "doc", "label": "实现音频通话", "id": "quick-start/implementing-voice-call", "articleID": 19524}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 20972, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "媒体补充增强信息（SEI）", "id": "communication/sei", "articleID": 19525, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "流量控制", "id": "communication/traffic-control", "articleID": 19526, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "房间连接状态说明", "id": "room/room-connection-status", "articleID": 19527, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 19528, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音频频谱与音量变化", "id": "audio/sound-level-spectrum", "articleID": 19529, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 19530, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音频 3A 处理", "id": "audio/audio-3a-processing", "articleID": 19531, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "变声/混响/立体声", "id": "audio/custom-audio-processing", "articleID": 20976, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "自定义音频采集", "id": "audio/custom-audio-capture-and-rendering", "articleID": 20975, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频处理", "id": "audio/custom-audio-processing", "articleID": 20974, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 19532, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "其他能力", "collapsed": false, "items": [{"type": "doc", "label": "媒体播放器", "id": "other/media-player", "articleID": 19533, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音效文件播放器", "id": "other/audio-effect-player", "articleID": 20973, "tag": {"label": "基础", "color": "Check"}}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/20528", "articleID": 20528}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=ExpressVideo&platform=harmony"}]}