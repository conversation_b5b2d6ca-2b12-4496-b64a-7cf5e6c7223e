---
articleID: 18237
---


# 微信小程序发布日志

- - -



## 3.8.0 版本

**发布日期：2025-03-20**

<h5>问题修复</h5>

1. 修复 [roomStateChanged](roomStateChanged) 回调缺陷，即所有需触发 `reason` 为 `Logining` 的场景（如登录房间、切换房间），错误传递 `Reconnecting`。
2. 修复已知问题。

---

## 3.6.0 版本

**发布日期：2024-10-22**


<h5>改进优化</h5>

**1. 变更日志上报的地址**

<Warning title="注意">


开发者无法通过 3.6.0 以前的版本，平滑升级到 3.6.0 或以上的版本。

</Warning>



开发者如需升级到 3.6.0 或以上的版本 SDK，需在微信小程序的开发设置里，将部分域名添加至服务器域名，用于上报事件和日志，详情请参考 [3.6.0 及以上版本升级指南](!upgrade_guide6)。

---

## 3.5.0 版本

**发布日期：2024-07-05**

<h5>新增功能</h5>

**1. 在开启审核服务时，可以单独控制每条流是否审核**

当开启审核服务时（可参考 [开始音频流审核](/real-time-voice-video-server/api-reference/moderation/start-censor-audio) 和 [开始视频流审](/real-time-voice-video-server/api-reference/moderation/start-censor-video) ），默认会对房间内的所有流进行审核。如需单独控制某条流是否送审，可以在客户端调用 [startPublishingStream](@startPublishingStream) 接口开始推流时，通过送审标识  `inspectFlag` 参数（0：允许，默认；1：不允许），单独控制是否对该流进行审核。

相关 API 请参考 [startPublishingStream](@startPublishingStream)

**2. 新增自动混流功能**

注意：
- 目前仅支持房间内全部为音频流时，开启自动混流。
- 在同一个房间内开启下一个自动混流任务前，请先调用 [stopAutoMixerTask](@stopAutoMixerTask)  接口结束上一次自动混流任务，以免造成当一个主播已经开启下一个自动混流任务与其他主播混流时，观众依然在拉上一个自动混流任务的输出流的情况。
- 若用户未主动结束当前自动混流任务，该任务将在房间不存在后，或者输入流持续 90 秒不存在后，自动结束。

SDK 可以指定房间开启自动混流任务，由 ZEGO 实时音视频服务器自动将房间内的所有音频流进行混流（目前只支持混音频流），常用于语聊房、合唱场景下。该功能可以将麦上主播的流混合之后给麦下观众，从而提升观众的观看体验，并且节省流量，详情请参考 [混流](!Other_Functions/Mixer)。

相关 API 请参考 [startAutoMixerTask](@startAutoMixerTask)、[stopAutoMixerTask](@stopAutoMixerTask)

**3. 混流功能新增功能配置及状态回调**

注意：新版本 SDK 输入流如果为纯音频，必须设置 layout 参数，SDK 不再自动设置，否则会出现报错提示。

混流功能增加输出视频配置、焦点语音等能力，同时增加混流的状态回调，用于获取混流转发的状态。

相关 API 请参考 [startMixerTask](@startMixerTask)

**4. 新增 [destroyEngine](@destroyEngine) 接口**

注意：调用 [destroyEngine](@destroyEngine) 接口后，不能再调用同一个引擎实例的其他接口，请开发者及时将销毁引擎变量赋值为 `null` 或 `undefined`，以去掉对已销毁引擎的引用，即置空实例，避免发生错误。

新增销毁引擎实例接口 [destroyEngine](@destroyEngine)，在 SDK 生命周期终点调用该接口，用于回收引擎内部剩余资源，关闭多余的网络连接。


<h5>改进优化</h5>

**1. 优化弱网抗性**

优化 SDK 在网络抖动和低网速场景下的可用性。


---


## 3.1.0 版本

**发布日期：2024-03-28**

<h5>改进优化</h5>

**1. 优化单房间模式下的房间切换逻辑**

用户成功登录某个房间后，如果没有退出该房间、重复登录该房间或直接登录其他房间，将会操作无效或登录失败，并返回 1002001 错误码。

相关 API 请参考 [loginRoom](@loginRoom)

**2. 优化多端登录逻辑**

用户在 A 设备上登录成功后，A 设备断网；然后使用同一 userID 在 B 设备上登录成功。此时如果 A 设备网络恢复，重连会失败，并抛出 1002050 错误码，提示该 userID 已在其他设备登录。

---

## 3.0.0 版本

**发布日期：2023-09-20**

<Warning title="注意">


为了保证您的音视频通话体验，ZEGO 推荐您使用 3.0.0 或以上版本的 SDK。

- 自 `2023-09-20` 起至 `2024-09-20` 止，ZEGO 不再接受对 3.0.0 以前版本的微信小程序 SDK 的功能新增申请，仅支持问题修复、提供安全补丁。
- 自 `2024-09-20` 后，ZEGO 将停止对 3.0.0 以前版本的微信小程序 SDK 的维护，不再接受功能新增、问题修复、安全补丁等各种申请。

</Warning>



<h5>改进优化</h5>

**1. 优化 SDK 内部逻辑，降低弱网环境下音视频的卡顿率和网络延迟，提升音视频的流畅度**

开发者 `不能` 通过 3.0.0 以前的版本 `平滑升级` 到 3.0.0 或以上的版本，详情请参考 [3.0.0 及以上版本升级指南](!upgrade_guide4)。

**2. 优化拉流方式**

通过优化 [startPlayingStream](@startPlayingStream)、[getPlayerInstance](@getPlayerInstance) 接口，实现跨房间拉流调度。

相关 API 请参考 [startPlayingStream](@startPlayingStream), [getPlayerInstance](@getPlayerInstance)

---


## 2.12.2 版本

**发布日期：2023-02-21**

<h5>改进优化</h5>

1. 优化拉混流功能

    - 2.12.1 及以前版本，支持通过 [startPlayingStream](@startPlayingStream) 接口设置 [playOption.isMix](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_wxxcx~interface~ZegoWxPlayOption#stream-params) 参数为 true，此时只能从 CDN 拉混流。
    - 2.12.2 及以上版本，支持通过 [startPlayingStream](@startPlayingStream) 接口设置 [playOption.sourceType](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_wxxcx~interface~ZegoWxPlayOption#stream-params) 参数，此时可设置拉混流来源为 CDN 或 BGP。

<h5>问题修复</h5>

 1. 修复已知问题

---

## 2.12.1 版本

**发布日期：2022-12-15**

<h5>问题修复</h5>

1. 修复房间流新增时，偶现的没有触发 [roomStreamUpdate](@roomStreamUpdate) 的问题。

---
## 2.12.0 版本

**发布日期：2022-11-09**

<h5>改进优化</h5>

1. 优化了日志上报信息。

<h5>问题修复</h5>

1. 修复了多用户在短时间内连续更新流附加消息可能导致的异常流删除回调。

---
## 2.11.1 版本

**发布日期：2022-09-09**

<h5>问题修复</h5>

1. 修复 [mutePublishStreamVideo](@mutePublishStreamVideo)，[mutePublishStreamVideo](@mutePublishStreamVideo) 接口参数取反的问题
2. 修复断连过程中的流更新后，在房间重连时没有触发 [roomStreamUpdate](@roomStreamUpdate) 的流更新信息的问题
3. 修复小程序切后台进入挂起状态后，再回到前台可能出现退出房间的问题

---

## 2.11.0 版本

**发布日期：2022-07-21**

<h5>改进优化</h5>

1. 优化了日志上报功能，增加设备相关信息

---

## 2.10.4 版本

**发布日期：2022-05-30**

<h5>问题修复</h5>

1. 修复了拉流失败后，无法通过 getPlayerInstance("streamID").stop() 正常停止拉流的问题

---

## 2.10.3 版本

**发布日期：2022-05-11**

<h5>问题修复</h5>

1. 修复了因微信升级后 SDK 推流还未成功就发布流新增，导致拉流失败的问题
2. 修复了拉流失败时返回的错误码异常的问题

---

## 2.10.2 版本

**发布日期：2022-04-22**

<h5>问题修复</h5>

1. 修复了网络异常时调用 [sendBroadcastMessage](@sendBroadcastMessage)、[sendBarrageMessage](@sendBarrageMessage) 或 [sendCustomCommand](@sendCustomCommand) 接口时未返回错误码的问题

---

## 2.10.1 版本

**发布日期：2022-04-19**

<h5>问题修复</h5>

1. 修复了 npm 依赖问题

---


## 2.10.0 版本

**发布日期：2022-03-11**

<h5>改进优化</h5>

1. 优化小程序的推拉流方式

在保留原有推拉流方式基础上，新增了 [createPusher]、[getPusherInstance]、[getPlayerInstance] 等接口用于新的推拉流方式，减少开发者对小程序组件的理解成本，接入使用更高效。

相关 API 请参考 [createPusher](@createPusher)、[getPusherInstance](@getPusherInstance)、[getPlayerInstance](@getPlayerInstance)

2. [loginRoom] 和 [renewToken] 接口支持使用 “token04” 版本的 Token

相关 API 请参考 [loginRoom](@loginRoom)、[renewToken](@renewToken)

---

## 2.9.1 版本

**发布日期：2022-02-16**

<h5>问题修复</h5>

1. 修复了调用 [stopPlayingStream](@stopPlayingStream) 接口停止拉流时，SDK 循环调用内部接口导致内存溢出的问题

---
## 2.9.0 版本

**发布日期：2022-01-12**

<h5>问题修复</h5>

1. 修复了微信小程序基础库更新导致推流状态码未发出，进而导致 SDK 未正确上报流新增的问题。
2. 修复了调用 [startPlayingStream](@startPlayingStream) 接口调度失败后未抛出结果的问题。

---



## 2.8.0 版本

**发布日期：2021-07-09**


<h5>新增功能</h5>

**1. 新增多房间功能**

<Warning title="注意">


需要联系 ZEGO 技术支持开通该功能。

</Warning>



同一个用户可以同时加入多个房间，并同时在多个房间内（目前默认最多同时加入 5 个房间）推流、拉流、发送实时消息和接收消息回调。本功能可以隔离多个房间的消息及回调，实现更灵活的连麦业务。ZEGO 推荐用于跨房间连麦和在线教育的超级小班场景。

需要在初始化 SDK 之后，登录房间前调用 [enableMultiRoom](@enableMultiRoom) 接口开启多房间，再调用 [loginRoom](@loginRoom) 接口登录多房间，详情请参考 [常用功能 - 多房间](/real-time-video-web/room/multi-room-login)。

相关 API 请参考 [enableMultiRoom](@enableMultiRoom)、[loginRoom](@loginRoom)

<h5>问题修复</h5>

1. 修改已知问题


---



## 2.7.0 版本

**发布日期：2021-06-10**


<h5>问题修复</h5>

1. 修复了 [startPlayingStream](@startPlayingStream) 中 “playOption.sourceType” 参数的默认值在接口调用时被修改的问题
2. 修改了其他已知问题


---




## 2.6.0 版本

**发布日期：2021-04-29**


<h5>新增功能</h5>

**1. 新增 token 过期回调 [tokenWillExpire](@tokenWillExpire) 和 token 更新接口 [renewToken](@renewToken)**

<Warning title="注意">


SDK 当前版本已支持该回调和接口，预计 2021-05-13 才可正式使用。

</Warning>



当 token 快要过期前的 30s  SDK 会主动触发 [tokenWillExpire](@tokenWillExpire) 回调，开发者需要在收到该回调时，重新生成 token 并调用 [renewToken](@renewToken) 接口更新。

[renewToken](@renewToken) 接口常用于如下场景：

a. 当 token 过期时，通过该接口来更新 token。

b. 当开发者需要从登录权限切换到推流（含登录）权限，或者从推流（含登录）权限切换到只有登录权限时，可以通过该接口来实现。

相关 API 请参考 [tokenWillExpire](@tokenWillExpire)、[renewToken](@renewToken)

**2. token 版本升级，支持 token 分功能鉴权**

<Warning title="注意">


SDK 当前版本已支持该鉴权功能，但预计 2021-05-13 才可正式使用。

</Warning>



通过新版本 token 控制是否可登录，是否能推流，若想使用新版本 token 鉴权功能，请先联系 ZEGO 技术支持开启对应配置。

相关 API 请参考 [tokenWillExpire](@tokenWillExpire)、[renewToken](@renewToken)


<h5>问题修复</h5>

1. 修复了 SDK 无法拉取自己所推流的问题

如果 SDK 在推流后调用 [startPublishingStream](@startPublishingStream) 接口拉取当前所推流，则会出现 “player already exist” 报错，原因为 SDK 限制重复拉流的机制导致无法拉取自己推的流，该问题已修复。

相关 API 请参考 [startPublishingStream](@startPublishingStream)、[startPlayingStream](@startPlayingStream)


---



## 2.5.0 版本

**发布日期：2021-04-15**


<h5>问题修复</h5>

1. 修复已知问题


---



## 2.4.0 版本

**发布日期：2021-04-01**


<h5>改进优化</h5>

1. 修改了设置日志时上传地址的优先级，使其优先级高于后台下发的地址

相关 API 请参考 [setLogConfig ](@setLogConfig)

2. 优化了视频码率设置策略，解决了屏幕共享码率初始值较低的问题

<h5>问题修复</h5>

1. 修复了流更新列表 “roomID” 为 “undefined” 的问题
2. 修复了初始化后立即登录房间时报 “network is broken, stop retry” 的问题


---



## 2.3.0 版本

**发布日期：2021-03-18**


<h5>问题修复</h5>

1. 修复已知问题


---

---
## 2.2.1 版本

**发布日期：2021-03-04**


<h5>改进优化</h5>

**1. 用户被踢出房间时，新增详细的踢出原因**

**2. 修复了重复登录房间时无法感知房间重置，导致收不到流更新的问题**

房间成员被踢下线时，SDK 需要根据附加信息提示具体被踢下线的原因。


---



## 2.0.0 版本

**发布日期：2021-01-28**


<h5>改进优化</h5>

**1. 代码重构，减少不同模块之间耦合性**

**2. 日志系统重构，优化了日志结构，提高问题定位效率**

**3. 重试逻辑重构，进一步提高 SDK 抗弱网能力**


---




## 1.7.2 版本

**发布日期：2020-12-28**


<h5>改进优化</h5>

**1. 优化了回调的报错机制**


---



## 1.7.1 版本

**发布日期：2020-11-16**


<h5>改进优化</h5>

**1. 优化了断网重试逻辑**

**2. 优化了在停止服务端混流回调中退出房间的报错机制**

相关 API 请参考 [stopMixerTask ](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#stop-mixer-task)



---



## 1.7.0 版本

**发布日期：2020-10-22**


<h5>新增功能</h5>

**1. 新增房间内当前在线用户数量回调**

新增 [roomOnlineUserCountUpdate ](@roomOnlineUserCountUpdate) 回调，用于通知用户当前房间在线人数。

相关 API 请参考  [roomOnlineUserCountUpdate ](@roomOnlineUserCountUpdate)

<h5>改进优化</h5>

**1. 开始推拉流和停止推拉流时触发 `推流状态更新回调` 和 `拉流状态更新回调`**

相关 API 请参考 [publisherStateUpdate ](@publisherStateUpdate) 和 [playerStateUpdate ](@playerStateUpdate)

**2. 优化了频繁切换推拉流时的报错机制**

**3. 优化了日志上报**

<h5>问题修复</h5>

1. 新增房间服务与混流服务器通信失败时的错误提示
2. server 支持备用域名，提高连通率
3. 修复可靠消息 bug
4. 修复媒体服务心跳间隔

---



## 1.6.0 版本

**发布日期：2020-06-15**


<h5>新增功能</h5>

**1. 推拉流重试，增加获取新的节点地址接口getNextUrl**

<h5>问题修复</h5>

1. publiserState及playerState状态回调优化
2. 流更新接口优化，解决房间流列表为空仍触发回调问题
3. 修复其它已知问题

---


## 1.5.1 版本

**发布日期：2020-06-08**


<h5>问题修复</h5>

1. startMixerTask 参数修正

---


## 1.5.0 版本

**发布日期：2020-05-15**


<h5>改进优化</h5>

**1. 优化部分错误码信息**

**2. 优化部分关键日志**

<h5>问题修复</h5>

1. 修复已知问题

---


## 1.4.0 版本

**发布日期：2020-04-30**

<h5>改进优化</h5>

**1. 优化房间用户列表拉取策略**

<h5>问题修复</h5>

1. 修复已知问题


---




## 1.3.0 版本

**发布日期：2020-04-15**


1. 客户端控制房间最大人数
2. 优化小程序拉流逻辑
3. 优化节点重试逻辑
4. 修复已知问题


---



## 1.2.1 版本

**发布日期：2020-03-30**

1. 增加房间弹幕消息功能、自定义信令
2. 优化混流接口

---



## 1.1.0 版本

**发布日期：2019-11-28**

1. 增加混流接口
2. 增加IM接口
3. 对齐各端接口，接口命名、参数命名、取值等统一

---



## 1.0.0 版本

**发布日期：2019-10-31**


Zego Webrtc Express SDK 初版发布。包含系统、房间、推流、拉流功能
