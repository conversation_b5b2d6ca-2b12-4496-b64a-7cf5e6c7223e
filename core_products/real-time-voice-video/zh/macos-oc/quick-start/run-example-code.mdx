---
articleID: 3127
---
# 跑通示例源码

---

## macOS 示例代码

如需跑通 macOS Objective-C 示例源码，可参考以下两种方式：
- **方式 1**：（推荐）参考 [macOS Swift ](/real-time-video-macos-swift/quick-start/run-example-code) 示例代码。
- **方式 2**：参考 [iOS Objective-C ](/real-time-video-ios-oc/quick-start/run-example-code) 示例代码。 (Express macOS SDK 与 Express iOS SDK 的接口基本一致)

<Note title="说明">


若需要使用 C++ 接口，请参考 macOS C++ 的 [跑通示例源码文档 ](/real-time-video-macos-cpp/quick-start/run-example-code)。
</Note>

## Mac Catalyst 示例代码

如需获取 Mac Catalyst 示例代码，请下载 [iOS Objective-C ](/real-time-video-ios-oc/quick-start/run-example-code) 示例代码，其中的 Xcode 工程包含了 Mac Catalyst 的 Target。
