---
articleID: 19662
---

import Content from '/snippets/Reuse/SignatureVerificationZH.mdx'


# 回调配置说明
---

在使用 ZEGO 服务端 API 时，开发者可通过回调服务对接业务后台，进一步保证业务的有序和正常。

<Warning title="注意">


**回调服务不能保证完全可靠，请慎重考虑使用回调方案构建核心业务流程的风险。**

</Warning>



## 使用场景

例如：

- 客户端推流成功后，业务后台可以接收 ZEGO 服务端的 [流创建回调](!Callback/Stream_Create)，用于增加直播列表 (可维护直播列表)。

<Note title="说明">


1. 流创建回调中的 “pic_url” 可用于鉴黄，默认 20s 一张的缓存图片。
2. “pic_url” 只能在推流过程中使用，推流结束后无效。

</Note>



- 客户端停止推流后，业务后台可以接收 ZEGO 服务端的 [流关闭回调](!Callback/Stream_Close)，用于删除直播列表 (可维护直播列表)。

- 客户端结束直播后，业务后台可以接收 ZEGO 服务端的 [录制文件生成回调](!Callback/Record_File_Create_Callback)，用于实现点播服务。


## 回调配置

开发者可根据实际业务需要，在 [ZEGO 控制台 ](https://console.zego.im) 的 “项目配置 > 服务端回调配置” 中进行回调信息的配置。


同时，可按需配置接收 ZEGO 回调的 URL 地址。

<Note title="说明">


可按如下方式查看控制台界面：

- **2021-11-16** 之后注册 [ZEGO 控制台](https://console.zego.im) 的用户，请参考 [控制台 - 服务端回调配置](/console/server-callback-configuration)。
- **2021-11-16** 及之前注册 [ZEGO 控制台](https://console.zego.im) 的用户，请参考 [控制台（旧版） - 项目管理](/console-old/project-management#4) 中的 “高级配置”。

</Note>




## 回调说明

- 请求方法：POST。

<Note title="说明">


  回调数据格式为 JSON，您需要对其进行 UrlDecode 解码。

</Note>



- 传输协议：HTTPS/HTTP，建议使用 HTTPS。

## 验证签名

<Content />

## 返回响应

返回 HTTP status code 为 2XX （例如 200）表示成功，其他响应都表示失败。

## 回调重试策略

如果 ZEGO 服务器没有收到响应，或收到的 HTTP status code 不为 2XX（例如 200），都会尝试重试，**最多进行 5 次重试**。每次重试请求与**上一次请求**的间隔时间分别为 2s、4s、8s、16s、32s。若第 5 次重试后仍然失败，将不再重试，该回调丢失。
