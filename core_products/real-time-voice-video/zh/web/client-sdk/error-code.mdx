---
articleID: 4381
---

# 常见错误码

- - -

## 简介


当 SDK 运行出现网络、媒体相关等错误时，SDK 无法自动恢复，需要 App 干预或进行用户提示。调用接口后返回结果存在错误信息、事件回调中有错误信息或 console 日志中出现错误日志，则表示该接口调用异常。

- 调用接口后返回错误信息示例代码：

```js
const { errorCode, extendedData } = await zg.setCaptureVolume(stream, result);
// errorCode 为错误码，extendedData 为错误描述信息
console.log(errorCode, extendedData);
```

- 调用接口后抛出错误信息示例代码：

```js
try {
    await zg.setCaptureVolume(stream, result);
} catch (error) {
    // errorCode 为错误码，extendedData 为错误描述信息
    const { errorCode, extendedData } = error;
    console.log(errorCode, extendedData);
}
```

- 事件回调中错误信息示例代码：

```js
zg.on('roomStateChanged', (roomID, reason, errorCode, extendedData) => {
    if (errorCode !== 0) {
      // errorCode 为错误码，extendedData 为错误描述信息
      console.log(errorCode, extendedData);
    }
})
```

## 1000xxx/1100xxx 通用错误码

通用类错误码为接口通用错误的错误码，通常在调用 [startPublishingStream](@startPublishingStream)、[startPlayingStream](@startPlayingStream) 接口会出现，请根据以下错误码并进行相应处理。

| 错误码 | 描述 |
|-|-|
| 0 | 执行成功。 |
| 1000002 | 描述：未登录房间。<br />可能原因：没有登录房间或者房间异常断开。<br />处理建议：在调用推流或拉流前，请先调用 [loginRoom](@loginRoom) 登录房间。 |
| 1000014 | 描述：输入 StreamID 超长。<br />可能原因：在调用 [startPublishingStream](@startPublishingStream) 或 [startPlayingStream](@startPublishingStream) 时传入的 StreamID 参数长度超过限制。<br />处理建议：StreamID 最大支持输入 256 个字符，检查调用函数时传入的 StreamID 参数是否过长。 |
| 1000015 | 描述：输入流 ID 为空。<br />可能原因：在调用 [startPublishingStream](@startPublishingStream) 或 [startPlayingStream](@startPlayingStream) 时传入的 StreamID 参数为 undefined、null 或空字符串。<br />处理建议：检查调用函数时传入的 streamID 参数是否正常。 |
| 1000016 | 描述：输入流 ID 包含非法字符。<br />可能原因：在调用 [startPublishingStream](@startPublishingStream) 或 [startPlayingStream](@startPlayingStream) 时传入的 StreamID 包含非法字符。<br />处理建议：检查调用函数时传入的 StreamID 参数是否正常，仅支持数字、英文字符和 "-"、"_"。|
| 1000017 | 描述：网络断开。<br />可能原因：网络异常或推拉流与媒体协商失败。<br />处理建议：检查网络是否正常。 |
| 1000018 | 描述：本地流错误。<br />可能原因：Web 端错误码，传入流对象错误或者流对象并不是 SDK 创建。<br />处理建议：Web 端：检查打印流对象是否正常 并确认是否是 [createStream](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#create-stream) 接口生成；一般发生在 [destroyStream](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#destroy-stream)、[setVideoConfig](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#set-video-config)、[useVideoDevice](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#use-video-device)、[useAudioDevice](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#use-audio-device) 接口。 |
| 1000019 | 描述：获取音浪失败。<br />可能原因：获取音浪失败。<br />处理建议：一般是浏览器兼容性问题，请尝试切换浏览器验证。 |
| 1100001 | 描述：参数错误。<br />可能原因：传参错误。<br />处理建议：检查打印传参，在 extendedData 中查看具体错误字段。 |
| 1100002 | 描述：请求响应超时。<br />可能原因：SDK请求服务端超时，可能是由于网络原因导致。<br />处理建议：请尝试切换网络解决。 |
| 1100003 | 描述：socket 关闭。<br />可能原因：可能是由于网络原因导致 SDK 与服务器建立的 socket连接被关闭。<br />处理建议：请尝试切换网络解决。 |
| 1100999 | 描述：服务端未知错误。<br />可能原因：一般是服务端错误。<br />处理建议：请联系 ZEGO 技术支持。 |

## 1001xxx/1101xxx 调试阶段错误码

调试阶段错误码指的是开发者在开发应用期间容易出现的错误，一般表现为接口参数出错等。

| 错误码 | 描述 |
|-|-|
| 1001004 | 描述：认证失败。<br />可能原因：AppID 不正确。<br />处理建议：检查传入的 AppID 是否与 ZEGO 控制台中的 AppID 一致。 |
| 1001021 | 描述：设置地理围栏失败。<br />可能原因：创建引擎实例之后设置地理围栏。<br />处理建议：创建引擎实例之前设置地理围栏。 |
| 1101000 | 描述：SDK 初始化失败。<br />可能原因：AppID 不正确导致。<br />处理建议：请检查 AppID 是否正确|
| 1101001 | 描述：微信小程序 getSetting 方法调用失败，无法检测摄像头和麦克风功能是否正常。<br />可能原因：小程序初始化失败、微信版本过低或者未获取推拉流组件权限。<br />处理建议：请尝试升级微信版本并检查是否有组件权限。 |
| 1101002 | 描述：获取流媒体服务配置失败。<br />可能原因：获取流媒体服务配置失败。<br />处理建议：请联系 ZEGO 技术支持。|

## 1002xxx/1102xxx 房间相关错误码

调用 [loginRoom ](@loginRoom) 接口时出错，通常为登录房间参数错误，以及 SDK 与 ZEGO 房间服务端通讯出现的异常。

| 错误码 | 描述 |
|-|-|
| 1002001 | 描述：登录房间数量超过上限。<br />可能原因：当前仅支持同时登录 1 个主房间和 1 个多房间。<br />处理建议：请检查当前用户是否登入了过多房间。 |
| 1002002 | 描述：未使用此房间 ID 登录过。<br />可能原因：调用 [logoutRoom] 或[switchRoom] 或 [renewToken] 或 [setRoomExtraInfo] 前，未使用此房间 ID 登录过。<br />处理建议：检查是否使用此房间 ID 登录过。 |
| 1002005 | 描述：输入用户 ID 为空。<br />可能原因：输入的用户 ID 为空字符串或没有输入用户 ID。<br />处理建议：检查用户 ID 是否为空。 |
| 1002006 | 描述：输入用户 ID 包含非法字符。<br />可能原因：输入用户 ID 包含非法字符。<br />处理建议：请检查用户ID是否正确。用户 ID 最大为64 字节的字符串，仅支持数字，英文字符 和 '~', '!', '@', '#', '$', '', '^', '&', '*', '(', ')', '_', '+', '=', '-', ';', '’', ',', '.' 。|
| 1002007 | 描述：输入用户 ID 超长。<br />可能原因：输入用户 ID 超长。<br />处理建议：请检查用户ID是否过长，最大长度应小于 64 字节。|
| 1002008 | 描述：输入用户名为空。<br />可能原因：输入用户名为空。<br />处理建议：请检查用户名是否为空。 |
| 1002010 | 描述：输入用户名超长。<br />可能原因：输入用户名超长。<br />处理建议：请检查输入的用户名是否超长，最大长度应小于 256 字节。 |
| 1002011 | 描述：输入房间 ID 为空。<br />可能原因：输入房间 ID 为空。<br />处理建议：请检查房间ID是否为空。 |
| 1002012 | 描述：输入房间 ID 包含非法字符。<br />可能原因：输入房间 ID 包含非法字符。<br />处理建议：仅支持数字，英文字符 和 '~', '!', '@', '#', '$', '', '^', '&', '*', '(', ')', '_', '+', '=', '-', ', ';', '’', ',', '.', '\<', '>', '/' 。|
| 1002013 | 描述：输入房间 ID 超长。<br />可能原因：输入房间 ID 超长。<br />处理建议：请检查输入房间 ID 是否超长，最大长度应小于 128 字节。 |
| 1002014 | 描述：房间未登录或房间已断开。<br />可能原因：当前房间不存在。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1002015 | 房间附加信息 key 超长。<br />[setRoomExtraInfo] 函数输入的房间附加信息 key 长度大于或等于 128 字节。<br />请检查 [setRoomExtraInfo] 函数调用时输入的房间附加信息 key，确保其长度小于 128 字节。 |
| 1002016 | 房间附加信息 value 超长。<br />[setRoomExtraInfo] 函数输入的房间附加信息 value 长度大于或等于 128 字节。<br />请检查 [setRoomExtraInfo] 函数调用时输入的房间附加信息 value，确保其长度小于 128 字节。 |
| 1002017 | 描述：房间附加消息设置的 key 超过支持的最大数量限制。 <br />可能原因：多次调用 [setRoomExtraInfo] 接口传入了不同的 key。 <br />处理建议：目前只支持一个 key。 |
| 1002018 | 描述：多房间模式下，登录房间的用户 ID 或用户名称不相同。 <br />可能原因：多房间模式下，登录多房间传入了不同的用户 ID 或用户名。 <br />处理建议：多房间模式下，传入的用户 ID 或用户名必须相同。|
| 1002031 | 描述：登录房间超时。<br />可能原因：可能是由于网络原因导致。<br />处理建议：请尝试切换网络重试。 |
| 1002034 | 描述：登录房间的用户超过最大数量。<br />可能原因：登录房间的用户数超过该房间配置的最大用户数量限制（测试环境下默认房间最大用户数为 50，正式环境无限制）。<br />处理建议：请联系 ZEGO 技术支持，检查房间人数是否超过限制。|
| 1002036 | 描述：登录失败，多房间功能没有开通。<br />可能原因：多房间功能未开通。 <br />处理建议：请联系 ZEGO 技术支持。 |
| 1002037 | 描述：同时登录的房间总数量超过限制。<br />可能原因：多房间功能可同时登录总房间数量受限。 <br />处理建议：请联系 ZEGO 技术支持。|
| 1002050 | 描述：用户被踢出房间。<br />可能原因：可能是相同用户 ID 在其他设备登录。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1002053 | 描述：重试登录房间超过最大的重试时间。<br />可能原因：可能是由于网络原因导致。<br />处理建议：请尝试切换网络重试。|
| 1002055 | 描述：业务后台发出了踢出房间信令。<br />可能原因：客户调用服务端踢人接口，触发服务端下发 kickout 信令。<br />处理建议：请查看被踢原因，联系 ZEGO 技术支持。 |
| 1002056 | 描述：用户重复进行登录。<br />可能原因：未登出主房间再次调起登录主房间。<br />处理建议：请排查登录逻辑，建议在登录与登出逻辑处断点或者打印进行调试。 |
| 1002064 | 描述：房间 ID 已被登录房间接口使用，未退出房间之前，当前用户无法再登录该房间。<br />可能原因：房间 ID 已被登录房间接口使用。<br />处理建议：退出相同房间 ID 的房间。 |
| 1002099 | 描述：系统内部异常导致房间登录失败。<br />可能原因：服务端返回未约定错误码，错误信息在 extendedData。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1102001 | 描述：心跳超时。<br />可能原因：可能是由于网络原因导致，默认 90 秒内未收到房间心跳回包会触发心跳超时。<br />处理建议：请尝试切换网络重试。 |
| 1102011 | 描述：数据格式错误。<br />可能原因：数据格式错误。<br />处理建议：请联系 ZEGO 技术支持。|
| 1102012 | 描述：正在执行登录流程。<br />可能原因：房间正在登录进行中。<br />处理建议：联系 ZEGO 技术支持。 |
| 1102013 | 描述：房间信令请求错误。<br />可能原因： 可能是由于网络原因导致。<br />处理建议：尝试切换网络重试或者联系 ZEGO 技术支持。|
| 1102014 | 描述：ZPush 失败。<br />可能原因：ZPush 失败。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1102015 | 描述：用户登录状态错误。<br />可能原因：登录状态错误。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1102016 | 描述：登录 token 格式有误<br />可能原因：登录 token 格式有误。<br />处理建议：token 获取方式：Web 端请参考 [用户权限控制](/real-time-video-web/communication/using-token-authentication)，小程序端请参考 [用户权限控制](/real-time-video-miniprogram/communication/using-token-authentication)。|
| 1102017 | 描述：调度失败。<br />可能原因：媒体节点调度失败。<br />处理建议：请拿着推拉流状态回调吐出的错误码，联系 ZEGO 技术支持。 |
| 1102018 | 描述：登录 token 过期。<br />可能原因：登录 token 过期，推拉流过程也可能出现该错误。<br /> 处理建议：及时调用 [renewToken](@renewToken) 接口来更新 token。 |
| 1102019 | 描述：subcmd 错误。<br />可能原因：subcmd 错误。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1102020 | 描述：登录鉴权失败。<br />可能原因：登录鉴权失败。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1102021 | 描述：biz_channel 错误。<br />可能原因：biz_channel 错误。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1102022 | 描述：调用请求超时。<br />可能原因：可能是由于网络原因导致。<br />处理建议：请尝试切换网络后重试。 |
| 1102023 | 描述：连接媒体服务器失败。<br />可能原因：可能是由于网络原因导致。<br />处理建议：请尝试切换网络后重试。 |
| 1102025 | 描述：本地日志上报失败。<br />可能原因：可能是由于网络原因导致。<br />处理建议：请尝试切换网络后重试。 |

## 1003xxx/1103xxx 推流相关的错误码

调用推流接口后，或者在推流过程中出现异常，开发者可从 [publisherStateUpdate](@publisherStateUpdate) 回调获取相关错误码，请根据以下错误码进行相应处理。

处理推流回调错误码非 0 的情况：

1. 当 “state” 为 “NO_PUBLISH” 时，且 “errorCode” 非 0，表示推流失败，同时不会再进行重试推流了，此时可在界面作出推流失败提示。
2. 当 “state” 为 “PUBLISH_REQUESTING” 时，且 “errorCode” 非 0，表示在重试推流，此时如果超出重试时间未成功推流会抛出推流失败通知。

| 错误码 | 描述 |
|-|-|
| 1003025 | 描述：ZEGO 后台禁止推流。<br />可能原因：开启了推流鉴权，流被媒体服务器禁止。<br />处理建议：若不是开发者调接口禁止的，请联系 ZEGO 技术支持解决。 |
| 1003028 | 描述：推流失败，房间内已有相同的流。<br />可能原因：房间内已有相同的流。<br />处理建议：更换新的流 ID。调整流名生成策略，保证唯一性。 |
| 1003040 | 描述：更新转推 CDN 状态失败。 <br />可能原因：转推地址 URL 不正确。 <br />处理建议：检查输入的 URL 是否有效。 |
| 1003050 | 描述：流附加信息为 null。<br />可能原因：流附加信息为 null。<br />处理建议：请检查流附加信息是否为正常。 |
| 1003051 | 描述：流附加信息超长。<br />可能原因：流附加信息超长。<br />处理建议：请检查对应的流附加信息是否太长 ，最大是 128 字节。|
| 1103001 | 描述：推流参数错误。<br />可能原因：推流参数错误。<br />处理建议：在传参错误的 extendedData 中查看具体错误字段。 |
| 1103002 | 描述：浏览器不支持。<br />可能原因：<ol><li>没有通过 https 或 locahost 的方式访问。</li><li>不支持你的浏览器版本。</li></ol><br />处理建议：<ol><li>请将项目部署在 https 服务器下，或者使用 localHost 访问。</li><li>如果上述问题还不能解决，请更换或者升级浏览器。</li></ol> |
| 1103003 | 描述：媒体连接失败。<br />可能原因：可能由于网络原因导致。<br />处理建议：请尝试切换网络后重试。 |
| 1103010 | 描述：屏幕共享失败。<br />可能原因：推屏幕流失败，参数设备不支持，使用插件推但并没装插件 extendedData 查看具体错误信息。<br />处理建议：请联系 ZEGO 技术支持。|
| 1103011 | 描述：设备信息枚举失败。<br />可能原因：获取设备列表枚举设备失败。<br />处理建议：拿到 console 里面具体错误信息，联系 ZEGO 技术支持。 |
| 1103019 | 描述：推流鉴权错误。<br />可能原因：推流参数中的 streamParams 设置错误导致拉流鉴权失败。<br />检查 streamParams 中的 token 是否正确，字符串拼接是否正确。 |
| 1103020 | 描述：获取媒体服务地址失败。<br />可能原因：获取媒体服务地址失败。<br />处理建议：拿到 console 里面具体错误信息，联系 ZEGO 技术支持。 |
| 1103021 | 描述：媒体服务连接失败。<br />可能原因：推拉流与媒体创建会话失败。<br />处理建议：拿到 console 里面具体错误信息，联系 ZEGO 技术支持。 |
| 1103022 | 描述：offer 创建失败。<br />可能原因：浏览器不兼容。<br />处理建议：切换浏览器或者拿到 console 里面获取具体错误信息，联系 ZEGO 技术支持。 |
| 1103023 | 描述：设置本地 SDP 失败。<br />可能原因：浏览器不兼容。<br />处理建议：切换浏览器或者拿到 console 里面获取具体错误信息，联系 ZEGO 技术支持。|
| 1103024 | 描述：SDP 发送失败。<br />可能原因：与网关交互的 mediaDesc 失败。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1103025 | 描述：远端 SDP 错误。<br />可能原因：浏览器不兼容。<br />处理建议：切换浏览器或者拿到 console 里面获取具体错误信息，联系 ZEGO 技术支持。 |
| 1103026 | 描述：webrtc 连接使用的 candidate 信息错误。<br />可能原因：与网关交互 candidate 失败。<br />处理建议：请联系 ZEGO 技术支持。|
| 1103027 | 描述：媒体连接关闭。<br />可能原因：SDK 与网关断开。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1103028 | 描述：ice 连接失败。<br />可能原因：UDP 断开，可能是因为网络原因，也可能由于网关主动断开。<br />处理建议：尝试切换网络重试，或者请联系 ZEGO 技术支持。 |
| 1103029 | 描述：推流参数设置错误。<br />可能原因：1.参数有问题 2.设置的参数设备不支持。<br />处理建议：拿到 console 里面获取具体错误信息，联系 ZEGO 技术支持。|
| 1103030 | 描述：推流失败重试超时。<br />可能原因：可能是由于网络原因导致协商超时或是未知问题阻塞流程。<br />处理建议：请尝试切换网络重试，或者联系 ZEGO 技术支持。 |
| 1103040 | 描述：未推流时修改参数。<br />可能原因：改变推流参数时推流未找到，可能是调用时没有在推流导致。 <br />处理建议：请检查在调用时是否有在推流。<br />注意事项：该错误码在 3.0.0 之前版本的含义为：“当前推流质量差、或网络较差，请检查您的网络。” |
| 1103041 | 描述：设备插拔或松动。<br />可能原因：设备插拔或松动。<br />处理建议：检查设备是否有插稳或是有接触不良的情况。|
| 1103042 | 描述：用户主动取消了屏幕共享。<br />可能原因：屏幕共享时在弹框选取时主动点击取消。<br />处理建议：请检查是否符合当前业务逻辑，若符合则无需解决。|
| 1103043 | 描述：当前浏览器不支持屏幕共享。<br />可能原因：当前浏览器不支持创建屏幕流。<br />处理建议：更新或者切换浏览器。|
| 1103044 | 描述：不是通过 [createStream](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#create-stream) 创建的流。<br />可能原因：Web 端报错，流不是通过 createStream 接口生成。<br />处理建议：Web 端：<br />1. 明确流的来源，确保流是由 createStream 接口生成。<br /> 2. createStream 是异步操作返回的是 promise，需要保证在 then 之后或者 await 之后，再去推流。 |
| 1103045 | 描述：媒体流不含视频。<br />可能原因：切换设备时流并未包含视轨，一般发生在调用 switchDevice 接口，创建流的时候使用参数 video:false。<br />处理建议：请检查流是否包含了视轨。 |
| 1103046 | 描述：媒体流不含音频。<br />可能原因：切换设备时流并未包含音轨，一般发生在调用 switchDevice 接口，创建流的时候使用参数 audio:false。<br />处理建议：请检查流是否包含了音轨。 |
| 1103047 | 描述：找不到 MediaStreamTrack 对象。<br />可能原因：可能在修改参数时，流并未包含对应类型轨，Web 端一般发生在调用 [replaceTrack](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#replace-track) 接口时。<br />处理建议：请检查流是否包含了对应的类型轨，或者联系 ZEGO 技术支持。 |
| 1103048 | 描述：找不到设备。<br />可能原因：切换设备时，设备 ID 不存在，可能发生在调用 switchDevice 接口，创建流的时候传参有误。<br />处理建议：请检查设备 ID 是否存在，或者联系 ZEGO 技术支持。 |
| 1103049 | 描述：重复拉取同一条流。<br />可能原因：可能是重复拉流或者之前拉流并未停止拉流。<br />处理建议：请检查拉流的逻辑是否正确，确认是否有重复拉流的情况。 |
| 1103050 | 描述：websocket 断开连接。<br />可能原因：可能是网络问题导致的媒体节点 socket 断开。<br />处理建议：请尝试切换网络后重试。 |
| 1103051 | 描述：推流重试超时。<br />可能原因：一般是网络原因。 <br />处理建议：请尝试切换网络后重试。如果大量出现，请联系 ZEGO 技术支持。 |
| 1103052 | 描述：CDN 推流错误。<br />可能原因：可能上转推cdn类型参数错误，cdnPublishConfig.type=["addpush" \| "delpush" \| "clearpush"]。<br />处理建议：请检查参数是否正确。 |
| 1103053 | 描述：必须使用 HTTPS 协议。<br />可能原因：webrtc 官方只允许 HTTPS 请求设备。<br />处理建议：请求使用 HTTPS。 |
| 1103054 | 描述：没有预览。<br />可能原因：修改推流参数和状态时，推流不存在。<br />处理建议：排查推流逻辑保证在推流成功再去调用；检查传参是否正确。 |
| 1103055 | 描述：找不到推流。<br />可能原因：推流没有被找到、设置流额外信息和转推 CDN（动态转推）的时候会报错。<br />处理建议：排查推流逻辑保证在推流成功再去调用；检查传参是否有问题。 |
| 1103056 | 描述：正在推流中。<br />可能原因：重复推流或者之前推流并未停止推流。<br />处理建议：请检查推流逻辑是否正确。 |
| 1103057 | 描述：本地混音音效资源解码失败。<br />可能原因：音频资源格式不正确，或浏览器不兼容导致。<br />处理建议：尝试切换浏览器或者联系 ZEGO 技术支持。|
| 1103058 | 描述：客户端 IP 发生变化。<br />可能原因：客户端 IP 发生变化。<br />处理建议：检查客户端 IP 是否有发生变动。 |
| 1103059 | 描述：推流网络节点过期。<br />可能原因：推流网络节点过期。<br />处理建议：联系 ZEGO 技术支持。|
| 1103060 | 描述：session 请求超时。<br />可能原因：session 请求超时。<br />处理建议：尝试切换网络后重试或者联系 ZEGO 技术支持。 |
| 1103061 | 描述：获取媒体失败。<br />可能原因：<br />1、在 iphone12.1 版本，创建流时，如果参数不在设备参数内有可能会报错，例如，手机只有 720p，接口 createStream 参数传入 1080p 的分辨率。<br />2、创建流时报错：NotReadableError，could not start audio source <br />处理建议：1、检查创建流的参数；2、拿到 console 中的错误信息，联系 ZEGO 技术支持。 |
| 1103064 | 描述：媒体流没有设备权限。<br />可能原因：媒体流没有设备权限，一般是 Web 端接口 [createStream](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~class~ZegoExpressEngine#create-stream) 报错。<br />处理建议：检查是否有给设备权限。|
| 1103065 | 描述：设备不可用于采集媒体流。<br />可能原因：可能是您当前的摄像头或麦克风被其他应用占用 。<br />处理建议：检查摄像头或麦克风是否正在被占用。|
| 1103066 | 描述：创建流的设备参数错误。<br />可能原因：创建流的设备参数错误或者设备无法满足。<br />处理建议：尝试检查或更换参数后重试。|
| 1103072 | 描述：不支持开启美颜功能。<br />可能原因：您当前使用的浏览器不支持美颜功能。<br />处理建议：请参考 [基础美颜](/real-time-video-web/video/basic-beauty) 中的浏览器兼容性说明，使用对应版本的浏览器开启美颜功能。|
| 1103073 | 描述：美颜功能正在启动中。<br />可能原因：由于设置美颜接口是异步执行的，媒体流的美颜功能开启还没完成的时候调用 startPublishingStream 接口推流，推流画面无法预期是否为美颜画面。<br />处理建议：如果需要在推流前开启美颜，需要先等待美颜异步启动完成后再调用推流接口进行推流。可参考 [基础美颜](/real-time-video-web/video/basic-beauty) 中的示例代码。|
| 1103074 | 描述：当前视轨不支持开启美颜。<br />可能原因：当前视轨不支持开启美颜，在启动时报错，SDK 会关闭美颜。<br />处理建议：尝试更换设备或视轨后重试或者联系 ZEGO 技术支持。|
| 1103075 | 描述：美颜性能过载。<br />可能原因：美颜性能过载。<br />处理建议：美颜性能过载时画面会卡顿，SDK 不会关闭美颜，开发者可自行判断是否关闭美颜能力。|
| 1103080 | 描述：AI 降噪运行时错误。<br />可能原因：AI 降噪运行时报错。<br />处理建议：请联系 ZEGO 技术支持。|
| 1103081 | 描述：不支持 AI 降噪。<br />可能原因：可能因为当前浏览器其不支持一些 API，导致的 AI 降噪初始化操作失败<br />处理建议：使用最新版的 Google Chrome 浏览器和 Edge 浏览器。|
| 1103082 | 描述：AI 降噪性能过载。<br />可能原因：AI 降噪过载。<br />处理建议：AI 降噪过载时会出现音频卡顿或音画不同步的现象，SDK 不会关闭 AI 降噪，开发者可自行判断是否关闭 AI 降噪能力。|

## 1004xxx/1104xxx 拉流相关错误码

调用拉流接口后，或者在拉流中途出现异常，开发者可从 [playerStateUpdate](@playerStateUpdate) 拉流状态回调获取相关错误码，请根据以下错误码进行相应的处理。

| 错误码 | 描述 |
|-|-|
| 1004002 | 描述：拉流失败。<br />可能原因：流不存在。<br />处理建议：请检查是否远端是否确实推流成功，或是否推流和拉流的环境不一致。|
| 1004020 | 描述：拉流临时中断。<br />可能原因：网络异常。<br />处理建议：请等待或者检查网络环境。|
| 1004025 | 描述：拉流失败。<br />可能原因：该流被后台系统配置为禁止推送。<br />处理建议：请联系技术支持解决。 |
| 1004099 | 描述：系统内部异常导致拉流失败。<br />可能原因：SDK 内部错误。<br />处理建议：请联系 ZEGO 技术支持人员。|
| 1104001 | 描述：拉流参数错误。<br />可能原因：拉流参数错误。<br />处理建议：请尝试检查 console 中打印的传参是否正确，extendedData 中可以查看的具体错误字段。|
| 1104020 | 描述：获取媒体服务地址失败。<br />可能原因：调度媒体节点失败，有可能是网络问题导致的。<br />处理建议：可以尝试切换网络后重试，如果无法解决请联系 ZEGO 技术支持。  |
| 1104021 | 描述：媒体服务连接失败。<br />可能原因：推拉流与媒体创建会话失败。<br />处理建议：联系 ZEGO 技术支持。 |
| 1104022 | 描述：offer 创建失败。<br />可能原因：可能是浏览器不兼容导致的。<br />处理建议：请尝试切换或者升级浏览器后重试。|
| 1104023 | 描述：设置本地 SDP 失败。<br />可能原因：可能是浏览器不兼容导致的。<br />处理建议：请尝试切换或者升级浏览器后重试。|
| 1104024 | 描述：SDP 发送失败。<br />可能原因：与网关交互的 mediaDesc 失败导致。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1104025 | 描述：远端 SDP 错误。<br />可能原因：可能是浏览器不兼容导致的。<br />处理建议：请尝试切换或者升级浏览器后重试。 |
| 1104026 | 描述：candidate 错误。<br />可能原因：与网关交互 candidate 失败导致。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1104027 | 描述：媒体连接关闭。<br />可能原因：SDK 与网关断开。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1104028 | 描述：ice 连接失败。<br />可能原因：一般是1.网络原因 2.网关主动断开。<br />处理建议：请尝试切换网络重试或者联系 ZEGO 技术支持。 |
| 1104029 | 描述：与媒体服务断开连接。<br />可能原因：一般是网络原因导致。<br />处理建议：请尝试切换网络后重试。 |
| 1104030 | 描述：服务端协商超时。<br />可能原因：可能是网络原因导致也可能由未知问题阻塞了流程。<br />处理建议：请尝试切换网络重试或者联系 ZEGO 技术支持。 |
| 1104031 | 描述：拉流失败重试超时。<br />可能原因：媒体节点连接不成功导致，一般是网络原因。<br />处理建议：请尝试切换网络重试或者联系 ZEGO 技术支持。 |
| 1104032 | 描述：已经在拉流中。<br />可能原因：重复拉流或者之前拉流并未停止拉流。<br />处理建议：请尝试排查拉流逻辑是否正确。|
| 1104033 | 描述：客户端 IP 发生变化。<br />可能原因：客户端 IP 发生变化。<br />处理建议：请检查客户端 IP 是否有变动。 |
| 1104034 | 描述：拉流网络节点过期。<br />可能原因：拉流网络节点过期。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1104035 | 描述：重置 session 推送。<br />可能原因：重置 session 推送。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1104036 | 描述：请求 session 超时。<br />可能原因：一般是网络原因导致。<br />处理建议：请尝试切换网络重试。 |
| 1104037 | 描述：质量探测超时。<br />可能原因：可能是网络原因导致。<br />处理建议：请尝试切换网络重试。 |
| 1104038 | 描述：当前 AppID 不支持 L3（Low-Latency Live Streaming，低延迟直播） 模式拉流。<br />可能原因：不支持的资源模式，标识当前 AppID 不支持 L3（Low-Latency Live Streaming，低延迟直播） 模式拉流。<br />处理建议：如有需要请联系 ZEGO 技术支持开启。 |
| 1104039 | 描述：拉流不存在，小程序中该错误码表示拉流失败。<br />可能原因：可能是重复拉流或者是房间不存在导致。<br />处理建议：请检查是否存在重复拉流或房间不存在。|
| 1104046 | 描述：拉流鉴权错误。<br />可能原因：拉流参数中的 streamParams 设置错误导致拉流鉴权失败。<br />处理建议：检查streamParams中的token是否正确，字符串拼接是否正确。|

## 1005xxx 混流相关错误码

调用开始混流 [startMixerTask](@startMixerTask) 和停止混流 [stopMixerTask](@stopMixerTask) 接口返回结果可能出现的错误。

| 错误码 | 描述 |
|-|-|
| 1005000 | 描述：无混流服务。<br />可能原因：当前 AppID 无混流服务。<br />处理建议：如有需要请联系 ZEGO 技术支持开启。 |
| 1005001 | 描述：混流任务 ID 为空。<br />可能原因：混流任务 ID 为空。<br />处理建议：请检查是否有在混流或者联系 ZEGO 技术支持开启。 |
| 1005002 | 描述：混流任务 ID 超长。<br />可能原因：混流任务 ID 超长，最大为 256 个字符。<br />处理建议：请检查混流任务 ID 是否过长。 |
| 1005003 | 描述：混流任务 ID 中含有非法字符。<br />可能原因：混流任务 ID 中含有非法字符。<br />处理建议：请检查混流任务 ID 是否有含有非法字符，仅支持数字,英文字符 和 '~', '!', '@', '#', '$', '', '^', '&', '*', '(', ')', '_', '+', '=', '-', ';', '’', ',' 。|
| 1005005 | 描述：混流任务配置未指定输出。<br />可能原因：可能是 outputConfig 配置不正确。<br />处理建议：请检查 outputConfig 是否正确配置。|
| 1005006 | 描述：混流输出目标有误。<br />可能原因：有可能在 “target” 为 “streamID” 时，传入了非法字符。<br />处理建议：请检查混流输出目标的 target 是否为 streamID 类型，如果是，请检查是否传入了非法字符，仅支持数字、英文字符和 "-"、"_"。 |
| 1005010 | 描述：启动混流任务请求失败。<br />可能原因：可能是网络问题导致的。<br />处理建议：请尝试切换网络后重试。|
| 1005011 | 描述：停止混流任务请求失败。<br />可能原因：可能是由于网络原因导致。<br />处理建议：请尝试切换网络后重试。|
| 1005012 | 描述：该混流任务必须由该任务的启动用户执行停止操作。<br />可能原因：可能是多个用户执行了混流和停止混流操作。<br />处理建议：请检查混流和停止混流逻辑，确保是同一个 UserID 操作。|
| 1005020 | 描述：混流任务输入流列表为空。<br />可能原因：可能是混流任务输入流列表的数据类型不符合格式导致。<br />处理建议：请检查混流任务输入流列表 inputList 是否符合官网格式。 |
| 1005021 | 描述：混流任务输出列表为空。<br />可能原因：可能是混流任务输出流列表的数据类型不符合格式导致。<br />处理建议：请检查混流任务输出流列表 outputList 是否符合官网格式。 |
| 1005023 | 描述：混流任务视频配置无效。<br />可能原因：可能是混流任务视频编码格式错误导致。<br />处理建议：检查混流的视频编码格式是否是"h264"或"VP8"。 |
| 1005025 | 描述：超过最大的输入流数量。<br />可能原因：输入流数量超过限制导致。<br />处理建议：请检查输入流数量，最大支持 9 个输入流。|
| 1005026 | 描述：输入流不存在。<br />可能原因：混流任务输入的流不存在。<br />处理建议：请检查混流任务输入流列表 inputList 里的流是否存在。|
| 1005027 | 描述：混流输入参数错误。<br />可能原因：可能是输入流的布局超过画布范围。<br />处理建议：请检查输入流的布局是否正常。 |
| 1005028 | 描述：混流输入文字水印过长。<br />可能原因：混流输入参数文字水印长度超过限制。<br />处理建议：请确保输入文字水印长度不超过 512 字节。 |
| 1005030 | 描述：超过最大的输出流数量。<br />可能原因：输出流数量超过限制导致。<br />处理建议：请检查输出流数量，最大支持 3 个输出流。|
| 1005034 | 描述：混流输入图片的链接过长。<br />可能原因：混流输入参数图片链接长度超出限制。<br />处理建议：请确保输入的图片链接长度不超过 1024 字节。 |
| 1005035 | 描述：混流输入图片失败。<br />可能原因：混流输入参数图片格式错误。<br />处理建议：使用 JPG 和 PNG 格式。支持 2 种使用方式：1. URI：将图片提供给 ZEGO 技术支持进行配置，配置完成后会提供图片 URI，例如：preset-id://xxx.jpg。2. URL：仅支持 HTTP 协议。 |
| 1005036 | 描述：混流输入图片失败。<br />可能原因：混流输入参数图片大小超出限制或图片不存在。<br />处理建议：图片大小限制在 1M 以内或检查图片路径是否正确。 |
| 1005050 | 描述：混流认证失败。<br />可能原因：可能是混流配置不正确。<br />处理建议：检查混流的配置或者联系 ZEGO 技术支持。 |
| 1005061 | 描述：开启混流失败。<br />可能原因：输入图片水印为空。<br />处理建议：请输入正确的水印参数 [ZegoWatermark]。 |
| 1005062 | 描述：开启混流失败。<br />可能原因：输入图片水印参数错误，可能是布局超过画布范围。<br />处理建议：请输入正确的水印参数 [ZegoWatermark]。 |
| 1005063 | 描述：开启混流失败。<br />可能原因：输入水印 URL 非法。<br />处理建议：水印 URL 必须以 "preset-id://" 开头且需要是 ".jpg" 或 ".png" 结尾。 |
| 1005067 | 描述：输入背景图 URL 非法。<br />可能原因：输入背景图 URL 非法导致。<br />处理建议：请检查背景图 URL 是否合法，必须以 “preset-id://” 开头且需要是 .jpg 或 .png 结尾。|
| 1005070 | 描述：未找到自动混流服务器。<br />可能原因：可能是自动混流服务器没有配置导致。<br />处理建议：请联系 ZEGO 技术支持配置。 |
| 1005099 | 描述：混流内部错误。<br />可能原因：有可能能是混流输入重复导致。<br />处理建议：请检查混流任务输入流列表 inputList 是否正确或者联系 ZEGO 技术支持。

## 1006xxx/1106xxx 设备错误

推流、拉流过程可能会造成使用的音视频设备出错，当开发者收到 [deviceError ](https://doc-zh.zego.im/article/api?doc=Express_Video_SDK_API~javascript_web~interface~ZegoRTCEvent#device-error) 设备异常回调参数的 “errorCode” 不为 0 时，请根据以下错误码进行相应的处理。

| 错误码 | 描述 |
|-|-|
| 1006006 |描述：设备被拔出。<br />可能原因：设备被拔出。<br />处理建议：请检查设备是否接触良好。 |
| 1106007 |描述：麦克风松动或者是快速拔插导致采集异常。<br />可能原因：麦克风被拔出。<br />处理建议：请检查麦克风是否接触良好。 |
| 1106008 |描述：摄像头松动或者是快速拔插导致采集异常。<br />可能原因：摄像头未连接。<br />处理建议：请检查摄像头是否接触良好。 |
| 1106009 |描述：检测到音频轨道采集异常停止。<br />可能原因：音频采集设备（如麦克风）连接异常。<br />处理建议：请检查设备是否连接正常。如果正常，请重试或联系 ZEGO 技术支持。 |
| 1106010 |描述：检测到视频轨道采集异常停止。<br />可能原因：视频采集设备（如摄像头）连接异常。<br />处理建议：请检查设备是否连接正常。如果正常，请重试或联系 ZEGO 技术支持。 |

## 1009xxx/1109xxx IM 错误

发送 IM 消息过程中出现的错误。

| 错误码 | 描述 |
|-|-|
| 1009001 | 描述：消息内容为空。<br />可能原因：消息内容不能为空。<br />处理建议：请检查消息内容是否为空。 |
| 1009002 | 描述：消息内容超长。<br />可能原因：消息内容超长。<br />处理建议：请检查发送的消息内容是否太长。 |
| 1009005 | 描述：发送消息的目标房间与当前登录的房间不一致。<br />可能原因：发送消息的目标房间与当前登录的房间不一致。<br />处理建议：发送消息传入当前登录的房间ID。 |
| 1009010 | 描述：发送消息失败。<br />可能原因：发送消息失败。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1009013 | 描述：发送消息失败。<br />可能原因：消息输入长度超出限制。<br />处理建议：检查输入内容长度或联系 ZEGO 技术支持扩容消息内容长度。 |
| 1009015 | 描述：发送房间广播消息失败。<br />可能原因：QPS 超过限制。<br />处理建议：控制最大 QPS 为 2。 |
| 1109001 | 描述：消息频率受限。<br />可能原因：消息发送过于频繁。<br />处理建议：请检查消息发送逻辑是否正确，降低消息发送的频次。|

## 1017xxx 版权音乐相关错误

使用 SDK 版权音乐相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1017000 | 描述：command 参数无效。<br />可能原因：输入的 command 参数为空。<br />处理建议：请传入正确的 command 参数, 详见 https://doc-zh.zego.im/online-ktv-android/client-api/send-extended-request#1。 |
| 1017001 | params 参数无效。<br />可能原因：输入的 params 参数为空。<br />处理建议：请传入正确的 params 参数。 |
| 1017002 | 描述：song_id 参数无效。<br />可能原因：输入的 song_id 参数为空。<br />处理建议：请传入正确的 song_id 参数, 详见 https://doc-zh.zego.im/online-ktv-android/client-api/send-extended-request#1。 |
| 1017003 | 描述：share_token 参数无效。<br />可能原因：输入的 share_token 参数为空。<br />处理建议：请传入正确的 share_token 参数，share_token 可以通过点歌 [requestResource] 获取。 |
| 1017004 | 描述：resource_id 参数无效。<br />可能原因：输入的 resource_id 参数为空。<br />处理建议：请传入正确的 resource_id 参数，resource_id 可以通过点歌/分享歌曲 [requestResource] [getSharedResource] 获取。 |
| 1017005 | 描述：start_position 参数无效。<br />可能原因：输入的 start_position 参数无效。<br />处理建议：请传入数值为正的 start_position 参数， 范围[0, 歌曲时长]。 |
| 1017006 | 描述：position 参数无效。<br />可能原因：输入的 position 参数无效。<br />处理建议：请传入数值为正的 position 参数， 范围[0, 歌曲时长]。 |
| 1017007 | 描述：volume 参数无效。<br />可能原因：输入的 Volume 参数无效。<br />处理建议：请传入正确的 Volume 参数, 范围[0, 200]。 |
| 1017008 | 描述：krcToken 参数无效。<br />可能原因：输入的 krcToken 参数为空。<br />处理建议：请传入正确的 krcToken 参数，krcToken 可以通过点伴奏 [requestAccompaniment] 获取 。 |
| 1017009 | 描述：版权音乐初始化鉴权失败。<br />可能原因：未设置 AppSign 或者 Token。<br />处理建议：在使用 Token 鉴权时，在调用 [initCopyrightedMusic] 前调用 [loginRoom]，或者使用 AppSign 鉴权。 |
| 1017010 | 描述：请求版权服务失败。<br />可能原因：输入参数错误或网络原因。<br />处理建议：请传入正确的调用参数并重试。 |
| 1017011 | 描述：本地磁盘空间不足。<br />可能原因：本地磁盘空间不足。<br />处理建议：请清理本地文件确保有足够磁盘空间。 |
| 1017012 | 描述：正在下载中。<br />可能原因：正在下载相同的资源。<br />处理建议：请等待资源下载成功。 |
| 1017013 | 描述：资源文件丢失。<br />可能原因：资源文件以被删除。<br />处理建议：请重新加载资源文件。 |
| 1017014 | 描述：过期的资源文件。<br />可能原因：资源文件已超过有效期。<br />处理建议：请重新点歌或点伴奏。 |
| 1017015 | 描述：无效的资源文件。<br />可能原因：文件已损坏。<br />处理建议：请调用 [download] 重新下载资源文件。 |
| 1017018 | 描述：资源 ID 未授权。<br />可能原因：该资源 ID 不是通过 [requestResource]、[getSharedResource] 接口获取的。<br />处理建议：请先调用 [requestResource]、[getSharedResource] 接口点歌获取有效的资源 ID。 |
| 1017019 | 描述：版权资源已过期。<br />可能原因：版权资源已过期。<br />处理建议：请重新点播此版权资源。 |
| 1017020 | 描述：该资源不支持此方法。<br />可能原因：资源 ID 传入错误。<br />处理建议：请传入正确的资源 ID。 |
| 1017030 | 描述：音乐无版权，无法收听和点唱歌曲。<br />可能原因：音乐无版权。<br />处理建议：请选择有版权的音乐。 |
| 1017031 | 描述：音乐无伴奏权限，只能收听歌曲，无法点唱。<br />可能原因：音乐无伴奏权限。<br />处理建议：请选择有词曲权限的音乐。 |
| 1017032 | 描述：非包月会员。<br />可能原因：未开通按包月会员。<br />处理建议：请开通按用户包月会员模式或使用按次计费模式点歌。 |
| 1017033 | 描述：没有伴奏资源。<br />可能原因：该歌曲没有伴奏资源。<br />处理建议：请选择有伴奏资源的歌曲。 |
| 1017034 | 描述：找不到资源<br />可能原因：找不到资源<br />处理建议：请选择其他歌曲。 |
| 1017040 | 描述：参数非法。<br />可能原因：传入的参数不正确。<br />处理建议：请输入正确的参数。 |
| 1017041 | 描述：AppID 不可用。<br />可能原因：当前 AppID 不支持版权音乐功能。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1017042 | 描述：不支持的计费模式。<br />可能原因：不支持的计费模式。<br />处理建议：请选择正确的计费模式。 |
| 1017043 | 描述：不合理的访问。<br />可能原因：包月会员按次计费点歌。<br />处理建议：请选择正确的计费模式。 |
| 1017044 | 描述：分享 token 过期。<br />可能原因：分享 token 过期。<br />处理建议：请选择未过期分享 token 获取资源。 |
| 1017045 | 描述：分享 token 非法。<br />可能原因：分享 token 非法。<br />处理建议：请选择正确分享 token 获取资源。 |
| 1017046 | 描述：krcToken 非法。<br />可能原因：krcToken 非法。<br />处理建议：请选择正确 krcToken 获取 krc 格式歌词。 |
| 1017047 | 描述：krcToken 过期。<br />可能原因：krcToken 过期。<br />处理建议：请选择未过期 krcToken 获取 krc 格式歌词。 |
| 1017048 | 描述：获取歌词失败。<br />可能原因：找不到歌词。<br />处理建议：请稍后重试。 |
| 1017049 | 描述：获取音高线失败。<br />可能原因：找不到音高线或者资源已下架。<br />处理建议：请稍后重试。 |
| 1017050 | 描述：房间内未分享该资源。<br />可能原因：房间内无用户分享该资源。<br />处理建议：请房间内任一用户调用 [requestResource] 接口请求资源并进行分享。 |
| 1017051 | 描述：该资源在房间内的免费获取次数用尽。<br />可能原因：1. 自己分享的资源无法再次获取；2. 已经获取过共享资源。<br />处理建议：请使用已经获取的资源，或者使用 [requestResource] 重新分享资源。 |
| 1017052 | 描述：该版权方不可用。<br />可能原因：版权方 ID 传入错误或者没有开通对应版权方。<br />处理建议：请传入正确的版权方 ID。 |
| 1017053 | 描述：该版权方不支持此方法。<br />可能原因：版权方 ID 传入错误。<br />处理建议：请传入正确的版权方 ID。 |
| 1017071 | 描述：无效的版权商 ID。<br />可能原因：版权方 ID 传入错误。<br />处理建议：请传入正确的版权方 ID。 |
| 1017072 | 描述：该版权方不支持此 songID。<br />可能原因：版权方曲库不存在此 songID。<br />处理建议：请传入正确的 songID。 |
| 1017073 | 描述：无效的 masterID。<br />可能原因：当选择按房主计费时，没有传入 masterID。<br />处理建议：请传入正确的 masterID。 |
| 1017074 | 描述：多版权搜索时 page 参数无效。<br />可能原因：第一次调用多版权搜索时，page 参数必须为 1。<br />处理建议：请传入正确的 page 取值。 |
| 1017075 | 描述：资源没有音高线。<br />可能原因：资源没有音高线。<br />处理建议：请传入正确的 songID。 |
| 1017076 | 描述：不支持的 sceneID。<br />可能原因：不支持的 sceneID。<br />处理建议：请传入已经开通场景对应的 sceneID。 |
| 1017077 | 描述：不支持的 topID。<br />可能原因：获取榜单歌曲时传入了不支持的 topID。<br />处理建议：请传入正确的 topID。 |
| 1017095 | 描述：版权音乐模块不支持此功能。<br />可能原因：版权音乐模块在当前平台下不支持此功能。<br />处理建议：请联系 ZEGO 技术支持处理。 |
| 1017096 | 描述：版权音乐模块未初始化。<br />可能原因：没有调用 [initCopyrightedMusic] 方法初始化版权模块。<br />处理建议：请先调用 [initCopyrightedMusic] 方法。 |
| 1017097 | 描述：系统繁忙。<br />可能原因：系统繁忙。<br />处理建议：请进行重试。 |
| 1017098 | 描述：网络异常导致失败。<br />可能原因：内部未知错误。<br />处理建议：请联系 ZEGO 技术支持处理。 |
| 1017099 | 描述：系统内部异常导致失败。<br />可能原因：内部未知错误。<br />处理建议：请联系 ZEGO 技术支持处理。 |

## 播放器插件相关错误码

| 错误码 | 描述 |
|-|-|
| 100001 | 描述：入参错误。<br />可能原因：传入了错误的参数、或不正确调用接口导致，不通过播放器的 onError 回调抛出。<br />处理建议：请检查传入的接口及其对应的参数。 |
| 100002 | 描述：环境错误。<br />可能原因：当前环境不支持使用播放器，不通过播放器的 onError 回调抛出。<br />处理建议：请联系 ZEGO 技术支持。 |
| 100003 | 描述：运行失败。<br />可能原因：一般是播放器自身原因导致的内部错误。<br />处理建议：请联系 ZEGO 技术支持。 |
| 100004 | 描述：网络错误。<br />可能原因：网络断开或网络异常。<br />处理建议：检查网络是否正常。 |
| 100005 | 描述：媒体解码错误。<br />可能原因：媒体无法打开、不支持的媒体格式、解码时出错无法解码。<br />处理建议：请联系 ZEGO 技术支持。 |
| 100006 | 描述：自动播放失败。<br />可能原因：由于浏览器自动播放策略的限制。<br />处理建议：需要用户点击触发视频的自动播放，即用户的点击事件触发调用播放器的 play() 方法。 |
