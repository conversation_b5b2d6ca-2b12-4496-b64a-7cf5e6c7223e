## 秒拉方案

##### 场景描述

在当前的社交直播娱乐中，经常会有一种场景，就是在观看某个主播直播的时候可以持续上拉，看下一位主播直播，由于大多数主播单推直播时使用的都是 CDN 直播。CDN 的时效性相对较慢，在这里 Zego 为大家推出一种方案为直播秒拉方案，在观看这种模式直播时实现快速拉流。

秒拉方案的实现在于上拉切换的瞬间快速拉取直播主播的下一条流，在拉流页面上下滑动页面快速显示主播画面。

##### 前置条件
- 已在项目中集成 ZEGO Express SDK，实现基本的实时音视频功能，详情请参考 [快速开始 - 集成](/real-time-video-ios-oc/quick-start/integrating-sdk) 和 [快速开始 - 实现流程](/real-time-video-ios-oc/quick-start/implementing-video-call)。
- 已在 [ZEGO 控制台](https://console.zego.im/) 创建项目，并申请有效的 AppID，详情请参考 [控制台 - 项目管理](/console/project-info) 中的“项目信息”。
- 主播推流端推流到CDN上，并设置优先级。当多主播连麦时需开启混流，并将混流输出到各自主播的CDN地址上，设置CDN优先级覆盖原单主播CDN流；当断开连麦后，恢复单主播推流，连麦过的各个主播推流到格式CDN地址上，设置CDN优先级覆盖混流。**主播流的CDN地址需在直播中（单推或是连麦）保持一直不变**
- 由于连麦时开启混流任务后推流到CDN地址上覆盖过程也是需要一定时间，则观众在看到连麦的画面会稍有延时。

##### 使用原理及步骤

秒拉方案的具体的实现步骤，如下图：
<Frame width="512" height="auto" caption="">
  <img src="https://storage.zego.im/sdk-doc/Pics/quickplay1.png" />
</Frame>

##### 多房间模式

> 1、在进入拉流页面前先 [设置多房间模式](https://doc-zh.zego.im/article/api?doc=express-video-sdk_API~objectivec_ios~class~ZegoExpressEngine#set-room-mode)，然后初始化SDK（在房间列表界面或者进入拉流页面之前）。
>
> 2、进入拉流页面A后，开始登录房间A，并行拉流第一个在线流 StreamID_A，此时不需要静音 StreamID_A。
>
> 3、当从页面A滑动到下一个页面B时，在页面B 出现（或者自定义滑动的距离达到一个 offset） 时，拉取房间B中的流 StreamID_B，此时页面B还没完全显示出来，但是已经进行拉流，为了避免两个声音播放突兀，可以先把即将要出现的页面B中的流 StreamID_B 先静音。（若房间B中在连麦，则同步拉连麦者的流；若房间B是在进入之后才连麦，则需要在收到房间流新增回调后，添加控件通过流名 StreamID 去拉取其他连麦主播的流，断开连麦在收到房间流删除回调后去停止拉相应主播的流并移除控件）。
>
> 4、当滑动方向的下一个页面（即页面B）显示出来后，停止拉取 StreamID_B 外的所有其他流，并且恢复该流的声音。
>
> 5、当退出拉流页面时退出已登录的房间，多房间模式有登录房间数的限制，否则会出错。


##### 1 设置多房间模式并初始化 SDK
该方案使用到 Zego 的多房间模式，用户需提前设置好多房间模式创建引擎。采用多房间模式的好处在于可以使用到即构的房间服务，即房间消息广播，观众与主播互动等。
```objc
ZegoEngineProfile *profile = [[ZegoEngineProfile alloc] init];
profile.scenario = ZegoScenarioGeneral;
profile.appID = @"your appID";
profile.appSign = @"your appSign";
// 设置多房间
[ZegoExpressEngine setRoomMode:ZegoRoomModeMultiRoom];
// 初始化 SDK
[ZegoExpressEngine createEngineWithProfile:profile eventHandler:handler];
```


进入到直播的页面的时候在 UI 页面显示的回调中（也就是每一次上拉出现下一个画面的回调中）开始拉流：
```objc
[[ZegoExpressEngine sharedEngine] loginRoom:roomID user:zegoUser config:roomConfig];
ZegoPlayerConfig *playConfig = [ZegoPlayerConfig new];
playConfig.roomID = roomID;
// 这里可以自行实现区分 CDN 拉流，L3拉流，或者是 RTC 拉流。一般情况下使用 CDN 的情况比较多。
switch (zegoPlayStreamType) {
    case PlayStreamMode_CDN:
    {
        ZegoCDNConfig *zgCdnConfig = [ZegoCDNConfig new];
        // 获取对应的 CDN 地址
        zgCdnConfig.url = @"主播对应的 CDN 地址";
        playConfig.cdnConfig = zgCdnConfig;
        playConfig.resourceMode = ZegoStreamResourceModeOnlyCDN;
    }
        break;
    case PlayStreamMode_L3:
    {
        playConfig.resourceMode = ZegoStreamResourceModeOnlyL3;
    }
        break;
    case PlayStreamMode_RTC:
    {
        playConfig.resourceMode = ZegoStreamResourceModeOnlyRTC;
    }
        break;
    default:
        break;
}


ZegoCanvas *playCanvas = [ZegoCanvas canvasWithView:playview];
playCanvas.viewMode = ZegoViewModeAspectFill;
[[ZegoExpressEngine sharedEngine] startPlayingStream:streamID canvas:playCanvas config:playConfig];

if (self.isFirstLoad) {
    // 记录第一次加载。刚进来的时候不需要静音
    self.isFirstLoad = NO;
} else {
    // 先展示视频 不覆盖声音。 所以 先静音, 等展示完毕再取消静音
    [[ZegoEngineManager shareInstance] mutePlayStreamAudio:YES streamID:streamID];
}
```

下一步是在直播页面显示完毕后停止拉取上一个页面的流，然后恢复当前页面流的声音

```objc
// 停止拉取消失的视图的流
[[ZegoExpressEngine sharedEngine] stopPlayingStream:上一页面的streamID];
// 这里推出上一个页面的房间
[[ZegoExpressEngine sharedEngine] logoutRoom:roomID];
// 把原来静音的流恢复声音
[[ZegoExpressEngine sharedEngine] mutePlayStreamAudio:NO streamID:当前显示页面的streamID];
```

##### 大厅模式
若不需要使用即构的房间服务，可以采用大厅模式，即无需登录每一个主播的房间也可以浏览到主播的直播内容。开发者在用户本地构造一个本地的房间用于本地用户登录即可，该房间只会有本地用户一人，用于即构的拉流观看直播。当前用户登入该房间即可开始拉流，即构支持跨房间拉流。

大厅模式的步骤流程图如下：
<Frame width="512" height="auto" caption="">
  <img src="https://storage.zego.im/sdk-doc/Pics/quickplay2.png" />
</Frame>
##### > 1、用户在初始化创建 SDK 后，便可操作登录一个构造的仅供本地拉流使用的房间，登录了房间才能采取拉流。
>
> 2、进入拉流页面A后，并行拉流第一个在线流StreamID_A，此时不需要静音StreamID_A。
>
> 3、当从页面A滑动到下一个页面B时，滑动的距离达到 offset 时，拉取房间B中的流 StreamID_B，此时页面B还没完全显示出来，但是已经进行拉流，为了避免两个声音播放突兀，可以先把即将要出现的页面B中的流 StreamID_B 先静音。
>
> 4、当滑动方向的下一个页面（即页面B）显示出来后，停止拉取 StreamID_B 外的所有其他流，并且恢复该流的声音
>
> 5、当退出拉流页面时可退出已登录的房间。

由于大厅模式仅是登录一个房间进行拉流，所以在滑动观看主播时无法通过即构的服务来获取当前主播是否在与其他人连麦，使用大厅模式时可根据需求采用服务端下发其他信息来得知当前是否在连麦进行 UI 界面的控件操作。


##### 总结
经过测试对比，秒拉方案实现的拉流首帧以及渲染速度如下：
- SDK在接收到第一帧数据到渲染出第一帧画面的耗时大部分在20~60ms之间。
- 在正常网络下，拉流成功到接收到一帧数据的耗时基本上在40ms以下，在网络较慢的情况下，改耗时能在1~2s内。
- 在网络很好的情况下，CDN首帧耗时也能做到100ms内。正常WiFi网络下，耗时大部分在200~500ms。
