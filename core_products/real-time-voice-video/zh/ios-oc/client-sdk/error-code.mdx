---
articleID: 4377
---
# 常见错误码

- - -

## 1000xxx 通用错误码

通用类错误码通常会从 `onRoomStateChanged`、`onPublisherStateUpdate`、`onPlayerStateUpdate` 中抛出，请根据以下错误码并进行相应处理。

| 错误码枚举值 | 说明 |
|-|-|
| 0 | 执行成功 |
| 1000001 | 描述：未初始化引擎，无法调用非静态函数。<br />可能原因：未创建引擎。<br />处理建议：请先调用 [createEngine] 函数创建引擎，再调用当前的函数。 |
| 1000002 | 描述：未登录房间，无法支持功能实现。<br />可能原因：未登录房间。<br />处理建议：请先调用 [loginRoom] 登录房间，并在进房后的在线期间使用相关功能。 |
| 1000003 | 描述：未启动引擎音视频模块，无法支持功能实现。<br />可能原因：未启动引擎的音视频模块。<br />处理建议：请先调用 [startPreviewView] [startPublishingStream] [startPlayingStream] 启动音视频模块。 |
| 1000006 | 描述：调用在当前系统/平台上不支持的函数。<br />可能原因：例如在非 Android 系统上调用设置 Android 上下文环境的函数。 <br />处理建议：检查系统环境是否匹配。 |
| 1000007 | 描述：无效的 Android 上下文环境。<br />可能原因：未设置或者设置了错误的 Android 上下文环境。 <br />处理建议：设置正确的 Android 上下文环境。 |
| 1000008 | 描述：已调用 `setEventHandler` 设置了 handler，请勿重复设置。<br />可能原因：重复调用 `setEventHandler` 设置了 handler。<br />处理建议：若需要重复设置，请先调用 `setEventHandler` 将之前的 handler 置空。 |
| 1000010 | 描述：当前 SDK 未支持该功能。<br />可能原因： 使用的 SDK 版本未包含该功能。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1000014 | 描述：输入 StreamID 超长。<br />可能原因：在调用 [startPublishingStream] 或 [startPlayingStream] 时传入的 streamID 参数长度超过限制。<br />处理建议：StreamID 应小于 256 字节。 |
| 1000015 | 描述：输入 StreamID 为空。<br />可能原因：在调用 [startPublishingStream] 或 [startPlayingStream] 时传入的 StreamID 参数为空指针或空字符串。<br />处理建议：检查调用函数时传入的 streamID 参数是否正常。 |
| 1000016 | 描述：输入 StreamID 包含非法字符。<br />可能原因：在调用 [startPublishingStream] 或 [startPlayingStream] 时传入的 StreamID 包含非法字符。<br />处理建议：检查调用函数时传入的 StreamID 参数是否正常，仅支持数字、英文字符和 "-"、"\_"。 |
| 1000017 | 描述：非法输入参数。<br />可能原因：参数为空、或参数为非法值。<br />处理建议：请输入正确的参数。 |
| 1000018 | 描述：输入 CDN URL 超长。<br />可能原因：在调用[enablePublishDirectToCDN] 或 [startPlayingStream] 时传入的 URL 参数长度超过限制。<br />处理建议： URL 长度应小于 1024 字节。 |
| 1000019 | 描述：输入 CDN 鉴权参数超长。<br />可能原因：在调用[enablePublishDirectToCDN] 或 [startPlayingStream] 时传入的 CDN 鉴权参数长度超过限制。<br />处理建议： 鉴权参数长度应小于 512 字节。 |
| 1000020 | 描述：登录房间、登录场景的用户 ID 或用户名称不相同。<br />可能原因：登录房间、登录场景传入了不同的用户 ID 或用户名。<br />处理建议：登录房间、登录场景传入同一个用户 ID 或用户名。 |
| 1000037 | 描述：该 AppID 已经下线。<br />处理建议：请在 ZEGO 官网控制台检查该 AppID 的状态，或联系技术支持。 |
| 1000038 | 描述：服务端后台配置有误。<br />可能原因：1. 域名配置错误；2. 媒体网络异常；3. 媒体网络链接为空。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1000039 | 描述：服务端拒绝访问。<br />可能原因：1. APP 开启了禁止域外 IP 访问的限制，当前客户端处于域外。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1000055 | 描述：CDN 地址有误。<br />可能原因：设置的 CDN URL 不是标准 RTMP 或 FLV 协议。<br />处理建议：请检查 CDN URL 的协议或者格式是否正确。 |
| 1000060 | DNS 解析失败，请检查网络配置。此错误码已废弃。 |
| 1000065 | 服务器调度异常，请联系技术支持解决。此错误码已废弃。 |
| 1000066 | 描述：已启动引擎音视频模块，不支持该设置。<br />可能原因：仅支持在启动引擎的音视频模块前进行设置。<br />处理建议：请在调用 [startPreviewView] [startPublishingStream] [startPlayingStream] 启动音视频模块前进行设置。 |
| 1000067 | 描述：已登录房间，不支持该设置。<br />可能原因：仅支持在登录房间前进行设置。<br />处理建议：请在调用 [loginRoom] 登录房间前或者调用 [logoutRoom] 退出房间后进行设置，注意若登录了多房间，需要退出所有房间后才能进行设置。 |
| 1000070 | 描述：加载 SDK 动态库失败。<br />可能原因：传入的 SDK 动态路径错误。<br />处理建议：请传入正确的 SDK 动态库路径。 |
| 1000071 | 描述：加载 SDK 动态库时获取 SDK 导出函数失败。<br />可能原因： SDK 动态库版本不匹配。<br />处理建议：请加载匹配版本的 SDK 动态库。 |
| 1000072 | 描述：不支持此调用。<br />可能原因：没有开启 ZEGOEXP_EXPLICIT 宏。<br />处理建议：请在预处理器中添加 ZEGOEXP_EXPLICIT 宏。 |
| 1000090 | 描述：SDK 内部空指针错误。<br />可能原因：Android JVM 环境异常。<br />处理建议：请检查 Android JVM 环境是否正常，或联系技术支持。 |

## 1001xxx 调试阶段等 SDK 内部校验相关的错误码

调试阶段错误码指的是开发者在应用开发期间容易出现的错误，一般表现为接口参数出错等。此时开发者可从 `onDebugError`、`onRoomStateChanged`、`onPublisherStateUpdate`、`onPlayerStateUpdate` 等回调中获取相关错误码。

| 错误码枚举值 | 说明 |
|-|-|
| 1001000 | AppID 不能为 0，请检查 AppID 是否正确 |
| 1001001 | 输入 AppSign 长度必须为 64 字节 |
| 1001002 | 输入 AppSign 包含非法字符, 仅支持 '0'-'9', 'a'-'f', 'A'-'F' |
| 1001003 | 输入 AppSign 为空 |
| 1001004 | 描述：认证失败。<br />可能原因：AppID 不正确；使用环境不对。<br />处理建议：检查传入的 AppID 是否与 ZEGO 控制台中的 AppID 一致；检查 AppID 配置的环境与 SDK 设置的环境是否一致。 |
| 1001005 | 描述：认证失败。<br />可能原因：AppSign 不正确。<br />处理建议：检查传入的 AppSign 是否与 ZEGO 控制台中的 AppSign 一致。 |
| 1001006 | 输入 license 为空。 |
| 1001014 | 描述：没有日志文件写入权限。<br />可能原因：App 没有日志文件保存目录的写权限。<br />处理建议：检查 App 是否具有写文件权限；检查 App 是否有日志文件保存目录的写权限。 |
| 1001015 | 描述：超长的日志文件路径。<br />可能原因：设置的日志文件路径超过限制。<br />处理建议：检查日志文件路径是否过长。 |
| 1001020 | 描述：设置房间模式失败。<br />可能原因：在初始化 SDK 之后设置房间模式。<br />处理建议：在初始化 SDK 之前设置房间模式。 |
| 1001021 | 描述：设置地理围栏失败。<br />可能原因：在初始化 SDK 之后设置地理围栏。<br />处理建议：在初始化 SDK 之前设置地理围栏。 |
| 1001091 | 描述：实验性 API json 参数解析失败。<br />可能原因：json 内容格式不正确；方法名或参数不正确。<br />处理建议：检查 json 内容格式是否正确；检查方法名或参数是否正确，具体的方法名、参数请联系技术支持。 |

## 1002xxx 房间相关的错误码

调用`loginRoom`出错、 SDK 与 ZEGO 房间服务端链接出现异常后，或者在房间中途出现异常时，开发者可从 `onDebugError`、`onRoomStateChanged` 获取相关错误码，请根据以下错误码说明进行相应的处理。

| 错误码枚举值 | 说明 |
|-|-|
| 1002001 | 描述：登录房间数量超过上限，当前仅支持同时登录 1 个主房间。<br />可能原因：单房间模式下，同时登录多个主房间（包括在未退出房间A的情况下，重复调用登录同一个房间A）。<br />处理建议：检查是否单房间模式下，同时登录多个主房间。 |
| 1002002 | 描述：未使用此房间 ID 登录过。<br />可能原因：调用 [logoutRoom] 或[switchRoom] 或 [renewToken] 或 [setRoomExtraInfo] 前，未使用此房间 ID 登录过。<br />处理建议：检查是否使用此房间 ID 登录过。 |
| 1002005 | 描述：输入的用户 ID 为空。<br />可能原因：输入的用户 ID 为空。<br />处理建议：检查输入的用户 ID 是否为空。 |
| 1002006 | 描述：输入的用户 ID 包含非法字符。<br />可能原因：输入的用户 ID 包含非法字符。<br />处理建议：用户 ID 中只能包含数字，英文字符 和 '~', '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '=', '-', '`', ';', ',', '.', '\<', '>', '\'。 |
| 1002007 | 输入用户 ID 超长。<br />[loginRoom] 函数输入的用户 ID 长度大于或等于 64 字节。<br />请检查 [loginRoom] 函数调用时输入的用户 ID，确保其长度小于 64 字节。 |
| 1002008 | 输入用户名为空。<br />[loginRoom] 函数输入的用户名为空。<br />请检查 [loginRoom] 函数调用时输入的用户名，确保其非空。 |
| 1002009 | 输入用户名包含非法字符。<br />[loginRoom] 函数输入的用户名包含非法字符。<br />请检查 [loginRoom] 函数调用时输入的用户名，确保其仅包含数字，英文字符和 '~', '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '=', '-', '`', ';', ',', '.', '\<', '>', '/', '\'。 |
| 1002010 | 输入用户名超长。<br />[loginRoom] 函数输入的用户名长度大于或等于 256 字节。<br />请检查 [loginRoom] 函数调用时输入的用户名，确保其长度小于 256 字节。 |
| 1002011 | 输入房间 ID 为空。<br />[loginRoom] 函数输入的房间 ID 为空。<br />请检查 [loginRoom] 函数调用时输入的房间 ID ，确保其非空。 |
| 1002012 | 输入房间 ID 包含非法字符。<br />[loginRoom] 函数输入的房间 ID 包含非法字符。<br />请检查 [loginRoom] 函数调用时输入的房间 ID ，确保其仅包含数字，英文字符和 '~', '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '=', '-', '`', ';', ',', '.', '\<', '>', '\'。 |
| 1002013 | 输入房间 ID 超长。<br />[loginRoom] 函数输入的房间 ID 长度大于或等于 128 字节。<br />请检查 [loginRoom] 函数调用时输入的房间 ID ，确保其长度小于 128 字节。 |
| 1002014 | 房间附加信息 key 为空。<br />[setRoomExtraInfo] 函数输入房间附加信息 key 为空。<br />请检查 [setRoomExtraInfo] 函数调用时输入的房间附加信息 key，确保其非空。 |
| 1002015 | 房间附加信息 key 超长。<br />[setRoomExtraInfo] 函数输入的房间附加信息 key 长度大于或等于 128 字节。<br />请检查 [setRoomExtraInfo] 函数调用时输入的房间附加信息 key，确保其长度小于 128 字节。 |
| 1002016 | 房间附加信息 value 超长。<br />[setRoomExtraInfo] 函数输入的房间附加信息 value 长度大于或等于 128 字节。<br />请检查 [setRoomExtraInfo] 函数调用时输入的房间附加信息 value，确保其长度小于 128 字节。 |
| 1002017 | 描述：房间附加消息设置的 key数量超过支持的最大数量限制 <br />可能原因：调用 setRoomExtraInfo 多次传入了不同的key。 <br />处理建议：目前只支持一个key。 |
| 1002018 | 描述：多房间模式下，登录房间的用户 ID 或用户名称不相同。<br />可能原因：多房间模式下，登录多房间传入了不同的用户 ID 或用户名。<br />处理建议：多房间模式下，登录多房间传入同一个用户 ID 或用户名。 |
| 1002019 | 描述：多房间模式下，[switchRoom] 函数不可用。 <br />可能原因：多房间模式下，SDK不支持[switchRoom]接口。<br />处理建议：可先调用[logoutRoom],再调用[loginRoom]。 |
| 1002030 | 描述：登录房间失败，可能是由于网络原因。 <br />可能原因：当前网络不可用。<br />处理建议：建议切换网络尝试。 |
| 1002031 | 描述：登录房间超时，可能是由于网络原因。 <br />可能原因：当前网络延时较大。<br />处理建议：建议切换网络尝试。 |
| 1002033 | 描述：登录房间鉴权失败。 <br />可能原因：登录传入的token错误，或者过期。<br />处理建议：设置新token。 |
| 1002034 | 描述：登录房间的用户数超过该房间配置的最大用户数量限制（测试环境下，默认房间最大用户数为 50）。 <br />可能原因：当前房间内用户数过多。<br />处理建议：联系ZEGO技术支持，增加房间用户数限制。 |
| 1002035 | 描述：测试环境下，同时登录的房间总数量超过限制，（最大并发房间数上限为 10）。<br />可能原因：同时登录的房间过多。<br />处理建议：退出部分房间，在重新登录。 |
| 1002036 | 描述：登录失败，多房间功能没有开通，请联系 ZEGO 技术支持人员。<br />可能原因：多房间功能未开通。 <br />处理建议：联系ZEGO技术支持。 |
| 1002037 | 同时登录的房间总数量超过限制， 请联系 ZEGO 技术支持人员(该错误码已废弃)。 |
| 1002050 | 描述：用户被踢出房间。<br />可能原因：可能是相同用户 ID 在其他设备登录。<br />处理建议：使用唯一的用户 ID。 |
| 1002051 | 描述：房间连接临时中断，正在重试。<br />可能原因：网络原因。<br />处理建议：请稍等或检查网络是否正常。 |
| 1002052 | 描述：房间连接断开。<br />可能原因：网络原因。<br />处理建议：请检查网络是否正常或切换网络环境。 |
| 1002053 | 描述：重试登录房间超过最大的重试时间。<br />可能原因：网络原因。<br />处理建议：请检查网络是否正常或切换网络环境。 |
| 1002055 | 描述：业务后台发出了踢出房间信令。<br />可能原因：业务后台发出了踢出房间信令。<br />处理建议：联系业务后台处理。 |
| 1002056 | 描述：业务后台发出了踢出房间信令。<br />可能原因：由于 token 过期， 业务后台发出了踢出房间信令。<br />处理建议：请在收到 token 即将过期的通知回调后及时更新 token。 |
| 1002057 | 描述：业务后台发出了踢出房间信令。<br />可能原因：由于内部会话异常， 业务后台发出了踢出房间信令。<br />处理建议：联系业务后台处理。 |
| 1002061 | 描述：登录房间顺序错误。<br />可能原因：未登录主房间就登录多房间。<br />处理建议：登录多房间前先调用 `loginRoom` 登录主房间。 |
| 1002062 | 描述：退出房间顺序错误。<br />可能原因：未退出多房间就退出主房间。<br />处理建议：退出主房间前先退出多房间。 |
| 1002063 | 描述：没有多房间权限。<br />可能原因：没有多房间权限。<br />处理建议：请联系技术支持开启。 |
| 1002064 | 描述：房间 ID 已被登录房间接口使用，未退出房间之前，当前用户无法再登录该房间。<br />可能原因：房间 ID 已被登录房间接口使用。<br />处理建议：退出相同房间 ID 的房间。 |
| 1002065 | 描述：此方法已在版本 2.9.0 以后已废。<br />可能原因：此方法已在版本 2.9.0 以后已废。<br />处理建议：请先在引擎初始化之前调用 [setRoomMode] 函数选择多房间模式，再调用 [loginRoom] 登录多房间 |
| 1002066 | 描述：用户登录房间时如果在服务器黑名单中，则会返回此错误码，表示禁止登录房间。<br />可能原因：用户当前在服务器黑名单中。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1002067 | 描述：用户创建引擎时，未传递[AppSign]，登录房间时也未传递[Token]，则会返回此错误码。<br />可能原因：登录房间未传 [Token]。<br />处理建议：登录房间时设置[Token]。 |
| 1002071 | 描述：该接口调用频率超过上限。<br />可能原因：本应用调用该接口频率过大。<br />处理建议：控制对该接口的调用频率，详情可参考：/real-time-video-android-java/best-practice/restrictions。 |
| 1002072 | 描述：房间内用户调用该接口频率超过上限。<br />可能原因：房间内用户调用该接口频率过大。<br />处理建议：控制房间内用户对该接口的调用频率，详情可参考：/real-time-video-android-java/best-practice/restrictions。 |
| 1002073 | 描述：房间信令类接口调用频率超过上限。<br />可能原因：本应用调用房间信令类接口频率过大。<br />处理建议：控制应用对房间信令类接口（如 [sendCustomCommand] [sendBroadcastMessage]）的调用频率，详情可参考：/real-time-video-android-java/best-practice/restrictions。 |
| 1002074 | 描述：Token 内部错误。<br />可能原因：内部未知错误。<br />处理建议：联系即构技术支持处理。 |
| 1002075 | 描述：Token 格式错误。<br />可能原因：生成Token 传递的参数payload 为非json格式。<br />处理建议：生成Token时正确传递payload 为json 格式。 |
| 1002076 | 描述：Token Appid错误。<br />可能原因：生成的Token的Appid 和SDK使用的Appid不一致，或者Appid类型错误。<br />处理建议：使用对应生成的Token 的Appid。 |
| 1002077 | 描述：Token userID错误。<br />可能原因：生成的Token的userID 和登录使用的userID不一致, 或者userID 类型错误。<br />处理建议：使用和生成Token的userID登录。 |
| 1002078 | 描述：Token 过期。<br />可能原因：Token 过期，或者生成Token的有效期参数类型错误。<br />处理建议：重新生成Token。 |
| 1002079 | 描述：Token 版本错误。<br />可能原因：生成Token version 错误。<br />处理建议：使用正确的version 生成Token。 |
| 1002080 | 描述：Token nonce 参数类型错误。<br />可能原因：生成Token 的nonce 参数类型错误。<br />处理建议：确保nonce 为int64 类型数据。 |
| 1002081 | 描述：需要使用多房间模式。<br />可能原因：使用了错误的房间模式，例如：单房间模式。<br />处理建议：在引擎初始化之前使用 [setRoomMode] 函数选择多房间模式。 |
| 1002082 | 描述：更新房间的 Token 没有推流权限。<br />可能原因：Token 无推流权限。<br />处理建议：传入带有推流权限的 Token。 |
| 1002083 | 描述：登录时附加的自定义数据超出长度限制。<br />可能原因：登录自定义回调数据过长。<br />处理建议：控制登录自定义数据长度不超过128字节。 |
| 1002084 | 描述：登录时使用的 license 鉴权，license 未找到。<br />可能原因：license 填写错误。 <br />处理建议：校对下 license 的正确性。 |
| 1002085 | 描述：登录时使用的 license 鉴权，license 已过期。<br />处理建议：联系即构技术支持处理。 |
| 1002086 | 描述：用户已在别处登录。<br />处理建议：确认登录用户是否已再别处登录。 |
| 1002087 | 描述：使用了 license 鉴权，在使用相关功能时，license 配置了功能限制，不允许使用该功能。<br />处理建议：校对下 license 的功能限制，合理使用功能。 |
| 1002099 | 描述：系统内部异常导致房间登录失败。<br />可能原因：内部未知错误。<br />处理建议：联系即构技术支持处理。 |

## 1003xxx 推流相关的错误码

调用推流接口后，或者在推流中途出现异常，开发者可从 `onPublisherStateUpdate` 获取相关错误码，请根据以下错误码进行相应的处理。

调用转推 CDN 或更新流附加信息等接口后，开发者可从 API 的 callback 参数(java为匿名类，oc为block，c++为lambda)中获取相关错误码。

> 处理推流回调错误码非 0 的情况：
>
> 1. 当 state 为 PUBLISHER_STATE_NO_PUBLISH 时，且 errcode 非 0，表示推流失败，同时不会再进行重试推流了，此时可在界面作出推流失败提示；
> 2. 当 state 为 PUBLISHER_STATE_PUBLISH_REQUESTING 时，且 errcode 非 0，表示在重试推流，此时如果超出重试时间未成功推流会抛出推流失败通知。

| 错误码枚举值 | 说明 |
|-|-|
| 1003001 | 描述：推流没有数据导致推流失败。<br />可能原因：推流没有数据。<br />处理建议：检查视频、音频采集模块是否正常工作。 |
| 1003002 | 描述：码率设置错误导致推流失败。<br />可能原因：设置的视频码率、音频码率或者流量控制的最低视频码率阈值超过上限。<br />处理建议：请检查单位(kbps)是否有误。调整码率的设置。 |
| 1003005 | 描述：流量控制的属性参数设置有误。<br />可能原因：设置的流量控制的属性小于 0 或者超过所有组合。<br />处理建议：检查流量控制的属性参数的设置。 |
| 1003010 | 描述：推流失败，不支持 H.265 编码。<br />可能原因：硬件设备不支持 H.265 编码，或 SDK 未包含 H.265 编码模块。<br />处理建议：联系 ZEGO 技术支持确认 SDK 是否包含 H.265 编码模块，如果硬件设备不支持，建议升级硬件。 |
| 1003011 | 描述：主体分割设备性能不足。<br />可能原因：设备性能不足。<br />处理建议：更换设备。 |
| 1003012 | 描述：该设备不支持主体分割。<br />可能原因：设备不支持，或者开启了多个通道的主体分割。<br />处理建议：更换设备或者只开启一个通道的主体分割。 |
| 1003013 | 描述：主体分割未授权。<br />可能原因：该 appid 未配置主体分割功能。<br />处理建议：请联系 ZEGO 技术支持配置主体分割功能。 |
| 1003014 | 描述：主体分割参数错误。<br />可能原因：主体分割设置为绿幕类型，不允许设置背景虚化。<br />处理建议：请使用正确的参数开启主体分割。 |
| 1003015 | 描述：主体分割背景加载错误。<br />可能原因：背景视频或图片的路径不正确，或者内容错误。<br />处理建议：请设置正确的背景图片或视频。 |
| 1003020 | 描述：推流临时中断，正在重试.<br />可能原因：网络波动或者网络信号不好。<br />处理建议：请稍等或检查网络是否正常。 |
| 1003021 | 描述：重试推流超过最大的重试时间。<br />可能原因：网络信号不好，超过了最大的重试时间。<br />处理建议：检查网络状态或者切换其他网络。 |
| 1003023 | 描述：推流失败，该通道已在推流。<br />可能原因：该通道已在推流。<br />处理建议：请检查业务逻辑，避免让正在推流的通道重复推流。切换推流通道进行推流。 |
| 1003025 | 描述：推流失败，该流被后台系统配置为禁止推送。<br />可能原因：该流被后台系统配置为禁止推送。<br />处理建议：联系即构技术支持处理。 |
| 1003028 | 描述：推流失败，房间内已有相同的流。<br />可能原因：房间内已有相同的流。<br />处理建议：更换新的流 ID。调整流名生成策略，保证唯一性。 |
| 1003029 | 描述：推流失败，与 RTMP 服务器连接中断。<br />可能原因：推流地址异常，网络信号不好。<br />处理建议：请检查网络或推流地址是否异常。 |
| 1003030 | 描述：推流画面截图失败。<br />可能原因：停止预览且推流异常。 <br />处理建议：开启预览或重新推流。 |
| 1003040 | 描述：更新转推 CDN 状态失败。 <br />可能原因：转推地址 URL 不正确。 <br />处理建议：检查输入的 URL 是否有效。 |
| 1003043 | 描述：发送 SEI 失败。 <br />可能原因：data 为空。 <br />处理建议：传入非空数据。 |
| 1003044 | 描述：发送 SEI 失败。 <br />可能原因：输入 data 超过长度限制。 <br />处理建议：发送的 SEI 数据长度应小于 4096 字节。 |
| 1003045 | 描述：发送音频次要信息失败。 <br />可能原因：data 为空。 <br />处理建议：传入非空数据。 |
| 1003046 | 描述：发送音频次要信息失败。 <br />可能原因：输入 data 超过长度限制。 <br />处理建议：发送的音频次要信息数据长度应小于 1024 字节。 |
| 1003050 | 流附加信息为空 (该错误码已废弃) |
| 1003051 | 流附加信息超长，最大长度应小于 1024 字节 (该错误码已废弃) |
| 1003053 | 更新流附加信息失败，请检查网络连接 (该错误码已废弃) |
| 1003055 | 描述：设置推流水印失败。 <br />可能原因：传入的水印路径为空。 <br />处理建议：传入非空水印路径。 |
| 1003056 | 描述：设置推流水印失败。 <br />可能原因：传入的水印路径超过字节大小限制。 <br />处理建议：传入的水印路径应小于 512 字节。 |
| 1003057 | 描述：设置推流水印失败。<br />可能原因：推流水印路径输入有误或者图片格式不支持。 <br />处理建议：输入正确的水印路径，仅支持 jpg 和 png 图片格式。 |
| 1003058 | 描述：水印布局有误。<br />可能原因：水印布局区域超出编码分辨率大小。<br />处理建议：在设置水印时需要确保水印的布局区域不能超过编码分辨率的大小。 |
| 1003060 | 描述：推流加密密钥不合法。<br />可能原因：设置的加密密钥长度不支持。<br />处理建议：推流加密密钥长度需要为16/24/32字节。 |
| 1003070 | 描述：推流函数调用失败。<br />可能原因：多房间模式下，调用的推流函数有误。<br />处理建议：调用带有参数 [ZegoPublisherConfig] 的推流函数。 |
| 1003071 | 描述：推流函数调用错误。<br />可能原因：多房间模式下，推流函数的 [roomID] 不能为空。<br />处理建议：多房间模式下，推流函数参数 [roomID] 不能为空。 |
| 1003072 | 描述：推拉流鉴权错误。<br />可能原因：创建引擎时传递的[appSign]错误，或者登录房间传递的[Token]错误或超时。<br />处理建议：登录时传递正确的[Token],或收到[onRoomTokenWillExpire] 回调时及时调用[renewToken更新Token。 |
| 1003073 | 描述：指定的关闭摄像头时所推静态图片的路径错误。<br />可能原因：路径拼写错误，或者没有读权限。<br />处理建议：检查设置的图片路径是否正确，以及是否有读权限。 |
| 1003080 | 描述：不支持的视频编码器。<br />可能原因：当前 SDK 无所选编码器。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1003081 | 描述：编码失败。<br />可能原因：所选编码器编码失败。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1003099 | 描述：系统内部异常导致推流失败。<br />可能原因：系统内部异常导致推流失败。<br />处理建议：请联系技术支持解决。 |

## 1004xxx 拉流相关的错误码

调用拉流接口后，或者在拉流中途出现异常，开发者可从 `onPlayerStateUpdate` 拉流状态回调获取相关错误码，请根据以下错误码进行相应的处理。

> 处理拉流回调错误码非 0 的情况：
>
> 1. 当 state 为 PLAYER_STATE_NO_PLAY 时，且 errcode 非 0，表示拉流失败，同时不会再进行重试拉流了，此时可在界面作出拉流失败提示；
> 2. 当 state 为 PLAYER_STATE_PLAY_REQUESTING 时，且 errcode 非 0，表示重试拉流，此时如果超出重试时间未成功拉到流会抛出拉流失败通知。

| 错误码枚举值 | 说明 |
|-|-|
| 1004001 | 描述：拉流失败。<br />可能原因：可能是拉流没有数据。<br />处理建议：请检查是否推流端推流正常或尝试重新拉流，如果问题仍未解决，请联系技术支持解决。 |
| 1004002 | 描述：拉流失败。<br />可能原因：流不存在。<br />处理建议：请检查是否远端是否确实推流成功，或是否推流和拉流的环境不一致。 |
| 1004003 | 描述：拉流鉴权错误。<br />可能原因：跨APP拉流传递的[Token]错误或超时。<br />处理建议：拉流传递正确的[Token],或及时更新[Token]。 |
| 1004004 | 描述：该设备不支持超分。<br />可能原因：服务端配置该设备不支持超分，或者服务端配置拉取失败。<br />处理建议：更换设备重试或者联系 ZEGO 技术支持。 |
| 1004005 | 描述：超分的流数量超过限制。<br />可能原因：超分流数量超过限制。<br />处理建议：目前支持最多 1 路拉流超分。 |
| 1004006 | 描述：超分原始分辨率超过限制。<br />可能原因：超分原始分辨率超过 640x480。<br />处理建议：降低分辨率。 |
| 1004007 | 描述：超分设备性能不足。<br />可能原因：设备性能不足。<br />处理建议：更换设备。 |
| 1004008 | 描述：超分未初始化。<br />可能原因：超分未初始化。<br />处理建议：请初始化超分。 |
| 1004010 | 描述：拉流错误。<br />可能原因：拉流超过最大的数量。<br />处理建议：目前支持最多 12 路拉流（如有需要可联系技术支持扩容）。 |
| 1004011 | 描述：未播放该流。<br />可能原因：未播放该流。<br />处理建议：请通过 [onPlayerStateUpdate] 或  [onUserStreamStateUpdate] 回调确认流已被播放。 |
| 1004020 | 描述：拉流临时中断。<br />可能原因：网络异常。<br />处理建议：请等待或者检查网络环境。 |
| 1004025 | 描述：拉流失败。<br />可能原因：该流被后台系统配置为禁止推送。<br />处理建议：请联系技术支持解决。 |
| 1004030 | 描述：对拉流画面截图失败，请检查要截图的流是否正常。<br />可能原因：没有拉到流。<br />处理建议：检查是否开始拉流，以及拉流过程中是否出现异常。 |
| 1004060 | 描述：拉流解密密钥不合法，当前密钥长度仅支持 16/24/32 字节。<br />可能原因：输入密钥长度不为 16/24/32 字节。<br />处理建议：输入密钥长度调整为 16/24/32 字节。 |
| 1004061 | 描述：拉流解密失败，请检查解密密钥是否正确。<br />可能原因：输入的解密密钥错误。<br />处理建议：请检查解密密钥是否与推流加密密钥匹配。 |
| 1004070 | 描述：开启多房间功能后调用错误函数导致拉流失败。<br />可能原因：调用了仅对只能加入单个房间模式生效的拉流接口。<br />处理建议：请使用带 ZegoPlayerConfig 的同名函数并指定房间 ID 拉流。 |
| 1004071 | 描述：在多房间模式下，拉流的 roomID 参数不能为空。<br />可能原因：拉流的 roomID 参数为空。<br />处理建议：请输入正确的 roomID。 |
| 1004072 | 描述：当使用 SDK 拉低延迟直播流时，若您未开通低延迟直播服务，则会返回此错误码。<br />可能原因：未开通低延迟直播服务。<br />处理建议：请联系 ZEGO 技术支持人员开通低延迟直播服务。 |
| 1004080 | 描述：不支持的视频解码器。<br />可能原因：当前 SDK 无所选解码器。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1004081 | 描述：解码失败。<br />可能原因：解码失败。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1004099 | 描述：系统内部异常导致拉流失败。<br />可能原因：SDK 内部错误。<br />处理建议：请联系 ZEGO 技术支持人员。 |

## 1005xxx 混流错误

调用开始混流/停止混流过程中出现异常时，开发者可通过 API 的 callback 参数(java为匿名类，oc为block，c++为lambda)可获得相关错误码。如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1005000 | 描述：不支持使用混流服务。<br />可能原因：未配置混流服务。<br />处理建议：请在控制台或联系 ZEGO 商务人员开通。 |
| 1005001 | 描述：混流任务 ID 为空。<br />可能原因：开启混流任务时输入的混流任务 ID 为空。<br />处理建议：请输入正确的混流任务 ID。 |
| 1005002 | 描述：混流任务 ID 超长。<br />可能原因：混流任务 ID 大于 256 字节。<br />处理建议：请输入小于 256 字节的混流任务 ID。 |
| 1005003 | 描述：混流任务 ID 无效。<br />可能原因：混流任务 ID 中含有非法字符。<br />处理建议：混流任务 ID 仅支持数字，英文字符 和 '~', '!', '@', '$', '%', '^', '&', '*', '(', ')', '_', '+', '=', '-', '`', ';', ',', '.', '\<', '>', '/', '\'，请输入格式正确的混流任务 ID。 |
| 1005005 | 描述：混流任务配置存在非法参数。<br />可能原因：1. 混流任务 ID 为空；2. 混流房间 ID 为空；3. 混流自定义数据长度超过 1000 字节；4. 混流输出目标流为空。<br />处理建议：请检查混流任务配置参数。 |
| 1005006 | 描述：混流输出目标参数格式非法。<br />可能原因：混流输出目标的 target 为 streamID 时，传入了非法字符。<br />处理建议：请检查混流输出目标的 target 是否为 streamID 类型，若是，target 仅支持数字、英文字符和 "-"、"\_"。 |
| 1005010 | 描述：启动混流任务请求失败。<br />可能原因：请求过于频繁，超过服务的 qps 限制；网络异常；传入非法的混流输出 url。<br />处理建议：请确保混流请求的 qps 小于 10；检查网络；并传入合法的混流输出 url。 |
| 1005011 | 描述：停止混流任务请求失败。<br />可能原因：可能是网络错误原因。<br />处理建议：请检查网络环境。 |
| 1005012 | 该混流任务必须由该任务的启动用户执行停止操作。此错误码已废弃。 |
| 1005015 | 描述：混流任务启动过于频繁。<br />可能原因：请求过于频繁，超过服务的 qps 限制。<br />处理建议：请确保混流请求的 qps 小于 100。 |
| 1005016 | 描述：混流任务停止过于频繁。<br />可能原因：请求过于频繁，超过服务的 qps 限制。<br />处理建议：请确保停止混流请求的 qps 小于 100。 |
| 1005020 | 描述：混流任务输入流列表为空。<br />可能原因：混流任务输入流列表为空。<br />处理建议：请检查混流任务输入流列表。 |
| 1005021 | 描述：混流任务输出列表为空。<br />可能原因：混流任务输出列表为空。<br />处理建议：请检查混流任务输出列表。 |
| 1005023 | 描述：混流任务视频配置无效。<br />可能原因：混流任务视频配置无效。<br />处理建议：请检查混流任务视频配置参数有效性。 |
| 1005024 | 描述：混流任务音频配置无效。<br />可能原因：1. 使用了不支持的音频编解码格式。2. 音频码率超过 192 kbps。<br />处理建议：请检查混流任务音频配置。 |
| 1005025 | 描述：超过最大的输入流数量。<br />可能原因：最大支持 9 个输入流，可能传入超过了 9 个输入流。<br />处理建议：请检查混流任务输入流配置。 |
| 1005026 | 描述：开启混流失败。<br />可能原因：输入的流不存在。<br />处理建议：请确保输入的 streamID 对应的流正在推流中。 |
| 1005027 | 描述：开启混流失败。<br />可能原因：混流输入参数错误，可能是输入流的布局超过画布范围。<br />处理建议：请输入正确的混流参数 [ZegoMixerTask]。 |
| 1005028 | 描述：混流输入文字水印过长。<br />可能原因：混流输入参数文字水印长度超过限制。<br />处理建议：请确保输入文字水印长度不超过 512 字节。 |
| 1005029 | 描述：混流输出目标过长。<br />可能原因：混流输出目标参数长度超过限制。<br />处理建议：请确保输出目标长度小于 1024 字节。 |
| 1005030 | 描述：开启混流失败。<br />可能原因：超过最大的输出流数量。<br />处理建议：最大支持 3 个输出流。 |
| 1005031 | 描述：开启混流失败。<br />可能原因：超过最大的焦点语音输入流数量。<br />处理建议：最大支持 4 个输入流设置焦点语音。 |
| 1005032 | 描述：混流高级配置过长。<br />可能原因：混流高级配置参数长度超过限制。<br />处理建议：请确保高级配置长度不超过 512 字节。 |
| 1005033 | 描述：混流水印路径过长。<br />可能原因：混流水印路径参数长度超过限制。<br />处理建议：请确保水印路径长度不超过 512 字节。 |
| 1005034 | 描述：混流输入图片的链接过长。<br />可能原因：混流输入参数图片链接长度超出限制。<br />处理建议：请确保输入的图片链接长度不超过 1024 字节。 |
| 1005035 | 描述：混流输入图片失败。<br />可能原因：混流输入参数图片格式错误。<br />处理建议：使用 JPG 和 PNG 格式。支持 2 种使用方式：1. URI：将图片提供给 ZEGO 技术支持进行配置，配置完成后会提供图片 URI，例如：preset-id://xxx.jpg。2. URL：仅支持 HTTP 协议。 |
| 1005036 | 描述：混流输入图片失败。<br />可能原因：混流输入参数图片大小超出限制。<br />处理建议：图片大小限制在 1M 以内。 |
| 1005037 | 描述：混流输出流房间配置错误。<br />可能原因：开始混流时房间不存在。<br />处理建议：请确保配置的 targetRoom 对应的房间存在。 |
| 1005050 | 描述：开启混流失败。<br />可能原因：混流认证失败。<br />处理建议：联系 ZEGO 技术支持。 |
| 1005061 | 描述：开启混流失败。<br />可能原因：输入图片水印为空。<br />处理建议：请输入正确的水印参数 [ZegoWatermark]。 |
| 1005062 | 描述：开启混流失败。<br />可能原因：输入图片水印参数错误，可能是布局超过画布范围。<br />处理建议：请输入正确的水印参数 [ZegoWatermark]。 |
| 1005063 | 描述：开启混流失败。<br />可能原因：输入水印 URL 非法。<br />处理建议：水印 URL 必须以 "preset-id://" 开头且需要是 ".jpg" 或 ".png" 结尾。 |
| 1005066 | 描述：混流背景图片路径过长。<br />可能原因：混流背景图片路径参数长度超过限制。<br />处理建议：请确保背景图片路径长度不超过 1024 字节。 |
| 1005067 | 描述：开启混流失败。<br />可能原因：输入背景图 URL 非法。<br />处理建议：背景图 URL 必须以 "preset-id://" 开头且需要是 ".jpg" 或 ".png" 结尾。 |
| 1005068 | 描述：开启混流失败。<br />可能原因：用户自定义数据超长。<br />处理建议：输入的自定义最大长度不超过 1000 字节。 |
| 1005070 | 描述：开启混流失败。<br />可能原因：未找到自动混流服务器。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1005099 | 描述：混流内部错误。<br />可能原因：混流内部发生未知错误。<br />处理建议：请联系 ZEGO 技术支持。 |

## 1006xxx 设备错误

推流、拉流过程可能会造成使用的音视频设备出错。2.15.0 之前的版本：监听 `onDeviceError` 回调；2.15.0 及以后的版本：监听 `onLocalDeviceExceptionOccurred` 回调。相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1006001 | 描述：设备一般错误。<br />可能原因：设备无法正常工作。<br />处理建议：使用系统的录制视频或者音频的应用来检查设备是否能够正常工作。如果设备是正常的，请联系 ZEGO 技术支持。 |
| 1006002 | 描述：设备 ID 不存在。<br />可能原因：设备 ID 拼写错误，或者对应设备被插拔。<br />处理建议：请使用 SDK 的接口来获取设备 ID, 检查设备是否正确连接。 |
| 1006003 | 描述：没有设备权限。<br />可能原因：没有申请或者获得对应设备的使用权限。<br />处理建议：请检查应用是否正确申请了摄像头或麦克风的权限，以及用户是否授予了相应权限。 |
| 1006004 | 描述：设备采集帧率为 0。<br />可能原因：设备错误，或者设备没有权限。<br />处理建议：请使用系统的录制视频或者音频的应用来检查设备是否能够正常工作，请检查应用是否正确申请了摄像头或麦克风的权限，以及用户是否授予了相应权限。如果设备是正常的且应用获取了相应的设备权限，请联系 ZEGO 技术支持。 |
| 1006005 | 描述：设备被占用。<br />可能原因：设备被其他程序占用。<br />处理建议：请使用系统的录制视频或者音频的应用来检查设备是否能够正常工作，确保设备没有被其他应用占用。 |
| 1006006 | 描述：设备被拔出。<br />可能原因：设备被拔出或者没有正常连接。<br />处理建议：检查设备线路，重新连接好设备。 |
| 1006007 | 描述：设备需要重启。<br />可能原因：驱动程序更新，或者设备错误需要重启。<br />处理建议：重启设备。 |
| 1006008 | 描述：设备媒体丢失。<br />可能原因：媒体服务无法恢复。<br />处理建议：重启设备。 |
| 1006020 | 描述：释放设备列表时，列表为空。<br />可能原因：设备列表已被释放或者尚未初始化。<br />处理建议：忽略。 |
| 1006031 | 描述：设置的声浪监控间隔超出范围。<br />可能原因：设置的声浪监控间隔小于 100 毫秒，或者大于 3000 毫秒。<br />处理建议：重新设置有效的声浪监控间隔，有效的声浪监控间隔是 [100, 3000]，单位毫秒。 |
| 1006032 | 描述：设置的音频频谱监控间隔超出范围。<br />可能原因：设置的音频频谱监控间隔小于 10 毫秒。<br />处理建议：重新设置有效的音频频谱监控间隔，有效的音频频谱监控间隔必须大于 10 毫秒。 |
| 1006040 | 描述：设置摄像头变焦失败。<br />可能原因：设置的摄像头变焦倍数超出范围。<br />处理建议：设置的摄像头变焦倍数不能超过获取的最大范围，最大范围可通过 [getCameraMaxZoomFactor] 获取。 |
| 1006041 | 描述：设置摄像头曝光补偿失败。<br />可能原因：设置的摄像头曝光补偿数值超出范围。<br />处理建议：设置摄像头的曝光补偿范围在 [-1,1] 之间。 |
| 1006042 | 无效的语音检测器类型 |
| 1006099 | 描述：设备内部错误。<br />处理建议：联系 ZEGO 技术支持。 |

## 1007xxx SDK 内部预音视频处理相关的错误

在调用 SDK 内部预处理相关的接口（例如设置 SDK 自带美颜）出错时抛出的错误。

| 错误码枚举值 | 说明 |
|-|-|
| 1007001 | 描述：前处理模块未知错误。<br />处理建议：请联系 ZEGO 技术人员。 |
| 1007005 | 描述：设置美颜配置失败。<br />可能原因：传入的美颜参数有误。<br />处理建议：请检查传入的 [ZegoBeautifyOption] 类型参数。 |
| 1007006 | 混响参数为 null，请检查传入的混响参数。此错误码已废弃。 |
| 1007007 | 变声器参数为 null，请检查传入的变声器参数。此错误码已废弃。 |
| 1007011 | 描述：设置混响参数失败。<br />可能原因：混响 room size 参数无效。<br />处理建议：混响 room size 参数正常范围 0.0 ~ 1.0。 |
| 1007012 | 描述：设置混响参数失败。<br />可能原因：混响 reverberance 参数无效。<br />处理建议：混响 reverberance 参数正常范围 0.0 ~ 0.5。 |
| 1007013 | 描述：设置混响参数失败。<br />可能原因：混响 damping 参数无效。<br />处理建议：混响 damping 参数正常范围 0.0 ~ 2.0。 |
| 1007014 | 描述：设置混响参数失败。<br />可能原因：混响 dry_wet_ratio 参数无效。<br />处理建议：混响 dry_wet_ratio 参数正常范围大于 0.0。 |
| 1007015 | 描述：启动虚拟立体声失败。<br />可能原因：虚拟立体声 angle 参数无效。<br />处理建议：angle 参数正常范围 -1 ~ 360。 |
| 1007016 | 描述：设置变声参数失败。<br />可能原因：变声参数 param 设置无效。<br />处理建议：参数 param 正常范围 -12.0 ~ 12.0。 |
| 1007017 | 混响回声参数为 null，请检查传入的混响回声参数 |
| 1007018 | 描述：设置混响回声参数失败。 <br />可能原因：混响回声参数无效。 <br />处理建议：输入正确的混响回声参数 [ZegoReverbEchoParam]。 |
| 1007019 | 描述：开启或关闭电音效果失败。<br />可能原因：电音的起始音高参数 tonal 无效。<br />处理建议：电音的起始音高参数正常范围 0 ~ 11。 |
| 1007020 | 描述：开启或关闭美颜环境失败。 <br />可能原因：未在引擎启动前开启或关闭美颜环境。 <br />处理建议：请确保在引擎启动之前开启或关闭美颜环境，例如：调用（startPreview)、（startPublishingStream）、（startPlayingStream）、（createMediaPlayer）或（createAudioEffectPlayer）之前。 |
| 1007021 | 描述：开启或关闭美颜效果失败。 <br />可能原因：未启动美颜环境。 <br />处理建议：请调用 [startEffectsEnv] 先启动美颜环境。 |
| 1007022 | 描述：设置美颜参数失败。 <br />可能原因：未启动美颜环境。 <br />处理建议：请调用 [startEffectsEnv] 先启动美颜环境。 |
| 1007023 | 描述：Effects 美颜不支持当前设置的视频数据类型。 <br />可能原因：[enableCustomVideoProcessing] 接口， Windows 平台只支持 raw_data，苹果设备只支持 cv_pixel_buffer，Android 平台支持 gl_texture_2d. <br />处理建议：选择正确的视频数据类型。 |
| 1007024 | 描述：开启人声增强失败。 <br />可能原因：人声增强等级参数无效。 <br />处理建议：人声增强等级取值范围 [0, 10]，请使用正确的参数值。 |

## 1008xxx 媒体播放器错误

使用 SDK 自带的媒体播放器相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1008001 | 描述：没有创建该媒体播放器实例。 <br />可能原因：没有创建该媒体播放器实例。 <br />处理建议：使用媒体播放器之前请先创建媒体播放器实例 [createMediaPlayer]。 |
| 1008003 | 描述：媒体播放器播放失败。 <br />可能原因：未加载资源文件。 <br />处理建议：媒体播放器在开始播放 [start] 之前请先加载媒体资源 [loadResource]。 |
| 1008004 | 描述：指定的资源文件路径太长。 <br />可能原因：指定的资源文件路径太长，最大长度应小于 1024 字节。 <br />处理建议：传入小于 1024 字节的资源文件路径。 |
| 1008005 | 描述：媒体播放器载入文件失败。 <br />可能原因：文件格式不支持。 <br />处理建议：不支持该格式的文件，请使用支持格式的文件。 |
| 1008006 | 描述：媒体播放器载入文件失败。 <br />可能原因：路径不存在。 <br />处理建议：请检查媒体文件路径的有效性。 |
| 1008007 | 描述：媒体播放器载入文件失败。 <br />可能原因：解码错误。 <br />处理建议：请检查媒体文件是否已损坏或联系 ZEGO 技术支持。 |
| 1008008 | 描述：媒体播放器载入文件失败。 <br />可能原因：无可播放的音视频流。 <br />处理建议：请检查媒体文件数据是否为空。 |
| 1008009 | 描述：版权音乐资源文件已过期。 <br />可能原因：资源文件已超过有效期。<br />处理建议：请重新点歌或点伴奏。 |
| 1008010 | 描述：媒体播放器播放失败。 <br />可能原因：文件解析过程中出现错误。 <br />处理建议：请重试或联系 ZEGO 技术支持。 |
| 1008011 | 描述：版权音乐资源已过期。 <br />可能原因：版权音乐资源已过期。<br />处理建议：请重新点播此版权音乐资源。 |
| 1008012 | 描述：版权音乐资源 ID 太长。 <br />可能原因：版权音乐资源 ID 太长，最大长度应小于 512 字节。 <br />处理建议：传入小于 512 字节的版权音乐资源 ID。 |
| 1008013 | 描述：用户取消。 <br />可能原因：调用 [Load] 加载资源后，调用 [Stop] 停止播放器。 <br />处理建议：加载资源过程中调用 [Stop] 的预期行为。 |
| 1008014 | 描述：媒体播放器已启动。 <br />可能原因：已调用 [Load]。 <br />处理建议：请先调用 [Stop], 然后再调用 [Load]。 |
| 1008016 | 描述：媒体播放器 Seek 失败。 <br />可能原因：还没加载文件。 <br />处理建议：请先加载媒体文件 [loadResource] 再执行Seek [seekTo]。 |
| 1008020 | 描述：媒体播放器设置了平台不支持的视频数据格式。 <br />可能原因：媒体播放器设置了该平台不支持的视频数据格式（如 iOS 平台下 CVPixelBuffer 不支持 NV21）。 <br />处理建议：检查接口 [setVideoHandler] 设置的数据格式format是否是当前平台支持的数据格式并设置正确的数据格式。 |
| 1008030 | 描述：媒体播放器创建实例超过最大限制。 <br />可能原因：媒体播放器创建实例超过最大限制，最多可创建 4 个实例。 <br />处理建议：媒体播放器可创建实例最多为 4 个，请确保创建媒体播放器实例个数不要超过最大限制。 |
| 1008040 | 描述：媒体播放器指定音轨失败。<br />可能原因：指定的音轨不存在。<br />处理建议：检查播放文件的音轨,可调用 [getAudioTrackCount] 获取。 |
| 1008041 | 描述：媒体播放器设置变声参数无效。<br />可能原因：设置参数错误。<br />处理建议：检查设置参数 控制 -12.0 到 12.0 之间。 |
| 1008042 | 描述：takeSnapshot截图失败。<br />可能原因：视频未播放中或未调用`setPlayerCanvas`将视频显示到控件上。<br />处理建议：检查视频是否正常播放(可查看 [onPlayStart] 回调)，画面是否正常显示。 |
| 1008043 | 描述：传入参数不在合法的取值范围内。<br />可能原因：传入参数错误。<br />处理建议：请查看接口注释并传入合法取值范围内的值。 |
| 1008044 | 描述：缓存网络资源文件失败。<br />可能原因：播放过程中有 [seekTo] 操作会导致缓存失败，网络原因或者主动停止导致播放不完整也会缓存失败。<br />处理建议：检查是否有 [seekTo] 操作，是否网络原因导致播放失败，或者主动停止播放了。 |
| 1008099 | 描述：媒体播放器内部错误。<br />可能原因：内部错误。<br />处理建议：联系技术支持。 |

## 1009xxx IM 错误

发送 IM 消息过程中，开发者可从 API 的 callback(java为匿名类，oc为block，c++为lambda)参数中获取相关的错误码，如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1009001 | 描述：输入消息内容为空。<br />可能原因：消息内容为空。<br />处理建议：输入非空消息。 |
| 1009002 | 描述：输入消息内容超长。<br />可能原因：消息内容大于1024字节。<br />处理建议：消息内容控制小于1024字节。 |
| 1009003 | 描述：输入的实时有序数据超长。<br />可能原因：输入的数据长度大于 4096 字节。<br />处理建议：检查输入数据的长度，可以考虑将大数据包拆为多个小数据分多次发送。 |
| 1009005 | 描述：发送消息的目标房间与当前登录的房间不一致。<br />可能原因：发送消息的目标房间与当前登录的房间不一致。<br />处理建议：发送消息传入当前登录的房间ID。 |
| 1009010 | 描述：发送消息失败。<br />可能原因：网络原因。<br />处理建议：检查网络。 |
| 1009011 | 描述：发送自定义信令失败。<br />可能原因：输入用户 ID 为空。<br />处理建议：请输入正确的用户 ID。 |
| 1009012 | 描述：发送自定义信令失败。<br />可能原因：输入用户 ID 超长。<br />处理建议：请输入正确的用户 ID，用户 ID 最大不超过 64 个字节。 |
| 1009013 | 描述：发送消息失败。<br />可能原因：消息输入长度超出限制。<br />处理建议：检查输入内容长度或联系 ZEGO 技术支持扩容消息内容长度。 |
| 1009015 | 描述：发送房间广播消息失败。<br />可能原因：QPS 超过限制。<br />处理建议：控制最大 QPS 为 2。 |
| 1009031 | 描述：实时有序数据管理器创建实例失败。<br />可能原因：此房间 ID 已经创建了一个管理器实例。<br />处理建议：每个房间 ID 最多可创建 1 个实例。若需要创建多个实例，请使用其他房间 ID。 |
| 1009032 | 描述：找不到指定的实时有序数据管理器实例。<br />可能原因：此管理器实例尚未创建。<br />处理建议：请先调用 [createRealTimeSequentialDataManager] 函数创建管理器实例。 |
| 1009033 | 描述：找不到可用于广播的推流通道。<br />可能原因：开发者已经使用了所有推流通道。<br />处理建议：不要占满所有推流通道，检查是否有流可以停止推流，或者联系技术支持增加可用的推流通道。 |
| 1009034 | 描述：要开始广播或订阅的流 ID 不可用。<br />可能原因：这条流 ID 已在此设备上使用于音视频流业务（例如 [startPublishingStream] / [startPlayingStream]）。<br />处理建议：请使用其他流 ID 进行广播。 |
| 1009035 | 描述：重复开始广播。<br />可能原因：开发者重复调用 [startBroadcasting] 函数。<br />处理建议：要广播的流已经正在广播中，请检查业务逻辑，避免让正在广播的流重复广播。 |
| 1009036 | 描述：要停止广播的流不存在。<br />可能原因：开发者调用 [stopBroadcasting] 函数传入的流 ID 不在广播中。<br />处理建议：检查流 ID 是否正确，或是否此流 ID 不在广播中。 |
| 1009037 | 描述：要停止订阅的流不存在。<br />可能原因：开发者调用 [stopSubscribing] 函数传入的流 ID 不在订阅中。<br />处理建议：检查流 ID 是否正确，或是否此流 ID 不在订阅中。 |
| 1009038 | 描述：重复开始订阅。<br />可能原因：开发者重复调用 [startSubscribing] 函数。<br />处理建议：要订阅的流已经正在订阅中，请检查业务逻辑，避免重复订阅。 |
| 1009039 | 描述：实时有序数据发送失败。<br />可能原因：尚未开始广播，或广播遇到网络问题。<br />处理建议：检查是否已经调用 [startBroadcasting] 开始广播，或者检查网络是否畅通。 |

## 1010xxx 媒体录制相关的错误

在使用 SDK 媒体录制相关 API 的功能时，开发者可从 API 的 callback(java为匿名类，oc为block，c++为lambda)参数中获取相关的错误码，如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1010002 | 描述：文件名后缀不支持。<br />可能原因：文件名后缀不支持。<br />处理建议：目前只支持 .mp4/.aac/.flv 文件名后缀。 |
| 1010003 | 描述：录制接口通用错误。 <br />可能原因：传入的参数不合法。 <br />处理建议：检查调用传入的录制文件保存路径或录制文件格式是否合法。 |
| 1010011 | 描述：指定的录制文件路径太长。 <br />可能原因：指定的录制文件路径太长，最大长度应小于 1024 字节。 <br />处理建议：传入小于 1024 字节的录制文件路径。 |
| 1010012 | 描述：SDK 内部错误。 <br />可能原因：内部错误。 <br />处理建议：请联系 ZEGO 技术支持。 |
| 1010013 | 描述：打开文件失败。 <br />可能原因：无效的文件路径；没有文件读写权限。 <br />处理建议：传入有效的且可正常读写的文件路径。 |
| 1010014 | 描述：写文件失败。 <br />可能原因：无写入文件权限。 <br />处理建议：传入可正常读写的文件路径。 |
| 1010017 | 描述：磁盘空间不足。 <br />可能原因：磁盘空间不足。 <br />处理建议：保证有足够的磁盘空间。 |
| 1010018 | 描述：文件句柄异常。 <br />可能原因：文件句柄异常。 <br />处理建议：请联系 ZEGO 技术支持。 |
| 1010019 | 描述：输入输出异常。 <br />可能原因：输入输出异常。 <br />处理建议：请联系 ZEGO 技术支持。 |

## 1011xxx 自定义视频采集、自定义视频渲染相关的错误

在使用 SDK 自定义采集、自定义渲染相关 API 的功能时，开发者可从 API 的 callback(java为匿名类，oc为block，c++为lambda)参数中获取相关的错误码，如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1011001 | 描述：未创建自定义视频采集器。 <br />可能原因：收到 onStart 回调前创建自定义视频采集器。 <br />处理建议：确保在收到 onStart 回调后创建自定义视频采集器。 |
| 1011002 | 描述：未开启自定义视频采集模块。 <br />可能原因：初始化配置中未开启自定义视频采集。 <br />处理建议：请联系 ZEGO 技术支持，确保在初始化配置中已开启自定义视频采集。 |
| 1011003 | 描述：设置自定义采集/前处理/渲染失败。 <br />可能原因：未在引擎启动前设置自定义采集/前处理/渲染。 <br />处理建议：请确保在引擎启动之前设置自定义采集/前处理/渲染，例如：调用（startPreview)、（startPublishingStream）、（startPlayingStream）、（createMediaPlayer）或（createAudioEffectPlayer）之前。 |
| 1011004 | 描述：自定义视频前处理模块未创建。 <br />可能原因：内部视频相关模块未创建。 <br />处理建议：请先调用 [startPreview] 或 [startPublishingStream]。 |
| 1011005 | 描述：自定义视频前处理模块未开启。 <br />可能原因：自定义视频前处理模块未开启。 <br />处理建议：调用 [enableCustomVideoProcessing] 开启自定义视频前处理模块。 |
| 1011010 | 描述：当前设置的自定义视频采集格式不支持该 API。 <br />可能原因：当前设置的自定义视频采集格式不支持该 API。 <br />处理建议：请联系 ZEGO 技术支持人员。 |
| 1011011 | 描述：自定义视频渲染不支持当前设置的视频数据类型。 <br />可能原因：[enableCustomVideoRender] 中的 config 里的 buffer_type 只支持 raw_data、cv_pixel_buffer、encoded_data。对于 [enableCustomVideoProcessing] 接口， Windows 平台只支持 raw_data，苹果设备只支持 cv_pixel_buffer，Android 平台支持 gl_texture_2d 和 surface_texture. <br />处理建议：选择正确的视频数据类型。 |
| 1011012 | 描述：[customVideoCaptureSendD3DTextureData] 传入 SDK 不支持的 texture。 <br />可能原因：[customVideoCaptureSendD3DTextureData] 传入的 texture 对应的设备不是由D3D11CreateDevice(0, D3D_DRIVER_TYPE_HARDWARE, NULL, ......)创建的. <br />处理建议：[customVideoCaptureSendD3DTextureData] 传入 D3D11CreateDevice(0, D3D_DRIVER_TYPE_HARDWARE, NULL, ......) 创建的 texture。 |

## 1012xxx 自定义音频采集、自定义音频渲染相关的错误

在使用 SDK 自定义音频采集、自定义音频渲染相关 API 的功能时，开发者可从 API 的 callback(java为匿名类，oc为block，c++为lambda)参数中获取相关的错误码，如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1012001 | 描述：不支持的自定义音频采集源类型。 <br />可能原因：只有 channel_aux 支持 zego_audio_source_type_media_player 。 <br />处理建议：选择正确的自定义音频采集源类型。 |
| 1012002 | 描述：自定义音频采集功能未开启。 <br />可能原因：自定义音频采集功能未开启。 <br />处理建议：选请确保已开启指定推流通道的自定义音频 IO 功能。 |
| 1012003 | 描述：自定义音频渲染功能未开启。 <br />可能原因：自定义音频渲染功能未开启。 <br />处理建议：请确保已开启自定义音频 IO 功能。 |
| 1012004 | 描述：设置自定义音频采集和渲染失败。 <br />可能原因：设置自定义音频采集和渲染失败。 <br />处理建议：请确保在引擎启动前调用（确保在 `startPreview`/`startPublishingStream`/`startPlayingStream` 之前调用）。 |
| 1012010 | 描述：采样率参数非法。 <br />可能原因：采集和渲染混音结果录制不支持8000、22050、24000采样率。 <br />处理建议：请确认接口允许的采样率参数取值是否合法。 |

## 1013xxx 媒体推流器相关错误

使用 SDK 媒体推流器相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1013000 | 描述：没有创建该媒体推流器实例。 <br />可能原因：没有创建该媒体推流器实例。 <br />处理建议：调用 [createMediaDataPublisher] 创建媒体推流器实例。 |
| 1013001 | 描述：该错误码已废弃。 <br />可能原因：无。 <br />处理建议：无。 |
| 1013002 | 描述：该错误码已废弃。 <br />可能原因：无。 <br />处理建议：无。 |
| 1013003 | 描述：文件解码异常。<br />可能原因：无效的媒体文件。<br />处理建议：检查传入的文件是否为有效的媒体文件；检测传入的文件格式是否在媒体播放器所支持的文件格式列表中，详情可参考 /real-time-video-android-java/other-capability/media-player。 |
| 1013004 | 描述：时间戳有误。<br />可能原因：后一帧时间戳比前一帧时间戳还要小。<br />处理建议：请提供媒体文件，并联系 ZEGO 技术支持。 |
| 1013005 | 描述：媒体推流器实例超过最大限制。 <br />可能原因：媒体推流器创建实例超过最大限制。 <br />处理建议：检查创建的媒体推流器实例个数是否超过最大限制，最大允许创建实例个数为 1。 |

## 1014xxx 音效播放器相关错误

使用 SDK 音效播放器相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1014000 | 描述：没有创建音效播放器实例。 <br />可能原因：没有创建音效播放器实例。 <br />处理建议：使用音效播放器之前请先创建音效播放器实例（createAudioEffectPlayer）。 |
| 1014001 | 描述：加载音效资源失败。 <br />可能原因：无效的音效文件。 <br />处理建议：检查传入的文件格式是否在音效播放器所支持的文件格式列表中，详情可参考 /real-time-video-android-java/other/audio-effect-player。 |
| 1014002 | 描述：播放音效失败。 <br />可能原因：无效的音效文件。 <br />处理建议：检查传入的文件格式是否在音效播放器所支持的文件格式列表中，详情可参考 /real-time-video-android-java/other/audio-effect-player。 |
| 1014003 | 描述：设置音效播放进度失败。 <br />可能原因：设置的音效播放进度值超过音效文件播放时长范围。 <br />处理建议：检查传入的音效播放进度值是否在音效文件播放时长范围内。 |
| 1014004 | 描述：音效播放器创建实例超过最大限制。 <br />可能原因：音效播放器创建实例超过最大限制。 <br />处理建议：检查创建的音效播放器实例个数是否超过最大限制，最大允许创建实例个数为 1。 |

## 1015xxx 实用工具相关错误

使用 SDK 实用工具（如性能监控、网络测速等）相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1015001 | 描述：网络连通性测试失败。 <br />可能原因：未接入网络。 <br />处理建议：检查是否可以正常访问互联网。 |
| 1015002 | 描述：网络测速连接服务器失败。 <br />可能原因：未接入网络。 <br />处理建议：检查是否可以正常访问互联网。 |
| 1015003 | 描述：RTP 超时。 <br />可能原因：未接入网络。 <br />处理建议：检查是否可以正常访问互联网。 |
| 1015004 | 描述：服务端结束网络测速。 <br />可能原因：网络测速时间过长。 <br />处理建议：在 30 秒钟内结束网络测速。 |
| 1015005 | 描述：网络测速已停止。 <br />可能原因：开始推流前未主动停止网络测速。 <br />处理建议：在推流之前先结束网络测速（stopNetworkSpeedTest）。 |
| 1015006 | 描述：网络测速已停止。 <br />可能原因：开始拉流前未主动停止网络测速。 <br />处理建议：在拉流之前先结束网络测速（stopNetworkSpeedTest）。 |
| 1015009 | 描述：内部异常导致网络测速错误。 <br />可能原因：内部异常。 <br />处理建议：请联系 ZEGO 技术支持。 |
| 1015031 | 描述：无效的系统性能监控间隔时间。 <br />可能原因：设置的系统性能监控间隔超出范围。 <br />处理建议：检查系统性能监控间隔是否超出范围，有效的取值范围为 [1000, 10000]。 |
| 1015032 | 描述：登录房间导致网络测试停止。<br />可能原因：已经登录房间。<br />处理建议：由于网络测试会占用带宽，请在登录房间之前进行。 |
| 1015033 | 描述：开始转储数据失败。 <br />可能原因：音视频引擎内部错误。 <br />处理建议：请联系 ZEGO 技术支持。 |
| 1015034 | 描述：已经开始转储数据了。 <br />可能原因：重复调用 [startDumpData]。 <br />处理建议：停止或取消之前的转储数据任务。 |
| 1015035 | 描述：没有转储数据。 <br />可能原因：音视频引擎内部错误。 <br />处理建议：请联系 ZEGO 技术支持。 |
| 1015036 | 描述：创建转储数据文件夹失败。 <br />可能原因：没有读写权限或者磁盘满了。 <br />处理建议：检查磁盘是否还有空间。 |
| 1015037 | 描述：上传模块内部错误。 <br />可能原因：上传模块内部发生错误。 <br />处理建议：请联系 ZEGO 技术支持。 |
| 1015038 | 描述：转储数据超过上传的最大限制错误。 <br />可能原因：转储数据太大，超过了上传模块的限制。 <br />处理建议：请联系 ZEGO 技术支持，通过手动方式上报给 ZEGO。 |

## 1016xxx 实用工具相关错误

使用 SDK 范围语音相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1016000 | 描述：调用函数失败。<br />可能原因：没有创建范围语音实例。<br />处理建议：创建范围语音实例。 |
| 1016001 | 描述：创建范围语音失败。<br />可能原因：实例超过最大限制。<br />处理建议：使用已用的范围语音实例。 |
| 1016002 | 描述：创建范围语音失败。<br />可能原因：多房间模式下无法使用范围语音。<br />处理建议：设置单方间模式。 |
| 1016003 | 描述：设置队伍 ID 失败。<br />可能原因：输入的队伍 ID 超过最大限制。<br />处理建议：输入的小于 64 个字节的字符串。 |
| 1016004 | 描述：设置队伍 ID 失败。<br />可能原因：输入的队伍 ID 包含非法字符。<br />处理建议：仅支持数字，英文字符 和 '~', '!', '@', '#', '$', '', '^', '&', '*', '(', ')', '_', '+', '=', '-', ', ';', ',', '.', '\<', '>', '/', ''。 |

## 1017xxx 版权音乐相关错误

使用 SDK 版权音乐相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1017000 | 描述：command 参数无效。<br />可能原因：输入的 command 参数为空。<br />处理建议：请传入正确的 command 参数, 详见 https://doc-zh.zego.im/online-ktv-android/client-api/send-extended-request#1。 |
| 1017001 | 描述：params 参数无效。<br />可能原因：输入的 params 参数为空。<br />处理建议：请传入正确的 params 参数。 |
| 1017002 | 描述：song_id 参数无效。<br />可能原因：输入的 song_id 参数为空。<br />处理建议：请传入正确的 song_id 参数, 详见 https://doc-zh.zego.im/online-ktv-android/client-api/send-extended-request#1。 |
| 1017003 | 描述：share_token 参数无效。<br />可能原因：输入的 share_token 参数为空。<br />处理建议：请传入正确的 share_token 参数，share_token 可以通过请求资源 [requestResource] 获取。 |
| 1017004 | 描述：resource_id 参数无效。<br />可能原因：输入的 resource_id 参数为空。<br />处理建议：请传入正确的 resource_id 参数，resource_id 可以通过点歌/点伴奏 [requestResource] [getSharedResource] 获取。 |
| 1017005 | 描述：start_position 参数无效。<br />可能原因：输入的 start_position 参数无效。<br />处理建议：请传入数值为正的 start_position 参数， 范围[0, 歌曲时长]。 |
| 1017006 | 描述：position 参数无效。<br />可能原因：输入的 position 参数无效。<br />处理建议：请传入数值为正的 position 参数， 范围[0, 歌曲时长]。 |
| 1017007 | 描述：volume 参数无效。<br />可能原因：输入的 Volume 参数无效。<br />处理建议：请传入正确的 Volume 参数, 范围[0, 200]。 |
| 1017008 | 描述：krcToken 参数无效。<br />可能原因：输入的 krcToken 参数为空。<br />处理建议：请传入正确的 krcToken 参数，krcToken 可以通过获取资源 [requestResource] [getSharedResource] 获取 。 |
| 1017009 | 描述：版权音乐初始化鉴权失败。<br />可能原因：未设置 AppSign 或者 Token。<br />处理建议：在使用 Token 鉴权时，在调用 [initCopyrightedMusic] 前调用 [loginRoom]，或者使用 AppSign 鉴权。 |
| 1017010 | 描述：请求版权服务失败。<br />可能原因：输入参数错误或网络原因。<br />处理建议：请传入正确的调用参数并重试。 |
| 1017011 | 描述：本地磁盘空间不足。<br />可能原因：本地磁盘空间不足。<br />处理建议：请清理本地文件确保有足够磁盘空间。 |
| 1017012 | 描述：正在下载中。<br />可能原因：正在下载相同的资源。<br />处理建议：请等待资源下载成功。 |
| 1017013 | 描述：资源文件丢失。<br />可能原因：资源文件以被删除。<br />处理建议：请重新加载资源文件。 |
| 1017014 | 描述：过期的资源文件。<br />可能原因：资源文件已超过有效期。<br />处理建议：请重新点歌或点伴奏。 |
| 1017015 | 描述：无效的资源文件。<br />可能原因：文件已损坏。<br />处理建议：请调用 [download] 重新下载资源文件。 |
| 1017016 | 描述：下载被取消。<br />可能原因：调用 [cancelDownload] 主动取消下载。 |
| 1017018 | 描述：资源 ID 未授权。<br />可能原因：该资源 ID 不是通过 [requestResource]、[getSharedResource] 接口获取的。<br />处理建议：请先调用 [requestResource]、[getSharedResource] 接口点歌获取有效的资源 ID。 |
| 1017019 | 描述：版权资源已过期。<br />可能原因：版权资源已过期。<br />处理建议：请重新点播此版权资源。 |
| 1017020 | 描述：该资源不支持此方法。<br />可能原因：资源 ID 传入错误。<br />处理建议：请传入正确的资源 ID。 |
| 1017030 | 描述：音乐无版权，无法收听和点唱歌曲。<br />可能原因：音乐无版权。<br />处理建议：请选择有版权的音乐。 |
| 1017031 | 描述：音乐无伴奏权限，只能收听歌曲，无法点唱。<br />可能原因：音乐无伴奏权限。<br />处理建议：请选择有词曲权限的音乐。 |
| 1017032 | 描述：非包月会员。<br />可能原因：未开通按包月会员。<br />处理建议：请开通按用户包月会员模式或使用按次计费模式点歌。 |
| 1017033 | 描述：没有伴奏资源。<br />可能原因：该歌曲没有伴奏资源。<br />处理建议：请选择有伴奏资源的歌曲。 |
| 1017034 | 描述：找不到资源<br />可能原因：找不到资源<br />处理建议：请选择其他歌曲。 |
| 1017040 | 描述：参数非法。<br />可能原因：传入的参数不正确。<br />处理建议：请输入正确的参数。 |
| 1017041 | 描述：AppID 不可用。<br />可能原因：当前 AppID 不支持版权音乐功能。<br />处理建议：请联系 ZEGO 技术支持。 |
| 1017042 | 描述：不支持的计费模式。<br />可能原因：不支持的计费模式。<br />处理建议：请选择正确的计费模式。 |
| 1017043 | 描述：不合理的访问。<br />可能原因：包月会员按次计费点歌。<br />处理建议：请选择正确的计费模式。 |
| 1017044 | 描述：分享 token 过期。<br />可能原因：分享 token 过期。<br />处理建议：请选择未过期分享 token 获取资源。 |
| 1017045 | 描述：分享 token 非法。<br />可能原因：分享 token 非法。<br />处理建议：请选择正确分享 token 获取资源。 |
| 1017046 | 描述：krcToken 非法。<br />可能原因：krcToken 非法。<br />处理建议：请选择正确 krcToken 获取 krc 格式歌词。 |
| 1017047 | 描述：krcToken 过期。<br />可能原因：krcToken 过期。<br />处理建议：请选择未过期 krcToken 获取 krc 格式歌词。 |
| 1017048 | 描述：获取歌词失败。<br />可能原因：找不到歌词。<br />处理建议：请稍后重试。 |
| 1017049 | 描述：获取音高线失败。<br />可能原因：找不到音高线或者资源已下架。<br />处理建议：请选择正确的歌曲 。 |
| 1017050 | 描述：房间内未分享该资源。<br />可能原因：房间内无用户分享该资源。<br />处理建议：请房间内任一用户调用 [requestResource] 接口请求资源并进行分享。 |
| 1017051 | 描述：该资源在房间内的免费获取次数用尽。<br />可能原因：1.自己分享的资源无法再次获取；2.已经获取过共享资源。<br />处理建议：请使用已经获取的资源，或者使用 [requestResource] 重新分享资源。 |
| 1017052 | 描述：该版权方不可用。<br />可能原因：没有开通对应版权方。<br />处理建议：请传入正确的版权方 ID。 |
| 1017053 | 描述：该版权方不支持此方法。<br />可能原因：版权方 ID 传入错误。<br />处理建议：请传入正确的版权方 ID。 |
| 1017054 | 描述：该房间 ID 未登录。<br />可能原因：房间 ID 传入错误。<br />处理建议：如果不是在多房间场景下使用版权，则不需要传入roomID，否则请传入正确的已登录的房间 ID。 |
| 1017071 | 描述：无效的版权商 ID。<br />可能原因：版权方 ID 传入错误。<br />处理建议：请传入正确的版权方 ID。 |
| 1017072 | 描述：该版权方不支持此 song ID。<br />可能原因：版权方曲库不存在此 song ID<br />处理建议：请传入正确的 song ID |
| 1017073 | 描述：无效的 master ID。<br />可能原因：当选择按房主计费时，没有传入master ID<br />处理建议：请传入正确的 master ID |
| 1017074 | 描述：多版权搜索时 page 参数无效。<br />可能原因：第一次调用多版权搜索时， page 参数必须为1<br />处理建议：请传入正确的 page |
| 1017075 | 描述：资源没有音高线。<br />可能原因：资源没有音高线<br />处理建议：请传入正确的 song ID |
| 1017076 | 描述：不支持的 scene ID。<br />可能原因：不支持的 scene ID<br />处理建议：请传入已经开通场景对应的 scene ID。 |
| 1017077 | 描述：不支持的 top ID。<br />可能原因：获取榜单歌曲时传入了不支持的 top ID<br />处理建议：请传入正确的 top ID。 |
| 1017094 | 描述：加载版权音乐插件失败。<br />可能原因：程序运行路径中没有包含版权音乐插件动态库。<br />处理建议：请正确配置版权音乐插件动态库依赖。 |
| 1017095 | 描述：版权音乐模块不支持此功能。<br />可能原因：版权音乐模块在当前平台下不支持此功能。<br />处理建议：请联系 ZEGO 技术支持处理。 |
| 1017096 | 描述：版权音乐模块未初始化。<br />可能原因：没有调用 [initCopyrightedMusic] 方法初始化版权模块。<br />处理建议：请先调用 [initCopyrightedMusic] 方法。 |
| 1017097 | 描述：系统繁忙。<br />可能原因：系统繁忙。<br />处理建议：请进行重试。 |
| 1017098 | 描述：网络异常导致失败。<br />可能原因：内部未知错误。<br />处理建议：请联系 ZEGO 技术支持处理。 |
| 1017099 | 描述：系统内部异常导致失败。<br />可能原因：内部未知错误。<br />处理建议：请联系 ZEGO 技术支持处理。 |

## 1018xxx 语音检测相关错误

使用 SDK 语音检测相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1018000 | 描述：SDK 内部返回空指针。 |
| 1018001 | 描述：调用函数失败。<br />可能原因：没有创建检测音频包类实例。<br />处理建议：创建检测音频包类实例。 |

## 1019xxx 范围场景相关错误

使用 SDK 范围场景相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1019000 | 描述：调用接口失败。<br />可能原因：参数非法或SDK 未包含 RangeScene 模块。<br />处理建议：请确认 SDK 是否包含 RangeScene 模块并检查参数。 |
| 1019001 | 描述：没有创建该范围场景实例。 <br />可能原因：没有创建该范围场景实例。 <br />处理建议：使用范围场景之前请先创建范围场景实例 [createRangeScene]。 |
| 1019002 | 描述：没有登录场景。 <br />可能原因：没有登录场景。 <br />处理建议：请调用 [loginScene] 登录场景。 |
| 1019003 | 描述：使用不支持的功能。 <br />可能原因：未开通状态同步功能时使用状态同步接口。 <br />处理建议：若要使用状态同步接口，请开通状态同步功能。 |
| 1019004 | 描述：重试登录场景超过最大的重试时间。<br />可能原因：网络原因。<br />处理建议：请检查网络是否正常或切换网络环境。 |
| 1019005 | 描述：场景连接临时中断。<br />可能原因：网络原因。<br />处理建议：请稍等或检查网络是否正常。 |
| 1019006 | Token 校验失败。<br />可能原因：生成 Token 时传入参数与 SDK 使用不一致。<br />处理建议：请使用重新生成的正确 Token。 |
| 1019007 | 描述：Token 过期。<br />可能原因：Token 过期，或者生成 Token 的有效期参数类型错误。<br />处理建议：重新生成 Token。 |
| 1019020 | 描述：坐标超出范围。<br />可能原因：坐标超出场景范围。<br />处理建议：请传入正确的坐标。 |
| 1019021 | 描述：物品已经被创建。<br />可能原因：物品已经被创建。<br />处理建议：物品已经被创建，无需再次创建物品。 |
| 1019022 | 描述：物品绑定用户超过最大限制。<br />可能原因：物品绑定用户超过最大限制。<br />处理建议：物品绑定用户超过最大限制，请稍后重试。 |
| 1019023 | 描述：物品不存在。<br />可能原因：物品不存在。<br />处理建议：请先创建物品。 |
| 1019024 | 描述：物品未绑定。<br />可能原因：物品未绑定。<br />处理建议：请先绑定物品。 |
| 1019025 | 描述：物品已被他人操作。<br />可能原因：物品已被他人操作。<br />处理建议：请稍后重试。 |
| 1019026 | 描述：物品绑定容量超过最大限制。<br />可能原因：物品绑定容量超过最大限制。<br />处理建议：请使用不超过绑定最大容量限制的容量创建物品。 |
| 1019027 | 描述：用户绑定物品达到最大限制。<br />可能原因：用户绑定物品达到最大限制。<br />处理建议：请先解绑一些暂时不需要操作的物品。 |
| 1019028 | 描述：物品超出用户视野范围。<br />可能原因：物品超出用户视野范围。<br />处理建议：请在用户视野范围内操作物品。 |
| 1019030 | 描述：范围场景创建实例超过最大限制。 <br />可能原因：范围场景创建实例超过最大限制，最多可创建 1 个实例。 <br />处理建议：范围场景可创建实例最多为 1 个，请确保创建范围场景实例个数不要超过最大限制。 |
| 1019031 | 描述：范围场景加入小队超过最大限制。 <br />可能原因：范围场景加入小队超过最大限制，默认允许加入 5 个小队。 <br />处理建议：范围场景默认最多允许加入 5 个小队，请确保加入小队个数不要超过最大限制。 |
| 1019032 | 描述：小队 ID 被占用。 <br />可能原因：范围场景加入小队时提供了的小队 ID 被占用。 <br />处理建议：提供新的小队 ID 加入小队。 |
| 1019033 | 描述：小队 ID 不存在。 <br />可能原因：范围场景离开小队时提供了错误的小队 ID。 <br />处理建议：离开小队时提供正确的小队 ID。 |
| 1019099 | 描述：系统内部异常导致失败。<br />可能原因：内部未知错误。<br />处理建议：请联系 ZEGO 技术支持处理。 |

## 1020xxx 屏幕采集相关错误

使用 SDK 屏幕采集相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1020000 | 描述：启动屏幕采集失败。<br />可能原因：用户拒绝授予应用屏幕采集权限。<br />处理建议：允许应用采集屏幕权限。 |
| 1020001 | 描述：启动屏幕采集失败。<br />可能原因：当前系统版本不支持屏幕采集。<br />处理建议：使用 Android 5（API 级别 21）以上的系统版本。 |
| 1020002 | 描述：启动屏幕采集失败。<br />可能原因：无法屏幕共享模块。<br />处理建议：请引入屏幕共享模块资源，或联系技术支持。 |
| 1020003 | 描述：调用函数失败。<br />可能原因：没有创建屏幕采集源实例。<br />处理建议：创建屏幕采集源实例。 |
| 1020004 | 描述：创建屏幕采集源失败。<br />可能原因：实例超过最大限制。<br />处理建议：使用已有的屏幕采集实例或销毁之前的实例。 |
| 1020005 | 描述：系统内部异常导致失败。<br />可能原因：内部未知错误。<br />处理建议：请联系 ZEGO 技术支持处理。 |

## 1021xxx AI 变声器相关错误

使用 SDK AI 变声器相关 API 过程相关的错误码如下：

| 错误码枚举值 | 说明 |
|-|-|
| 1021000 | 描述：调用函数失败。<br />可能原因：没有创建 AI 变声器实例。<br />处理建议：创建 AI 变声器实例。 |
| 1021001 | 描述：创建 AI 变声器实例失败。<br />可能原因：实例超过最大限制。<br />处理建议：使用已有的 AI 变声器实例或销毁之前的实例。 |
| 1021002 | 描述：系统内部异常导致失败。<br />可能原因：内部未知错误。<br />处理建议：请联系 ZEGO 技术支持处理。 |
| 1021003 | 描述：未初始化 AI 变声器。<br />可能原因：使用 AI 变声器前，未对其进行初始化。<br />处理建议：请初始化 AI 变声器后再使用。 |
| 1021004 | 描述：初始化 AI 变声器失败。<br />可能原因：使用前未设置用户 ID 或 AI 变声服务到期导致初始化失败。<br />处理建议：请在设置用户 ID 后使用或联系 ZEGO 技术支持处理。 |
| 1021005 | 描述：资源文件未下载。<br />可能原因：没有下载资源文件。<br />处理建议：使用 AI 变声功能前，请确保资源文件已正常下载。 |
| 1021006 | 描述：下载资源文件失败。<br />可能原因：网络连接不稳定。<br />处理建议：请检测网络连接是否正常。 |
| 1021007 | 描述：设备不支持运行 AI 变声功能。<br />可能原因：由于设备性能较差，不支持运行 AI 变声功能。<br />处理建议：当前设备不开启 AI 变声功能。 |
