{"mySidebar": [{"type": "category", "label": "产品简介", "collapsed": false, "items": [{"type": "doc", "label": "概述", "id": "introduction/overview", "articleID": 5424}, {"type": "doc", "label": "产品功能", "id": "introduction/product-feature-list", "articleID": 16678}, {"type": "category", "label": "计费说明", "collapsed": false, "items": [{"type": "doc", "label": "实时音视频价格说明", "id": "introduction/pricing/rtc", "articleID": 11438}, {"type": "doc", "label": "服务端混流价格说明", "id": "introduction/pricing/server-side-stream-mixing", "articleID": 11457}, {"type": "doc", "label": "CDN 直播价格说明", "id": "introduction/pricing/cdn-live-streaming", "articleID": 14101}]}, {"type": "doc", "label": "实时音视频 SDK 与实时语音 SDK 差异", "id": "introduction/difference-between-audio-and-video-sdk", "articleID": 15786}]}, {"type": "category", "label": "客户端 SDK", "collapsed": false, "items": [{"type": "doc", "label": "下载", "id": "client-sdk/download-sdk", "articleID": 5570}, {"type": "doc", "label": "发布日志", "id": "client-sdk/release-notes", "articleID": 12552}, {"type": "category", "label": "升级指南", "collapsed": false, "items": [{"type": "doc", "label": "2.23.0 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v2-23", "articleID": 16119}, {"type": "doc", "label": "3.0.0 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3", "articleID": 16116}, {"type": "doc", "label": "3.8.1 及以上版本升级指南", "id": "client-sdk/upgrade-guide/upgrade-to-v3-81", "articleID": 18180}]}, {"type": "doc", "label": "常见错误码", "id": "client-sdk/error-code", "articleID": 5641}]}, {"type": "category", "label": "快速开始", "collapsed": false, "items": [{"type": "doc", "label": "跑通示例源码", "id": "quick-start/run-example-code", "articleID": 3130}, {"type": "doc", "label": "集成 SDK", "id": "quick-start/integrating-sdk", "articleID": 1241}, {"type": "doc", "label": "实现视频通话", "id": "quick-start/implementing-video-call", "articleID": 7634}, {"type": "doc", "label": "场景化音视频配置", "id": "quick-start/scenario-based-audio-video-configuration", "articleID": 16870}]}, {"type": "category", "label": "通信能力", "collapsed": false, "items": [{"type": "doc", "label": "使用 Token 鉴权", "id": "communication/using-token-authentication", "articleID": 14350, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话前检测", "id": "communication/pre-call-detection", "articleID": 15473, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通话质量监测", "id": "communication/monitor-stream-quality", "articleID": 10910, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "网络测速", "id": "communication/testing-network", "articleID": 10908, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "多源采集", "id": "communication/multi-source-capture", "articleID": 17126, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "媒体补充增强信息（SEI）", "id": "communication/sei", "articleID": 10909, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "云代理", "id": "communication/cloud-proxy", "articleID": 17125, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "地理围栏", "id": "communication/geofencing", "articleID": 17814, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "音视频流加密", "id": "communication/encrypt-streams", "articleID": 15477, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "游戏语音", "id": "communication/range-audio", "articleID": 15214, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "房间能力", "collapsed": false, "items": [{"type": "doc", "label": "房间连接状态说明", "id": "room/room-connection-status", "articleID": 15474, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "实时消息与信令", "id": "room/messaging-and-signaling", "articleID": 10906, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "登录多房间", "id": "room/multi-room-login", "articleID": 15475, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "音频能力", "collapsed": false, "items": [{"type": "doc", "label": "音量变化与音频频谱", "id": "audio/sound-level-spectrum", "articleID": 10920, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "耳返与声道设置", "id": "audio/headphone-monitor", "articleID": 10921, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音频 3A 处理", "id": "audio/audio-3a-processing", "articleID": 15213, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "变声/混响/立体声", "id": "audio/audio-effects", "articleID": 10922, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "场景化 AI 降噪", "id": "audio/scenario-based-ai-noise-reduction", "articleID": 16877, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义音频采集与渲染", "id": "audio/custom-audio-capture-and-rendering", "articleID": 17137, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "AI 变声", "id": "audio/ai-voice-changer", "articleID": 18580, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "视频能力", "collapsed": false, "items": [{"type": "doc", "label": "常用视频配置", "id": "video/common-video-configuration", "articleID": 14368, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "视频画面旋转", "id": "video/video-rotation", "articleID": 11783, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "视频采集旋转", "id": "video/video-capture-rotation", "articleID": 16395, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "屏幕共享", "id": "video/screen-sharing", "articleID": 17127, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "水印和截图", "id": "video/watermark-and-screenshot", "articleID": 10901, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "设置视频编码方式", "id": "video/set-video-encoding", "articleID": 10915, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频采集", "id": "video/custom-video-capture", "articleID": 6061, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频渲染", "id": "video/custom-video-rendering", "articleID": 21456, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "自定义视频前处理", "id": "video/custom-video-preprocessing", "articleID": 21457, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "超分辨率", "id": "video/super-resolution", "articleID": 16869, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "主体分割", "id": "video/object-segmentation", "articleID": 17917, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "H.265", "id": "video/h265", "articleID": 15212, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "视频大小流和分层编码", "id": "video/small-large-video-stream-and-layered-encoding", "articleID": 18030, "tag": {"label": "进阶", "color": "Warning"}}, {"type": "doc", "label": "推流视频增强", "id": "video/publish-video-enhancement", "articleID": 18928, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "直播能力", "collapsed": false, "items": [{"type": "doc", "label": "混流", "id": "live-streaming/stream-mixing", "articleID": 10905, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "使用 CDN 直播", "id": "live-streaming/using-cdn-for-live-streaming", "articleID": 10912, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "CDN 推流鉴权", "id": "live-streaming/cdn-stream-publishing-authentication", "articleID": 15823, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "通过 URL 拉流", "id": "live-streaming/playing-stream-by-url", "articleID": 10911, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "超低延迟直播", "id": "live-streaming/low-latency-live-streaming", "articleID": 10914, "tag": {"label": "特色", "color": "Error"}}, {"type": "doc", "label": "单流转码", "id": "live-streaming/single-stream-transcoding", "articleID": 18303, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "其他能力", "collapsed": false, "items": [{"type": "doc", "label": "媒体播放器", "id": "other/media-player", "articleID": 10902, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音效文件播放器", "id": "other/audio-effect-player", "articleID": 10903, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "音视频录制", "id": "other/local-media-recording", "articleID": 10904, "tag": {"label": "基础", "color": "Check"}}, {"type": "doc", "label": "将白板推送到第三方平台", "id": "other/push-the-whiteboard", "articleID": 18196, "tag": {"label": "进阶", "color": "Warning"}}]}, {"type": "category", "label": "最佳实践", "collapsed": false, "items": [{"type": "doc", "label": "多人视频通话", "id": "best-practice/multiplayer-video-call", "articleID": 15215}, {"type": "doc", "label": "调试与配置", "id": "best-practice/debug-and-config", "articleID": 15478}, {"type": "doc", "label": "限制说明", "id": "best-practice/restrictions", "articleID": 15216}]}, {"type": "category", "label": "API 参考文档", "collapsed": false, "items": [{"type": "link", "label": "客户端 API", "href": "https://doc-zh.zego.im/article/19494", "articleID": 19494}, {"type": "link", "label": "服务端 API", "href": "/real-time-video-server/overview", "articleID": 19455}]}, {"type": "link", "label": "常见问题", "href": "https://doc-zh.zego.im/faq/?product=ExpressVideo&platform=flutter"}]}