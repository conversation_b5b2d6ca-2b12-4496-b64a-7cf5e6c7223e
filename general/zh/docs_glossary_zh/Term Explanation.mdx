# 术语说明


## A

#### AEC

AEC（Acoustic Echo Cancelling）指回声消除，ZEGO Express SDK 支持对采集到的音频数据进行过滤，以减少音频中的回声。

#### AGC

AGC（Automatic Gain Control）指自动增益控制，ZEGO Express SDK 能够自动调节麦克风音量，适应远近拾音，保持音量稳定。

#### [AI 视觉](https://doc-zh.zego.im/article/9556)

ZEGO 基于领先的 AI 算法，提供多项智能图像渲染和算法能力，包括智能美颜、AR 特效、图像分割等，可广泛应用于娱乐直播、在线教育、拍照工具等多种场景。

#### ANS

ANS（Active Noise Control）指降噪，ZEGO Express SDK 支持识别声音中的背景噪声并进行消除，使人声更加清晰。

#### AOI

AOI （Area of Interest）是指感兴趣的区域或范围。在 ZEGO 的虚拟场景中一般指用户的可视范围，该范围实时跟随用户的位置，仅接收 AOI 范围内远端用户的音视频信息、网络状态、设备状态等。

#### AppID

AppID 是 [ZEGO 控制台](https://console.zego.im/) 为每个项目签发的应用 ID，是每个项目的唯一标识。

#### AppSign

AppSign 是 [ZEGO 控制台](https://console.zego.im/) 为每个项目签发的鉴权密钥，用于校验对应 AppID 的合法性。

#### API

API（Application Programming Interface，应用程序接口）是一些预先定义的接口，比如函数、HTTP 接口等。

ZEGO 提供的 API 包含 `客户端 API` 和 `服务端 API`，开发者可通过调用接口实现各种功能，详情请参考 [ZEGO API 中心](https://doc-zh.zego.im/api-center)。

#### ARQ

ARQ（Automatic Repeat-reQuest，自动重传请求）是 OSI 模型中数据链路层和传输层的错误纠正协议之一。它通过使用确认和超时这两个机制，在不可靠服务的基础上实现可靠的信息传输。



## B

#### 补充增强信息

补充增强信息（Supplemental Enhancement Information，SEI）是码流范畴里面的概念，提供了向视频码流中加入信息的办法，是 H.264/H.265 视频压缩标准的特性之一。


#### BGP

BGP（Border Gateway Protocol，边界网关协议）是互联网上一个核心的去中心化自治路由协议。BGP 系统的主要功能是和其他的 BGP 系统交换网络可达信息。


#### 变声

变声是指通过改变输入的音调，使输出的声音在感官上与原始声音不同，可实现男声变女声等多种效果。


## C

#### CDN

CDN（Content Delivery Network 或 Content Distribution Network，内容分发网络）是指一种透过互联网互相连接的电脑网络系统，利用最靠近每位用户的服务器，更快、更可靠地将音乐、图片、视频、应用程序及其他文件分发给大量用户。

#### CDN 推流

ZEGO Express SDK 支持推流到 CDN（Content Delivery Network，内容分发网络），包括转推 CDN 和直推 CDN 两种功能。开发者基于该功能可打通 RTC 产品和 CDN 直播产品，方便用户从网页或第三方播放器直接观看和收听直播内容。

#### CDN 拉流
CDN 拉流：通过传统 CDN 直播拉流，一般使用 RTMP、HLS 等 TCP 协议，直播观众端延迟一般在 3s ～ 10s+。


#### 场景化音视频

为方便开发者快速接入，降低开发者接入门槛，ZEGO Express SDK 通过大量线上数据验证，沉淀出多种场景化配置方案。开发者可根据所需场景，选择对应的房间模式，SDK 将自动应用适合该场景的音视频编解码器、音视频参数、流控策略等配置，从而快速实现该场景下的最佳效果。

#### [超低延迟直播](https://doc-zh.zego.im/article/13182)

ZEGO 超低延迟直播（Low-Latency Live Streaming，L3）为高质量体验而生，可打造超低延迟、超强同步、抗极端弱网、超低卡顿、超清画质、首帧秒开的极致直播体验，是直播分发产品中的“六边形战士”。

#### 超分

超分辨率（简称超分）功能可以在拉流端，对拉取到的视频流画面的宽和高的像素进行倍增，从而得到更好的画质。

例如：拉流端拉取到的原始画面分辨率为 640p x 360p，对画面进行超分处理后分辨率将提升为 1280p x 720p。

## D

#### 大小流

视频大小流编码（H.264 DualStream）参考了分层视频编码（H.264 SVC）的理念，通过对视频码流分层，保障不同网络和设备性能的终端流畅的体验。

两者的区别，请参考 [视频大小流和分层编码](https://doc-zh.zego.im/article/17942)。

#### 单流录制

云端录制的一种模式，指分别录制房间内每条音视频流、白板，每条音视频流都会生成对应的音视频文件，所有白板会生成一个视频文件。

#### DAU

DAU（Daily Active User），日活跃用户，简称日活，通常是指一天之内使用某产品的独立用户数。

例如，单个用户登录即时通讯（ZIM SDK），则计为 1 个 DAU。当日内，如果该用户更换设备登录、重复登录，都会记为同一个 DAU。DAU 从每月 1 号开始统计，不足一个月的（例如在当月 15 号开启/关闭服务）都将按照一整个月计费。

#### 地理围栏

地理围栏指将音视频及信令数据传输限定在某一区域，用以满足地区数据隐私安全相关法规，即限定访问某一特定区域的音视频服务。例如，当指定的地理围栏区域为欧洲时，不区分 App 用户所在区域，SDK 实际访问的区域将为欧洲。

#### 丢包

丢包是指一个或多个数据包无法通过网络到达目的地，丢包严重时可能影响音视频的质量。

#### 抖动

经网络传输后，接收端收到的数据包之间延迟有所差异，也就是说数据包的延时忽大忽小，将这种现象称之为抖动。

#### 多端登录

用户同一个账号可在多个平台上同时登录，满足用户的会话、消息、群组等数据互通。ZIM SDK 支持配置自定义多端登录。

## E

#### 耳返

耳返是指利用耳机监听本侧采集设备的声音。

## F

#### 范围语音

ZEGO Express SDK 提供的 “游戏语音” 功能的其中一种场景，即房间内的收听者对音频的接收距离有范围限制。若发声者与自己的距离超过该范围，则无法听到声音。为保证语音清晰，附近超过 20 人发声时，只能听到离自己最近的 20 个发声者的声音。

#### 房间

在不同 SDK 中，“房间” 代表的含义不同：

- Express SDK：“房间” 在 Express SDK 中，可以理解为音视频中 “频道” 的概念是指 ZEGO 提供的音视频空间服务，用于组织用户群，同一房间内的用户可以互相收发实时音视频及消息。实时音视频的所有功能都是以 “房间” 为载体，登录即登录房间，因此以 `loginRoom` 接口登录作为开启音视频功能的第一步。

- ZIM SDK：“房间” 在 ZIM SDK 中，可以理解为 “聊天室” 的概念。ZIM 为开发者提供全平台互动、海量并发、超低延时、消息必达的通信服务，除了房间还有单聊、群聊消息等其他模块，相对实时音视频更多一层 “账号” 概念，所以 `login` 接口登录作为开启 ZIM 服务的第一步。

两个产品的业务侧重点有所不同，请开发者注意区分。


#### FEC

FEC（forward error correction，前向纠错）是一种差错控制方式，它是指信号在被送入传输信道之前预先按一定的算法进行编码处理，加入带有信号本身特征的冗码，在接收端按照相应算法对接收到的信号进行解码，从而找出在传输过程中产生的错误码并将其纠正的技术。该技术提高信号传输的可靠性。

#### 分辨率

- 视频分辨率，是用于度量图像内数据量多少的一个参数，通常表示成 ppi。

- 采集分辨率，是指摄像头等采集设备提供的画面分辨率。

- 编码分辨率，是指经过编码处理的画面的分辨率。

#### 分层视频编码

分层视频编码（Scalable Video Coding，SVC）即可伸缩视频编码标准，是 H.264/AVC 标准的可伸缩性扩展档次（ITU-T and ISO/IEC JTC,2007），它可以根据需求将视频流分割为一个基础层和多个增强层，基础层为用户提供最基本的视频质量、帧率和分辨率，而增强层则对视频质量进行完善。

#### FLV

FLV（Flash Video），是一种网络视频格式，用作流媒体格式，CDN 直播中会通过 HTTP 协议传输 FLV 视频格式的数据给客户端，称作 HTTP-FLV。


## H

#### H.264/H.265

视频编码格式，通过特定的压缩技术，将原始视频格式的文件转换成另一种视频格式的方式，便于传输和存储。

- H.264：一种高级视频编码，是一种基于运动补偿的视频编码标准。到 2014 年，它已经成为高精度视频录制、压缩和发布的最常用格式之一。
- H.265：一种高效的视频编码标准，旨在有限带宽下传输更高质量的网络视频。


#### HLS

HLS（HTTP Live Streaming）是由苹果公司提出基于 HTTP 的流媒体网络传输协议。

#### 互动连麦

连麦的概念出现在直播房间场景下，包括视频连麦和语音连麦，是房间内用户之间互动的一种形式。通过 `startPublishingStream` 接口推自己流的同时，也调用 `startPlayingStream` 接口拉对方的流，两个用户连麦成功之后即可进行互动通话。

#### 呼叫邀请

ZIM SDK 提供了呼叫邀请功能，支持主叫向被叫（可为离线状态）发送呼叫邀请、被叫（可为离线状态）接受或拒绝邀请等完整的业务流程控制能力。呼叫邀请分为两种模式，普通模式与进阶模式。

#### 回声消除

回声是指扬声器播放出来的声音被麦克风拾取后发回远端，使远端谈话者能听到自己的声音。

回声消除是指采用回波抵消法，估计回波信号的大小，然后在接收信号中减去此估计值以抵消回波。

#### 混流

混流是把多路音视频流从云端合并成一路流的技术，也称合流。

#### 混流录制

云端录制的一种模式，指房间内所有音视频流、白板混合录制成一个音视频文件。

#### 混响

混响是指通过对声音进行特殊处理，制造不同环境的混响效果，可实现音乐厅、大教堂等场景中的效果。


## I

#### IM

IM（Instant Messaging，即时通讯）是一种通讯方式，允许两人或多人使用网络实时的传递文字消息、文件、语音与视频交流。

## J

#### 鉴权

鉴权（authentication）是指验证用户是否拥有访问系统的权利。

#### 静默推送

ZIM SDK 支持同步处于后台运行的 App 和服务端的数据，同时无需弹出窗口通知用户。仅限支持 Google 服务的 Android 手机实现此功能。

## L

#### 拉流

- L3 拉流：通过 ZEGO 自研超低延迟直播（简称 L3）拉流，基于 UDP 协议实现直播观众端延迟 < 1s ，更能实现超强同步、抗极端弱网、超低卡顿、超清画质、首帧秒开的优质直播体验。
- CDN 拉流：通过传统 CDN 直播拉流，一般使用 RTMP、HLS 等 TCP 协议，直播观众端延迟一般在 3s ～ 10s+。
- RTC 拉流：通过 ZEGO RTC 服务拉流，延迟 < 400ms ，一般用于主播连麦、实时通话场景。

#### 离线推送

当用户离线时，ZIM 消息可通过手机厂商推送通道触达客户端，在用户终端的通知栏内展示推送。

目前 ZIM 已支持 Apple APNs、华为、小米、vivo、OPPO、Google 等主流手机厂商。

#### 离线消息

指用户不在线（离线）时，可以接收到的消息。当 App 在后台运行或者 App 进程被杀死后，服务器会暂存离线消息，当用户再次登录 ZIM 时，会自动拉取离线阶段的消息，实现消息必达。

目前 ZIM 支持单聊/群组的离线消息推送功能。

#### LogUrl

LogUrl 是指集成 ZEGO 的 Web 平台或小程序平台的 SDK 时，用于上报日志的路径，可排查故障、定位问题，在 SDK 中进行配置。

#### 拉流

从 ZEGO 实时音视频云将已有音视频数据流拉取播放的过程。

#### 流

流是指一组按指定编码格式封装，不断发送中的音视频数据。

#### 流量控制

流量控制（流控）是指根据当前网络环境状态来动态调整推流的码率、帧率、分辨率，自动适应当前网络环境及网络波动，保证音视频流畅发布。

#### 连麦

连麦的概念出现在直播房间场景下，有视频连麦和语音连麦两种，是房间内用户之间互动的一种形式。通过 [startPublishingStream](https://doc-zh.zego.im/article/api?doc=express-video-sdk_API~objectivec_ios~class~ZegoExpressEngine#start-publishing-stream) 接口推自己流的同时，也调用 [startPlayingStream](https://doc-zh.zego.im/article/api?doc=express-video-sdk_API~objectivec_ios~class~ZegoExpressEngine#start-publishing-stream) 接口拉对方的流，两个用户连麦成功之后即可进行互动通话。


## M

#### 码率

码率是指每秒传输的比特（bit）数，单位为 bps（bit per second）。


#### MOS 音质评分

Mean Opinion Score（平均意见得分），简称 MOS，是一种最常用、且相对简单的主观音质判断方法，由于是人类给出测评分数，可以灵活应用与测试声音各方面的特性。采取 5 个级别对被测语音的质量进行评价，一般 MOS 在 4 以上被认为是比较好的音质，待测语音的质量是在所有试听人员的评分上求平均得到的。

## N

#### Native

Native 平台，一般指 iOS、Android、macOS、Windows 等平台。

#### NTP

NTP（Network Time Protocol，网络时间协议），是用来使计算机时间同步化的一种协议，它可以使计算机对其服务器或时钟源（如石英钟，GPS 等等)做同步化，并提供高精准度的时间校正（LAN 上与标准间差小于1毫秒，WAN 上几十毫秒），且可通过加密确认的方式来防止恶毒的协议攻击。

## Q

#### QoS

QoS（Quality of Service，服务质量）是一种控制机制，它提供了针对不同用户或者不同数据流采用相应不同的优先级，或者是根据应用程序的要求，保证数据流的性能达到一定的水准。

#### QPS

QPS（并发），指每秒请求的并发数，1 QPS 即每秒同时支持请求接口 1 次，50 QPS 即每秒同时支持请求接口 50 次。

#### 群组

ZIM SDK 的群组，指两个或以上的用户一起进行聊天。有成员在群组内发送消息时，其他成员会收到消息；当 App 在后台运行或者 App 进程被杀死后，不在线的群内成员也可以接收到离线消息的推送通知。

ZIM 会持久化存储群组关系链，不会因为群成员的在线、离线状态而变更关系链。

## R

#### RTC 推流

把采集阶段封包好的音视频数据流推送到 ZEGO 实时音视频云。

#### RTC 拉流

通过 ZEGO RTC 服务拉流，延迟 < 400ms ，一般用于主播连麦、实时通话场景。

#### RTMP

RTMP（Real-Time Messaging Protocol，实时消息协议）也称实时消息传输协议，是最初由 Macromedia 为通过互联网在 Flash 播放器与一个服务器之间传输流媒体音频、视频和数据而开发的一个专有协议。

#### RTP

RTP（Real-time Transmission protocol，实时传输协议）是一个网络传输协议，详细说明了在互联网上传递音频和视频的标准数据包格式，它是创建在 UDP 协议上的。


## S

#### SDK

SDK（Software Development Kit，软件开发工具包）是软件工程师为特定的软件包、软件框架、硬件平台、操作系统等建立应用软件时的开发工具的集合。

ZEGO 提供了 [SDK 中心](https://doc-zh.zego.im/sdk-download/2968)，开发者可以下载不同产品、不同平台或框架的最新 SDK 包。

#### SEI

SEI（Supplemental Enhancement Information，媒体补充增强信息）是通过流媒体通道将文本信息与音视频内容打包在一起，从主播端（推流端）推出，并从观众端（拉流端）接收，以此实现文本数据与音视频内容的精准同步的目的。

<Note title="说明">
SEI 的相关概念及原理请参考 [如何理解和使用 SEI（媒体补充增强信息）？](https://doc-zh.zego.im/faq/sei)。
</Note>

#### ServerSecret

ServerSecret 是 [ZEGO 控制台](https://console.zego.im/) 为每个项目签发的后台服务请求接口的鉴权校验密钥，一般用于 Web 平台上校验对应 AppID 的合法性。

#### 沙箱

沙箱（Sandbox）是一个虚拟系统程序，它创造了一个独立运行的环境，在其内部运行的程序并不会对硬盘产生实际影响，通常用来运行文件样本。

#### 上麦

占据麦位，可以与麦上的其他用户通话，常见于 KTV、语聊房等场景中。

#### 视频编码

视频编码是指连通过消除连续图像之间的时域、空域冗余信息来压缩视频。

#### 数智人

区别于传统的数字人，不仅是以数字形式存在于数字空间中，具有拟人或真人的外貌、行为和特点的虚拟人物，同时借助 AIGC 技术，拥有媲美真人的表情、动作和互动能力。

#### [数智人创作平台](https://aigc.zego.im/)

ZEGO 推出的一款基于人工智能、形象声音克隆、文本驱动、语音驱动、云计算技术的视频生成平台。您可以通过平台提供的多种工具、功能和服务，轻松制作出拥有本人形象和声音的短视频。用生成式技术赋能短视频制作，降低内容生产门槛。

#### 数智人直播

虚拟直播的进阶玩法，商家可以利用数智人，搭建无需真人出镜、无需设备、无需场地的虚拟直播间。

#### SSL

SSL（Secure Socket Layer）安全套接层是 Netscape 公司率先采用的一种网络安全协议。它是在传输通信协议（TCP/IP）上实现的一种安全协议，采用公开密钥技术。SSL 广泛支持各种类型的网络，同时提供三种基本的安全服务，均使用公开密钥技术，为互联网通信提供安全及数据完整性保障。


## T

#### Token

Token 是用于保障房间登录安全的令牌，Token 信息需要开发者自行生成。

#### 推多路流

推多路流是指同时推多条流的技术。

#### 推流

把采集阶段封包好的音视频数据流推送到 ZEGO 实时音视频云的过程。


## W

#### 网络带宽

网络带宽是指在单位时间内能传输的数据量，基本单位为 bps（bit per second）。

上行带宽是指用户电脑向网络发送信息时的数据传输速率，推流时占用上行带宽。

下行带宽是指网络向用户电脑发送信息时的数据传输速率，拉流时占用下行带宽。

## X

#### 下麦

离开麦位，无法与麦上的其他用户通话，常见于 KTV、语聊房等场景中。

#### 消息表态

消息表态，是指用户对消息的反应。一般可用于对单聊或群聊的消息添加或删除表情，也可用于发起群组投票、确认群组结果等操作。ZIM SDK 支持设置消息表态。

#### 消息回执

消息已读回执，是指用户在会话中发送一条消息后，得知其他用户已读或未读此消息。ZIM SDK 支持设置消息回执功能。

#### 消息云存储

用户发送的单聊、群组、房间消息将存储在 ZIM 服务器上，方便更换设备、离线登录后重新获取。

出于隐私保护需求，用户可设置消息实际存储时长，或删除消息存储。

#### 信令消息

开发者可根据业务需求自定义信令消息类型、内容，ZIM 将消息透传至业务服务器，帮助开发者实现相应业务逻辑。

#### [星图](https://console.zego.im/)

星图（Analytics Dashboard，AD）是 ZEGO 为开发者提供的音视频质量运营平台，帮助开发者全面监测音视频服务，还原通话质量和互动情景，低门槛且高效定位排查问题，提升音视频质量和用户体验。

#### 虚拟立体声

虚拟立体声是指通过使用双声道技术，模拟音源的各个位置角度，可实现立体声、3D 环绕音、听声辩位等效果。


## Y

#### 音频采样率

音频采样率是指录音设备在一秒钟内对声音信号的采样次数，采样频率越高声音的还原就越真实越自然。在当今的主流采集卡上，采样频率一般共分为 11025 Hz、22050 Hz、24000 Hz、44100 Hz、48000 Hz 五个等级。

#### 云代理

ZEGO Express SDK 提供的代理云服务，将 SDK 对应的所有流量通过云端的代理服务器中转，适应不同的网络环境下，保障通信正常。

## Z

#### 噪音抑制

噪音抑制（Automatic Noise Supperession，ANS）是指探测背景固定频率的杂音并消除背景噪音。

#### 帧率

帧率（Frame Rate）是单位时间内视频显示帧数的量度单位，测量单位为“每秒显示帧数”（Frame Per Second，FPS）。


#### 直推 CDN

直推 CDN 是指将音视频流直接从本地客户端推送到 CDN 的过程。

#### 主体分割

主体分割是 Express SDK 提供的增值能力，通过 AI 算法识别视频画面中的内容，对每一个像素点设置透明度信息。其中，主体部分的像素点会被设置为“不透明”，主体部分之外的像素点会被设置为“透明”。开发者可以利用这些像素点的透明度信息，对画面中的主体和非主体部分做不同的处理，从而实现不同的功能。

#### 转推 CDN

转推 CDN 是指将音视频流从 ZEGO 实时音视频云推送到 CDN 的过程。


#### 自定义视频采集

自定义视频采集是指开发者向 SDK 提供自定义的视频输入源，SDK 可对源数据进行编码、传输。


#### 自定义视频渲染

自定义视频渲染是指 SDK 向外部提供本地预览及远端拉流的视频帧数据，供开发者自行渲染。


#### 自动增益控制

自动增益控制（Automatic Gain Control，AGC）是指 SDK  能够自动调节麦克风音量，适应远近拾音，保持音量稳定。
