# Integrate the SDK

---


## Prepare the environment

Before you integrate ZEGOCLOUD MiniGameEngine SDK, make sure that the development environment meets the following requirements:

- Android Studio 2021.2.1 or later is installed.
- Android SDK 29 or later, Android SDK Build-Tools 29.0.2 or later, and Android SDK Platform-Tools 29.x.x or later are installed.
- Android 5.0 or later is installed. An Android device that supports audio and video playback is available.
- The Android device is connected to the internet.

## Integrate SDKs

### 1. (Optional) Create a project

<Accordion title="This step describes how to create a project. If you want to integrate SDKs with an existing project, skip this step." defaultOpen="false">

1. Open Android Studio and select **File** > **New** > **New Project**.

    <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Android/ExpressSDK/Integration/android_new_project.png" /></Frame>

2. Specify the project name and storage path.

    <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/MiniGames/empty_activity.jpeg" /></Frame>

3. Use the default settings for other parameters. Click **Next**. Then, click **Finish** to create the project.

</Accordion>

### 2. Import ZEGOCLOUD Express SDK

ZEGOCLOUD MiniGameEngine SDK must be used with **ZEGOCLOUD Express SDK 3.1.1 or later**. If your project is already integrated with ZEGOCLOUD Express SDK of a proper version, skip this step. Otherwise, integrate ZEGOCLOUD Express SDK by referring to the following topic:

- [Get Started with Video Call SDK](https://www.zegocloud.com/docs/live-streaming/quick-start?platform=android&language=java)


### 3. Import ZEGOCLOUD MiniGameEngine SDK

You can integrate the SDK by using one of the following methods:

#### Method 1: Automatic integration

1. Add the URL of the private Maven repository of ZEGOCLOUD to the `repositories` section in the `setting.gradle` or `build.gradle` file.

  ```gradle
  repositories {
      ...
      maven { url "https://maven.zego.im" }
      ...
  }
  ```

2. Open the `app/build.gradle` file and add the following dependency to the `dependencies` section:

  ```
  dependencies {
      ...
      // Replace x.y.z in the following sample code with the version number of ZEGOCLOUD MiniGameEngine SDK. For more information, see [Release notes](https://www.zegocloud.com/docs/mini-game-android/quick-start/release-notes).
      implementation "im.zego:minigameengine:x.y.z"
      ...
  }
  ```

#### Method 2: Manual integration

1. Download the latest SDK and decompress the SDK. For more information, see [Download](../Download.mdx).

2. Open the decompressed folder. Copy the `zegoaminigame.aar` file to the `app/libs` directory of your project.

3. Open the `app/build.gradle` file and add the following dependencies to the `dependencies` section:

  ```groovy
  ...
  dependencies {
      ...
      implementation fileTree(dir:'libs', include: ['.jar', '.aar'])
      ...
      ...
      implementation "com.google.code.gson:gson:2.8.8"
      ...
  }
  ```

### 4. Grant permissions

#### Necessary permissions

Open the `/app/src/main/AndroidManifest.xml` file and grant the following permissions to the SDK:

```xml
<!-- Permissions mandatory for the SDK -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

The following table describes the permissions.

<table>

<tbody><tr>
<th>Required</th>
<th>Permission</th>
<th>Description</th>
<th>Why required</th>
</tr>
<tr>
<td rowspan="3">Yes</td>
<td>INTERNET</td>
<td>Permission required to access the internet.</td>
<td>You can use the basic features of the SDK only after you connect to the internet.</td>
</tr>
<tr>
<td>ACCESS_WIFI_STATE</td>
<td>Permission required to obtain the Wi-Fi status.</td>
<td rowspan="2">The SDK performs different operations when the network status changes. For example, when the system reconnects to the internet, the SDK restores the system to the state it was in when the network was disconnected. You do not need to perform any operations.</td>
</tr>
<tr>
<td>ACCESS_NETWORK_STATE</td>
<td>Permission required to obtain the network status.</td>
</tr>
</tbody></table>

#### Optional permissions

To allow your project to support live streaming and voice chatroom scenarios, grant the following permissions related to camera and microphone to the SDK.

Open the file `app/src/main/AndroidManifest.xml`, and add the following code:

```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
```

### 5. Configure obfuscation rules

ZEGOCLOUD MiniGameEngine SDK adopts code obfuscation. You do not need to add obfuscation rules for integrating ZEGOCLOUD MiniGameEngine SDK.

## What to do next

After you perform the preceding steps, the SDKs are integrated with your project. Then, you can read the following docs to start streaming the live comment-based games:

- [Implement live comment-based games on PCs as a host](./Damaku%20Games%20Quick%20Start/Live%20streaming%20games%20on%20PC.mdx)
- [Implement live comment-based games on mobile devices as a host](./Damaku%20Games%20Quick%20Start/Live%20streaming%20games%20on%20mobile.mdx)

