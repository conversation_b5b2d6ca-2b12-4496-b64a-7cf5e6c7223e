# 集成 SDK

- - -

## 准备环境

在开始集成 ZEGO MiniGameEngine SDK 前，请确保开发环境满足以下要求：
- Android Studio 2021.2.1 或以上版本。
- Android SDK 29、Android SDK Build-Tools 29.0.2、Android SDK Platform-Tools 29.x.x 或以上版本。
- Android 5.0 或以上版本且支持音视频的 Android 设备。
- Android 设备已经连接到 Internet。

## 集成 SDK

### （可选）新建项目

<Accordion title="此步骤以如何创建新项目为例，如果是集成到已有项目，可忽略此步。" defaultOpen="false">

1. 打开 Android Studio，选择 “File > New > New Project” 菜单。

    <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/Android/ExpressSDK/Integration/android_new_project.png" /></Frame>

2. 填写项目名及项目存储路径。

    <Frame width="512" height="auto" caption=""><img src="https://storage.zego.im/sdk-doc/Pics/MiniGames/empty_activity.jpeg" /></Frame>

3. 其它按照默认设置，单击 “Next”，最后单击 “Finish” 完成新工程创建。

</Accordion>


###  导入 ZEGO Express SDK

ZEGO MiniGameEngine SDK 需要与 **ZEGO Express SDK 3.1.1 或以上版本** 搭配使用。如果您的项目已集成相关 SDK，可忽略此步骤，否则，请根据您的项目需求，参考以下任一文完成集成：

- [实时音视频 - 集成 SDK](https://doc-zh.zego.im/article/195)。
- [实时语音 - 集成 SDK](https://doc-zh.zego.im/article/3575)。

###  导入 ZEGO MiniGameEngine SDK

开发者可通过以下任意一种方式实现集成 SDK。

#### 方式一：自动集成

1. 在项目的 “setting.gradle” 或 “build.gradle” 文件中的 ·repositories” 节点加上 ZEGO 的私有 maven 仓库地址。

    ```gradle
    repositories {
        ...
        maven { url "https://maven.zego.im" }
        ...
    }
    ```

2. 打开 “app/build.gradle” 文件，在 `dependencies` 节点下添加以下依赖项：

  以下代码中的 `x.y.z` 应替换为为小游戏平台 SDK 的版本号，请参考 [小游戏平台 - 发布日志](../Overview/release%20notes.mdx)
    ```gradle
    dependencies {
        ...
        implementation "im.zego:minigameengine:x.y.z"
        ...
        implementation "com.tencent.tcr:tcrsdk-full:3.19.1" // 弹幕游戏必须依赖此项
        implementation "com.google.code.gson:gson:2.8.8"
    }
    ```

#### 方式二：手动集成

1. 请参考 [SDK 下载](../download.mdx) 文档，下载最新版本的 SDK 并解压。

2. 打开已解压文件夹，将 zegoaminigame.aar 文件，拷贝到您的项目的 “app/libs” 目录下。

3. 打开 “app/build.gradle” 文件，在 dependencies 节点下添加以下依赖项：

    ```groovy
    ...
    dependencies {
        ...
        implementation fileTree(dir:'libs', include: ['.jar', '.aar'])
        ...
        ...
        implementation "com.tencent.tcr:tcrsdk-full:3.19.1" // 弹幕游戏必须依赖此项
        implementation "com.google.code.gson:gson:2.8.8"
        ...
    }
    ```

###  设置权限

#### 必需权限

打开 “/app/src/main/AndroidManifest.xml” 文件，添加如下权限：

```xml
<!-- SDK 必须使用的权限 -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

具体的权限说明如下：

<table>

<tbody><tr>
<th>必要性</th>
<th>权限</th>
<th>权限说明</th>
<th>申请原因</th>
</tr>
<tr>
<td rowspan="3">必要权限</td>
<td>INTERNET</td>
<td>访问网络权限。</td>
<td>SDK 基本功能都需要在联网的情况下才可以使用。</td>
</tr>
<tr>
<td>ACCESS_WIFI_STATE</td>
<td>获取当前 Wi-Fi 状态权限。</td>
<td rowspan="2">SDK 会根据网络状态的改变执行不同的操作。例如，当网络重连时，SDK 内部会将网络断开时的状态都恢复，用户不需做额外的操作。</td>
</tr>
<tr>
<td>ACCESS_NETWORK_STATE</td>
<td>获取当前网络状态权限。</td>
</tr>
</tbody></table>

#### 可选权限

如果您需要在项目中实现直播、语聊房等场景，请设置摄像头和麦克风相关权限。

打开 “/app/src/main/AndroidManifest.xml” 文件，添加如下权限：

```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
```

具体的权限说明如下：

<table>

<tbody><tr>
<th>必要性</th>
<th>权限</th>
<th>权限说明</th>
<th>申请原因</th>
</tr>
<tr>
<td rowspan="4">非必要权限</td>
<td>CAMERA</td>
<td>访问相机权限。</td>
<td>预览和发送视频时需要使用该权限。</td>
</tr>
<tr>
<td>RECORD_AUDIO</td>
<td>录制音频权限。</td>
<td>发送音频时需要使用该权限。</td>
</tr>
<tr>
<td>BLUETOOTH</td>
<td>连接蓝牙设备权限。</td>
<td>连接蓝牙设备时需要使用该权限。<Warning title="注意"><p>仅 Android 6.0 以下版本需要声明，Android 6.0 及以上版本无需声明。</p></Warning></td>
</tr>
<tr>
<td>MODIFY_AUDIO_SETTINGS</td>
<td>修改音频配置权限。</td>
<td>修改音频设备配置时需要使用该权限。</td>
</tr>
</tbody></table>


### 5 设置混淆规则

ZEGO MiniGameEngine SDK 内部已经做了混淆处理，您的项目无需再针对集成 ZEGO MiniGameEngine SDK 增加混淆规则。


## 下一步


到此为止，您已成功向您的项目集成了相关 SDK。接下来，您可参考下列文档，了解如何开播弹幕游戏：

- [主播在 PC 端开播弹幕游戏](./Damaku%20Games%20Quick%20Start/Live%20streaming%20games%20on%20PC.mdx)。
- [主播在移动端开播弹幕游戏](./Damaku%20Games%20Quick%20Start/Live%20streaming%20games%20on%20mobile.mdx)。
