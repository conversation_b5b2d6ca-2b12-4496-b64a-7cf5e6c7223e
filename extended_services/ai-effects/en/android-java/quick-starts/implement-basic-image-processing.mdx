---
articleID: 10240
---

# Implement basic image processing

---

This document describes how to implement basic image processing with the ZegoEffects SDK.

## Prerequisites

Before implementing the basic image processing functionality, make sure you complete the following steps:

- Integrate the ZegoEffects SDK into your project. For more information, see [Quick starts - Integration](./import-the-sdk.mdx).
- Get the unique license file of the SDK. For details, see [Online authentication](./online-authentication.mdx).
- A project has been created in [ZEGOCLOUD Console](https://console.zegocloud.com) and applied for a valid AppID and AppSign. For details, please refer to [Console - How to view project information](https://docs.zegocloud.com/article/16144) .


## Implementation steps

The following diagram shows the  API call sequence of basic image processing with the ZegoEffects SDK:

<Frame width="auto" height="auto" >
  <img src="https://storage.zego.im/sdk-doc/Pics/AI_Vision/QuickStarts/Implemention_Android_en.png" />
</Frame>


## Create a ZegoEffects object

1. Import the AI resources and models.

     To use the SDK's AI features, you must import the necessary AI resources or models by calling the `setResources` method. For details, see [Quick starts - Import resources and models](./import-resources-and-models.mdx).

    ```java
    // Specify the absolute path of the face recognition model, which is required for various features including Face detection, eyes enlarging, and face slimming.
    ArrayList<String> aiModeInfos = new ArrayList<>();
    aiModeInfos.add("sdcard/xxx/xxxxx/FaceDetectionModel.model");
    aiModeInfos.add("sdcard/xxx/xxxxx/Segmentation.model");

    // Set the list of model paths, which must be called before calling the create method.
    ZegoEffects.setResources(aiModeInfos);
    ```

2. Deploy Advanced Configuration.

    Call the [setAdvancedConfig](@setAdvancedConfig) interface to deploy advanced configuration items, such as configuring device performance levels. For details, please refer to [Configure Device Performance Level](./configure-device-performance-levels.mdx).

    ```java
    ZegoEffectsAdvancedConfig config = new ZegoEffectsAdvancedConfig();
    // Device performance level can be configured
    ZegoEffects.setAdvancedConfig(config);
    ```

3. Create Effects Object.

    Pass the AppID and AppSign obtained from the [Prerequisites](!Quick_Starts/Solution_Implementation#1) directly to the [create](@create__1) interface. After internal authentication by the SDK, it will create an Effects object and return the corresponding [error code](!Error_Codes/Error_Codes).

    ```java
    ZegoEffects mEffects = null;
    long appid = *******;
    String appSign = "*******";
    mEffects = ZegoEffects.create(appid, appSign, applicationContex);
    ```


## Initialize the ZegoEffects object

1. Call the `initEnv` method to initialize the `ZegoEffects` object, passing in the width and height of the original image to be processed.

    ```java
    // Initialize the ZegoEffects object, passing in the width and height of the original image to be processed.
    mEffects.initEnv(1280, 720);
    ```

2. Call the following methods to enable the AI features you want to use.

    - `enableWhiten`
    - `enableBigEyes`
    - `setPortraitSegmentationBackgroundPath`
    - `enablePortraitSegmentation`

    ```java
    // 1. Enable the skin tone enhancement feature.
    // 2. Enable the eyes enlargeing feature.
    // 3. Enable the AI portrait segmentation feature, passing in the absolute path of the segmented background image.
    mEffects.enableWhiten(true)
            .enableBigEyes(true)
            .setPortraitSegmentationBackgroundPath("MY_BACKGROUND_PATH", ZegoEffectsScaleMode.ASPECT_FILL);
            .enablePortraitSegmentation(true);
    ```

## Perform image processing

Call the `processTexture`method to perform image processing.  SDK also supports YUV, Texture, and other formats for image processing.  For details, see the following table:

| Video frame type | Pixel format / Texture ID | Method |
|---|---|---|
|Buff| <ul><li>BGRA32</li><li>RGBA32</li></ul>|`processImageBufferRGB`|
|Buff| <ul><li>NV21</li><li>NV12</li><li>I420</li><li>YV12</li></ul>|`processImageBufferYUV` |
|Texture| Texture ID|`processTexture` |


The following sample code calls the `processTexture`method for image processing:

```java
ZegoEffectsVideoFrameParam zegoEffectsVideoFrameParam = new ZegoEffectsVideoFrameParam();
zegoEffectsVideoFrameParam.setFormat(ZegoEffectsVideoFrameFormat.RGBA32);
zegoEffectsVideoFrameParam.setWidth(width);
zegoEffectsVideoFrameParam.setHeight(height);
// Pass in the textureID of the original video frame to be processed, and return the textureID of the processed video frame.
zegoTextureId = mEffects.processTexture(mTextureId, zegoEffectsVideoFrameParam);

```
