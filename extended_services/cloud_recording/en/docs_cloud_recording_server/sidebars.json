{"mySidebar": [{"type": "doc", "label": "Overview", "id": "overview"}, {"type": "doc", "label": "Accessing server APIs", "id": "accessing-server-apis"}, {"type": "doc", "label": "Start recording", "id": "start-record"}, {"type": "doc", "label": "Stop recording", "id": "stop-record"}, {"type": "doc", "label": "Update the mixed stream layout", "id": "update-layout"}, {"type": "doc", "label": "Update the whiteboard layout", "id": "update-whiteboard"}, {"type": "doc", "label": "Query recording status", "id": "describe-record-status"}, {"type": "doc", "label": "Query the record task list", "id": "describe-tasks"}, {"type": "doc", "label": "Pause recording", "id": "pause-record"}, {"type": "doc", "label": "Resume recording", "id": "resume-record"}, {"type": "doc", "label": "Take a Snapshot", "id": "take-snapshot"}, {"type": "doc", "label": "Recording status callback", "id": "recording-status-callback"}, {"type": "doc", "label": "Return codes", "id": "return-codes"}, {"type": "doc", "label": "API testing with Postman", "id": "debugging-guide-of-server-api-operations"}]}